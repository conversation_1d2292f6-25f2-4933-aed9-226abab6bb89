<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('rotekarten', function (Blueprint $table) {
            $table->id();
            $table->string('status')->default('created');

            // Spektrometer Daten
            $table->json('spektrometer_daten')->nullable();

            // Formanlage Daten
            $table->json('formanlage_daten')->nullable();



            // Timestamps
            $table->timestamps();

            // Indizes für häufig abgefragte Felder
            $table->index('status');
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('rotekarten');
    }
};
