<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('email_verteiler', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // formanlage, spektrometer, gussnachbehandlung, qs
            $table->string('email');
            $table->boolean('is_cc')->default(false);
            $table->timestamps();

            // Index für schnellere Abfragen
            $table->index(['type', 'is_cc']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('email_verteiler');
    }
};
