<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pruefkatalogs', function (Blueprint $table) {
            $table->string('grenzwert_typ')->nullable()->after('absolutwerte');
            $table->string('grenzwert_richtung')->nullable()->after('grenzwert_typ');
            $table->string('grenzwert_operator')->nullable()->after('grenzwert_richtung');
            $table->decimal('grenzwert_wert', 10, 6)->nullable()->after('grenzwert_operator');
            $table->string('grenzwert_einheit')->nullable()->after('grenzwert_wert');
            $table->json('grenzwerte_json')->nullable()->after('grenzwert_einheit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pruefkatalogs', function (Blueprint $table) {
            $table->dropColumn([
                'grenzwert_typ',
                'grenzwert_richtung',
                'grenzwert_operator',
                'grenzwert_wert',
                'grenzwert_einheit',
                'grenzwerte_json'
            ]);
        });
    }
}; 