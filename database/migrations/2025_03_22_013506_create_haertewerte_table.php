<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('haertewerte', function (Blueprint $table) {
            $table->id();
            $table->string('teilenummer');
            $table->string('auftragsnummer');
            $table->string('chargenummer');
            $table->string('nestnummer')->nullable();
            $table->string('eisenmarke')->nullable();
            $table->decimal('soll_von', 8, 2);
            $table->decimal('soll_bis', 8, 2);
            $table->string('geprueft_von');
            $table->decimal('ist_wert', 8, 2);
            $table->string('pruefposition');
            $table->string('pruefverfahren');
            $table->text('bemerkung')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('haertewerte');
    }
};
