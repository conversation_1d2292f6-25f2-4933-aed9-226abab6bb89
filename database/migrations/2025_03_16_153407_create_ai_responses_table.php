<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_responses', function (Blueprint $table) {
            $table->id();
            $table->text('prompt')->comment('Original prompt sent to AI');
            $table->string('prompt_hash', 32)->comment('MD5 hash of prompt for efficient lookup');
            $table->foreignId('rotekarte_id')->nullable()->comment('Referenz zur Rotekarte');
            $table->text('response')->comment('AI response content');
            $table->integer('usage_count')->default(0)->comment('Number of times this response was retrieved');
            $table->timestamps();
            
            // Index für schnelle Lookups
            $table->index(['prompt_hash', 'rotekarte_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_responses');
    }
};
