<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL to modify the column length
        DB::statement('ALTER TABLE haertewerts MODIFY pruefverfahren VARCHAR(100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original size
        DB::statement('ALTER TABLE haertewerts MODIFY pruefverfahren VARCHAR(50)');
    }
};
