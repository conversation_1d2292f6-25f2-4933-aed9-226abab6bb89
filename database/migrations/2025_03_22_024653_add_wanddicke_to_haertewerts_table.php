<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            // Füge die wanddicke-Spalte hinzu, falls sie nicht existiert
            if (!Schema::hasColumn('haertewerts', 'wanddicke')) {
                $table->string('wanddicke')->nullable()->after('bemerkung');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            // Entferne die wanddicke-Spalte, falls sie existiert
            if (Schema::hasColumn('haertewerts', 'wanddicke')) {
                $table->dropColumn('wanddicke');
            }
        });
    }
};
