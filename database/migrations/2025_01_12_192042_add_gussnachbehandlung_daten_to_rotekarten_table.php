<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rotekarten', function (Blueprint $table) {
            $table->json('gussnachbehandlung_daten')->nullable()->after('formanlage_daten');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rotekarten', function (Blueprint $table) {
            $table->dropColumn('gussnachbehandlung_daten');
        });
    }
};
