<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            $table->string('eisenmarke')->nullable()->after('chargenummer');
            // Add index for faster searching
            $table->index('eisenmarke');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            $table->dropColumn('eisenmarke');
            $table->dropIndex(['eisenmarke']);
        });
    }
};
