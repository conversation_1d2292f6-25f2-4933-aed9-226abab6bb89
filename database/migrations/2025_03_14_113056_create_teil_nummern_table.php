<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teil_nummern', function (Blueprint $table) {
            $table->id();
            $table->string('teil_nummer')->unique()->comment('Part number (Teilenummer)');
            $table->decimal('preis', 10, 2)->nullable()->comment('Price in EUR');
            $table->decimal('gewicht', 10, 3)->nullable()->comment('Weight in kg');
            $table->decimal('schrott_preis', 10, 2)->nullable()->comment('Scrap price in EUR');
            $table->timestamps();
            
            // Indexes
            $table->index('teil_nummer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teil_nummern');
    }
};
