<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pruefkatalogs', function (Blueprint $table) {
            $table->id();
            $table->string('fehlercode')->unique();
            $table->string('eisenmarke');
            $table->string('element');
            $table->string('absolutwerte');
            $table->boolean('teilesperren');
            $table->string('massnahmen');
            $table->string('bemerkungen')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pruefkatalogs');
    }
};
