<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('haertewerte', function (Blueprint $table) {
            $table->id();
            $table->string('teilenummer');
            $table->string('auftragsnummer');
            $table->string('chargenummer');
            $table->float('soll_von');
            $table->float('soll_bis');
            $table->float('ist_wert');
            $table->enum('pruefposition', ['UT', 'OT', 'Stehend', 'Zugstrebe', 'Zahnkranz', 'Andere']);
            $table->enum('pruefverfahren', ['Brinellhärte', 'Scherkraft-Härteprüfer', 'Optisches Auslesen'])->default('Brinellhärte');
            $table->string('geprueft_von');
            $table->text('bemerkung')->nullable();
            $table->timestamps();

            // Indizes für schnellere Suche
            $table->index('teilenummer');
            $table->index('auftragsnummer');
            $table->index('chargenummer');
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('haertewerte');
    }
}; 