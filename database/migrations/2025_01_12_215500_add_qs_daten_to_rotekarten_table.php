<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rotekarten', function (Blueprint $table) {
            $table->json('qs_daten')->nullable()->after('gussnachbehandlung_daten');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rotekarten', function (Blueprint $table) {
            $table->dropColumn('qs_daten');
        });
    }
};
