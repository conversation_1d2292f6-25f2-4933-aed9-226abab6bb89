<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            if (!Schema::hasColumn('haertewerts', 'nestnummer')) {
                $table->string('nestnummer')->nullable()->after('chargenummer');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            $table->dropColumn('nestnummer');
        });
    }
};
