<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            // Ändern der pruefposition-Spalte, um längen benutzerdefinierte Positionen zu erlauben
            $table->string('pruefposition', 100)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('haertewerts', function (Blueprint $table) {
            // <PERSON>ur<PERSON> zur ursprünglichen Größe (default: 255 Zeichen)
            $table->string('pruefposition')->change();
        });
    }
};
