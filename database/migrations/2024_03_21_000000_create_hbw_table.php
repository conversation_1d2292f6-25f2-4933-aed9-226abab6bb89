<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('hbw_table', function (Blueprint $table) {
            $table->id();
            $table->string('material_type');
            $table->string('thickness_range');
            $table->integer('min_hbw');
            $table->integer('max_hbw');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('hbw_table');
    }
}; 