<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_responses', function (Blueprint $table) {
            $table->foreignId('analyse_id')->nullable()->after('prompt_hash')
                ->comment('Referenz zur Spektrometer-Analyse');
                
            // Index hinzufügen für bessere Performance
            $table->index(['prompt_hash', 'analyse_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_responses', function (Blueprint $table) {
            $table->dropIndex(['prompt_hash', 'analyse_id']);
            $table->dropColumn('analyse_id');
        });
    }
};
