<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // <PERSON><PERSON><PERSON><PERSON>, ob die Tabelle existiert
        if (Schema::hasTable('ai_responses')) {
            // Prüfen, ob die Spalte existiert
            if (Schema::hasColumn('ai_responses', 'analyse_id')) {
                try {
                    // Versuche, direkt die Spalte umzubenennen
                    Schema::table('ai_responses', function (Blueprint $table) {
                        $table->renameColumn('analyse_id', 'rotekarte_id');
                    });
                    
                    // Kommentar anpassen, wenn möglich
                    try {
                        DB::statement("ALTER TABLE `ai_responses` CHANGE `rotekarte_id` `rotekarte_id` BIGINT UNSIGNED NULL COMMENT 'Referenz zur Rotekarte'");
                    } catch (\Exception $e) {
                        // Fehler beim Ändern des Kommentars ignorieren
                        logger()->info('Kommentar für rotekarte_id konnte nicht geändert werden: ' . $e->getMessage());
                    }
                    
                    logger()->info('Spalte erfolgreicht von analyse_id zu rotekarte_id umbenannt');
                } catch (\Exception $e) {
                    logger()->error('Fehler beim Umbenennen der Spalte: ' . $e->getMessage());
                    throw $e;
                }
            } else if (Schema::hasColumn('ai_responses', 'rotekarte_id')) {
                logger()->info('Spalte rotekarte_id existiert bereits');
            } else {
                // Die Spalte existiert noch nicht, füge sie neu hinzu
                Schema::table('ai_responses', function (Blueprint $table) {
                    $table->foreignId('rotekarte_id')->nullable()->after('prompt_hash')
                        ->comment('Referenz zur Rotekarte');
                });
                logger()->info('Spalte rotekarte_id neu erstellt');
            }
        } else {
            logger()->warning('Tabelle ai_responses existiert nicht');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Prüfen, ob die Tabelle existiert
        if (Schema::hasTable('ai_responses')) {
            // Prüfen, ob die Spalte existiert
            if (Schema::hasColumn('ai_responses', 'rotekarte_id')) {
                try {
                    // Versuche, direkt die Spalte umzubenennen
                    Schema::table('ai_responses', function (Blueprint $table) {
                        $table->renameColumn('rotekarte_id', 'analyse_id');
                    });
                    
                    // Kommentar anpassen, wenn möglich
                    try {
                        DB::statement("ALTER TABLE `ai_responses` CHANGE `analyse_id` `analyse_id` BIGINT UNSIGNED NULL COMMENT 'Referenz zur Spektrometer-Analyse'");
                    } catch (\Exception $e) {
                        // Fehler beim Ändern des Kommentars ignorieren
                        logger()->info('Kommentar für analyse_id konnte nicht geändert werden: ' . $e->getMessage());
                    }
                    
                    logger()->info('Spalte erfolgreicht von rotekarte_id zu analyse_id umbenannt');
                } catch (\Exception $e) {
                    logger()->error('Fehler beim Umbenennen der Spalte: ' . $e->getMessage());
                    throw $e;
                }
            }
        }
    }
};
