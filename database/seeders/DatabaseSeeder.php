<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // User::factory()->create([
        //     'name' => 'Arif <PERSON>han',
        //     'email' => '<EMAIL>',
        //     'password' => Hash::make('12345678'),
        // ]);

        $this->call([
            SollvorgabenTableSeeder::class,
            HaerteWerteTableSeeder::class,
            PruefkatalogSeeder::class,
        ]);

    }
}
