{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "daisyui": "^5.0.3", "laravel-vite-plugin": "^1.0", "nodemon": "^3.1.9", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "vite": "^6.0"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@inertiajs/vue3": "^2.0.5", "@vitejs/plugin-vue": "^5.2.3", "@vueuse/core": "^12.4.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "dompurify": "^3.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-vue-next": "^0.483.0", "marked": "^15.0.7", "moment": "^2.30.1", "radix-vue": "^1.9.17", "sweetalert2": "^11.15.10", "tailwind-merge": "^3.0.2", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-json-pretty": "^2.4.0", "ziggy-js": "^2.4.2"}}