<?php
require 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Pruefkatalog;
use Illuminate\Support\Facades\DB;

// Typen zählen
$types = DB::table('pruefkatalogs')
    ->whereNull('grenzwert_wert')
    ->select('grenzwert_typ', DB::raw('count(*) as total'))
    ->groupBy('grenzwert_typ')
    ->get();

echo "Fehlende Werte nach Typ:\n";
foreach ($types as $type) {
    echo "\n{$type->grenzwert_typ}: {$type->total} Einträge\n";
    
    // Beispiele anzeigen
    $examples = DB::table('pruefkatalogs')
        ->whereNull('grenzwert_wert')
        ->where('grenzwert_typ', $type->grenzwert_typ)
        ->select('fehlercode', 'element', 'absolutwerte')
        ->limit(5)
        ->get();
    
    foreach ($examples as $example) {
        echo "  {$example->fehlercode} | {$example->element} | {$example->absolutwerte}\n";
    }
    echo "  ...\n";
} 