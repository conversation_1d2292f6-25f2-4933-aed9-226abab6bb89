use App\Http\Controllers\SystemInfoController;
use App\Http\Controllers\AiAssistantController;
// use App\Http\Controllers\AiAssistantController; -- wird jetzt in web.php verwendet

Route::get('/system-info', [SystemInfoController::class, 'index']);

// AI Assistant API Endpoints - Kommentiert, da diese jetzt in web.php definiert sind
// Route::post('/ai-assistant', [AiAssistantController::class, 'query']);
// Route::post('/ai-assistant/query', [AiAssistantController::class, 'query']);
// Route::get('/ai-assistant', function() {
//     return response()->json([
//         'message' => 'This endpoint only accepts POST requests with a prompt parameter.',
//         'error' => 'Invalid request method'
//     ], 400);
// });

// AI Assistant API Endpoint -- wird jetzt in web.php verwendet
// Route::post('/ai-assistant', [AiAssistantController::class, 'query']);
