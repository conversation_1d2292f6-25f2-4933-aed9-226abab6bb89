<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FormanlagController;
use App\Http\Controllers\RotekarteController;
use App\Http\Controllers\SpektrometerController;
use App\Http\Controllers\GussnachbehandlungController;
use App\Http\Controllers\QSController;
use App\Http\Controllers\EinstellungenController;
use App\Http\Controllers\ExcelDataController;
use App\Http\Controllers\SystemInfoController;
use App\Http\Controllers\QSDocumentController;
use App\Http\Controllers\SollvorgabenController;
use App\Http\Controllers\PruefkatalogController;
use App\Http\Controllers\TeilNummerController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\AiAssistantController;
use App\Http\Controllers\AiResponseHistoryController;
use App\Http\Controllers\HBWTableController;
use App\Http\Controllers\HaertewertController;
use App\Http\Controllers\StatistikController;
use App\Http\Controllers\EmailVerteilerController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

// Statistik
Route::get('/statistik', [StatistikController::class, 'index'])->name('statistik.index');
Route::get('/statistik/export-pdf', [StatistikController::class, 'exportPdf'])->name('statistik.export-pdf');
Route::post('/statistik/send-email', [StatistikController::class, 'sendEmail'])->name('statistik.send-email');
Route::get('/statistik/email-verteiler', [StatistikController::class, 'getEmailVerteiler'])->name('statistik.email-verteiler');

// E-Mail-Verteiler Verwaltung
Route::resource('email-verteiler', EmailVerteilerController::class)->except(['show', 'create', 'edit']);
Route::get('/offene-karten-druck', [DashboardController::class, 'offeneKartenDruck'])
    ->name('offene-karten-druck');
Route::get('/offene-karten-pdf', [DashboardController::class, 'offeneKartenPdf'])
    ->name('offene-karten-pdf');
Route::get('/in-bearbeitung-karten-druck', [DashboardController::class, 'inBearbeitungKartenDruck'])
    ->name('in-bearbeitung-karten-druck');
Route::get('/in-bearbeitung-karten-pdf', [DashboardController::class, 'inBearbeitungKartenPdf'])
    ->name('in-bearbeitung-karten-pdf');

// Rotekarte routes
Route::get('/rotekarte/{rotekarte}', [RotekarteController::class, 'show'])->name('rotekarte.show');
Route::get('/rotekarte/{rotekarte}/final-pdf', [RotekarteController::class, 'generateFinalPdf'])->name('rotekarte.final-pdf');

// Spektrometer routes
Route::get('/spektrometer', [SpektrometerController::class, 'index'])->name('spektrometer.index');
Route::post('/spektrometer', [SpektrometerController::class, 'store'])->name('spektrometer.store');
Route::get('/spektrometer/sollwerte', [SpektrometerController::class, 'getSollwerte'])->name('spektrometer.sollwerte');
Route::get('/spektrometer/fehlercodes', [SpektrometerController::class, 'getFehlercodes'])->name('spektrometer.fehlercodes');
Route::post('/spektrometer/suggest-fehlercode', [SpektrometerController::class, 'suggestFehlercode'])->name('spektrometer.suggest-fehlercode');
Route::post('/email/send-spektrometer-notification', [SpektrometerController::class, 'sendNotification'])
    ->name('spektrometer.send-notification');

// Formanlage routes
Route::get('/formanlage', [FormanlagController::class, 'index'])->name('formanlage.index');
Route::post('/formanlage', [FormanlagController::class, 'store'])->name('formanlage.store');

// Gussnachbehandlung routes
Route::get('/gussnachbehandlung', [GussnachbehandlungController::class, 'index'])->name('gussnachbehandlung.index');
Route::post('/gussnachbehandlung', [GussnachbehandlungController::class, 'store'])->name('gussnachbehandlung.store');

// QS routes
Route::get('/qs', [QSController::class, 'index'])->name('qs.index');
Route::post('/qs', [QSController::class, 'store'])->name('qs.store');
Route::post('/qs/calculate-scrap-costs', [QSController::class, 'calculateScrapCosts'])->name('qs.calculate-scrap-costs');
Route::post('/qs/save-haertewerte', [QSController::class, 'saveHaertewerte'])->name('qs.save-haertewerte');

// Einstellungen routes
Route::get('/einstellungen', [EinstellungenController::class, 'index'])->name('einstellungen.index');
Route::post('/einstellungen', [EinstellungenController::class, 'store'])->name('einstellungen.store');
Route::post('/einstellungen/excel/import', [EinstellungenController::class, 'importExcel'])->name('einstellungen.excel.import');
Route::match(['post', 'delete'], '/einstellungen/excel/delete', [EinstellungenController::class, 'destroyExcel'])->name('einstellungen.excel.destroy');
Route::delete('/einstellungen/email/{id}', [EinstellungenController::class, 'destroyEmail'])->name('einstellungen.email.destroy');

// System Info routes
Route::get('/system-info', [SystemInfoController::class, 'index'])->name('system.info');

// QS Document routes
Route::post('/qs-documents', [QSDocumentController::class, 'store'])->name('qs.documents.store');
Route::get('/qs-documents', [QSDocumentController::class, 'index'])->name('qs.documents.index');
Route::delete('/qs-documents/{document}', [QSDocumentController::class, 'destroy'])
    ->name('qs.documents.destroy')
    ->middleware('web');

// Sollvorgaben routes
Route::get('/sollvorgaben', [SollvorgabenController::class, 'index'])->name('sollvorgaben.index');
Route::post('/sollvorgaben', [SollvorgabenController::class, 'store'])->name('sollvorgaben.store');
Route::put('/sollvorgaben/{sollvorgabe}', [SollvorgabenController::class, 'update'])->name('sollvorgaben.update');
Route::delete('/sollvorgaben/{sollvorgabe}', [SollvorgabenController::class, 'destroy'])->name('sollvorgaben.destroy');

// Pruefkatalog routes
Route::get('/pruefkatalog', [PruefkatalogController::class, 'index'])->name('pruefkatalog.index');
Route::get('/pruefkatalog/fehlercodes', [PruefkatalogController::class, 'getFehlercodes'])->name('pruefkatalog.fehlercodes');
Route::post('/pruefkatalog', [PruefkatalogController::class, 'store'])->name('pruefkatalog.store');
Route::put('/pruefkatalog/{pruefkatalog}', [PruefkatalogController::class, 'update'])->name('pruefkatalog.update');
Route::delete('/pruefkatalog/{pruefkatalog}', [PruefkatalogController::class, 'destroy'])->name('pruefkatalog.destroy');
Route::get('/pruefkatalog/migration', [PruefkatalogController::class, 'showMigration'])->name('pruefkatalog.migration');
Route::post('/pruefkatalog/migrate-data', [PruefkatalogController::class, 'executeMigration'])->name('pruefkatalog.migrate-data');
Route::get('/pruefkatalog/fix-json', [PruefkatalogController::class, 'showFixJson'])->name('pruefkatalog.show-fix-json');
Route::post('/pruefkatalog/fix-json', [PruefkatalogController::class, 'executeFixJson'])->name('pruefkatalog.fix-json');
Route::post('/pruefkatalog/fix-structured-fields', [PruefkatalogController::class, 'executeFixStructuredFields'])->name('pruefkatalog.fix-structured-fields');
Route::post('/pruefkatalog/reparse-absolutwerte', [PruefkatalogController::class, 'executeReparseAbsolutwerte'])->name('pruefkatalog.reparse-absolutwerte');
Route::post('/pruefkatalog/full-resync', [PruefkatalogController::class, 'executeFullResync'])->name('pruefkatalog.full-resync');

// TeilNummer routes
Route::prefix('teilnummer')->group(function () {
    Route::get('/', [TeilNummerController::class, 'index'])->name('teilnummer.index');
    Route::post('/', [TeilNummerController::class, 'store'])->name('teilnummer.store');
    Route::put('/{teilnummer}', [TeilNummerController::class, 'update'])->name('teilnummer.update');
    Route::delete('/{teilnummer}', [TeilNummerController::class, 'destroy'])->name('teilnummer.destroy');
    Route::post('/import', [TeilNummerController::class, 'importFromRotekarten'])->name('teilnummer.import');
    Route::get('/schrottpreis', [TeilNummerController::class, 'getSchrottpreis'])->name('teilnummer.get-schrottpreis');
    Route::post('/schrottpreis', [TeilNummerController::class, 'updateSchrottpreis'])->name('teilnummer.update-schrottpreis');
});

// Settings routes
Route::get('/settings/{key}', [SettingsController::class, 'get'])->name('settings.get');
Route::post('/settings', [SettingsController::class, 'update'])->name('settings.update');

// AI Assistant Routes
Route::post('/api/ai-assistant/query', [AiAssistantController::class, 'query'])
    ->name('ai-assistant.query')
    ->middleware(['web']);
    
// Add a fallback route for compatibility
Route::post('/api/ai-assistant', [AiAssistantController::class, 'query'])
    ->name('ai-assistant.compat')
    ->middleware(['web']);
    
// Handle GET requests to AI Assistant endpoint
Route::get('/api/ai-assistant', function() {
    return response()->json([
        'message' => 'This endpoint only accepts POST requests with a prompt parameter.',
        'error' => 'Invalid request method',
        'response' => 'Fehler: Diese API akzeptiert nur POST-Anfragen.'
    ], 400);
})
    ->name('ai-assistant.get')
    ->middleware(['web']);

// AI Response History API endpoints
Route::get('/api/ai-responses/history', [AiResponseHistoryController::class, 'index'])
    ->name('ai-responses.history')
    ->middleware(['web']);
    
Route::get('/api/ai-responses/{id}', [AiResponseHistoryController::class, 'show'])
    ->name('ai-responses.show')
    ->middleware(['web']);
    
Route::delete('/api/ai-responses/history/{id}', [AiResponseHistoryController::class, 'destroy'])
    ->name('ai-responses.destroy')
    ->middleware(['web']);

// Fix für falschen Pfad ohne /api/ im Frontend
Route::get('/ai-assistant', function() {
    return Inertia::render('AiAssistant');
})
    ->name('ai-assistant.page')
    ->middleware(['web']);

// Alte Seite mit neuem Namen (nur für Kompatibilität)
Route::get('/ai-assistant-page', function() {
    return Inertia::render('AiAssistant');
})->name('ai-assistant.old-page')->middleware(['web']);

// Neue Härtewerte Tool Routen (behalten)
Route::middleware(['web'])->group(function () {
    Route::get('/haertewerte', [HaertewertController::class, 'index'])->name('haertewerte.index');
    Route::post('/haertewerte', [HaertewertController::class, 'store'])->name('haertewerte.store');
    Route::get('/haertewerte/search', [HaertewertController::class, 'search'])->name('haertewerte.search');
    Route::delete('/haertewerte/{haertewert?}', [HaertewertController::class, 'destroy'])->name('haertewerte.destroy');
    Route::get('/haertewerte/sollwerte-by-eisenmarke', [HaertewertController::class, 'getSollwerteByEisenmarke'])->name('haertewerte.sollwerte-by-eisenmarke');
    Route::post('/haertewerte/kommentar', [HaertewertController::class, 'saveKommentar'])->name('haertewerte.kommentar');
});

// HBW-Table Routen
Route::middleware(['web'])->group(function () {
    Route::get('/hbwtable', [HBWTableController::class, 'index'])->name('hbwtable.index');
    Route::get('/hbwtable/all', [HBWTableController::class, 'getAllData'])->name('hbwtable.all');
    Route::post('/hbwtable', [HBWTableController::class, 'store'])->name('hbwtable.store');
    Route::put('/hbwtable/{hbwTable}', [HBWTableController::class, 'update'])->name('hbwtable.update');
    Route::delete('/hbwtable/{hbwTable}', [HBWTableController::class, 'destroy'])->name('hbwtable.destroy');
});

