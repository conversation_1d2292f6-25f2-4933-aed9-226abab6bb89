# 📊 Intelligente Statistiken - Rotekarten Verwaltung

## Übersicht

Die neue Statistik-Seite bietet umfassende und intelligente Analysen aller Rotekarten-Daten. Sie wurde entwickelt, um Ihnen tiefe Einblicke in Ihre Produktionsprozesse zu geben und Optimierungspotentiale aufzuzeigen.

## Features

### 🎯 Gesamtstatistiken
- **Gesamtanzahl** aller Rotekarten
- **Tagesstatistiken** (heute erstellte Karten)
- **Wochenstatistiken** (aktuelle Woche)
- **Monatsstatistiken** (aktueller Monat)
- **Durchschnitt pro Tag** (basierend auf den letzten 30 Tagen)

### 📈 Interaktive Diagramme
- **Status-Verteilung** (Doughnut-Chart)
  - Offen, In Bearbeitung, Abgeschlossen
- **Abteilungsanalyse** (Bar-Chart)
  - Nassgussanlage (NG), <PERSON><PERSON><PERSON><PERSON><PERSON> (GG), Hochfrequenz (HF)
- **Zeitlicher Verlauf** (Line-Chart)
  - Tägliche Entwicklung über den gewählten Zeitraum
- **Durchlaufzeiten** (Bar-Chart)
  - Top 10 längste Bearbeitungszeiten

### 🔍 Qualitätsmetriken
- **Abschlussrate** in Prozent
- **Verteilung** nach Status
- **Durchschnittliche Bearbeitungszeit** in Stunden

### 📋 Durchlaufzeit-Details mit Paginierung
- **Vollständige Liste** aller abgeschlossenen Rotekarten
- **Paginierung** mit 10 Einträgen pro Seite
- **Sortierung** nach längster Durchlaufzeit
- **Navigation** zwischen den Seiten

### 🔬 Erweiterte Analysen
- **Analyseabweichungen** - Häufigste Elemente mit Toleranzüberschreitungen
- **Eisenmarken-Analyse** - Welche Eisenmarken am meisten betroffen sind
- **Schicht-Analyse** - Verteilung nach Arbeitsschichten (Früh/Spät/Nacht)
- **Mitarbeiter-Analyse** - Wer hat die meisten Rotekarten erstellt
- **Fehlercode-Analyse** - Häufigste Fehlercodes und deren Verteilung

### 🚨 Problembereiche
- Identifikation von Abteilungen mit vielen offenen Problemen
- Priorisierung von Verbesserungsmaßnahmen

### 📅 Flexible Filterung
- **Zeitraum-Filter** (Von/Bis-Datum)
- **Standard**: Letzte 30 Tage
- **Echtzeit-Aktualisierung** bei Filteränderungen

## Zugriff

Die Statistik-Seite ist über den **📊 Statistik-Button** im Header der Anwendung erreichbar (rechts oben, neben dem KI-Assistenten).

## Technische Details

### Backend (Laravel)
- **Controller**: `StatistikController.php`
- **Route**: `/statistik`
- **Datenbankabfragen**: Optimierte SQL-Queries mit JSON-Extraktion
- **Caching**: Effiziente Datenaufbereitung

### Frontend (Vue.js)
- **Komponente**: `resources/js/Pages/Statistik/Index.vue`
- **Charts**: Chart.js für interaktive Diagramme
- **Styling**: Tailwind CSS + DaisyUI
- **Responsive Design**: Mobile-first Ansatz

### Datenquellen
Alle Statistiken basieren auf der `rotekarten` Tabelle:
- `spektrometer_daten` (JSON)
- `formanlage_daten` (JSON)
- `gussnachbehandlung_daten` (JSON)
- `qs_daten` (JSON)
- `created_at`, `updated_at` (Timestamps)
- `status` (String)

## Verwendung

1. **Öffnen**: Klicken Sie auf das 📊 Symbol im Header
2. **Filtern**: Wählen Sie den gewünschten Zeitraum
3. **Analysieren**: Betrachten Sie die verschiedenen Diagramme und Metriken
4. **Optimieren**: Nutzen Sie die Erkenntnisse zur Prozessverbesserung

## Vorteile

### Für das Management
- **Überblick** über alle KPIs auf einen Blick
- **Trends** erkennen und rechtzeitig reagieren
- **Ressourcenplanung** basierend auf Daten

### Für die Produktion
- **Problembereiche** schnell identifizieren
- **Durchlaufzeiten** optimieren
- **Qualitätsverbesserungen** messbar machen

### Für die Qualitätssicherung
- **Abschlussraten** verfolgen
- **Bearbeitungszeiten** analysieren
- **Abteilungsvergleiche** durchführen

## Zukünftige Erweiterungen

- Export-Funktionen (PDF, Excel)
- Erweiterte Filter (nach Abteilung, Status, etc.)
- Vergleichsanalysen (Vormonat, Vorjahr)
- Automatische Berichte
- Benachrichtigungen bei Anomalien

## Support

Bei Fragen oder Problemen wenden Sie sich an das Entwicklungsteam oder nutzen Sie den integrierten KI-Assistenten.
