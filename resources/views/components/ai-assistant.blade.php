@props(['analyseId' => null, 'prompt' => null])

<div id="ai-assistant-container" class="mt-4">
    <ai-assistant 
        :prompt="{{ json_encode($prompt) }}"
        @if($analyseId) :analyse-id="{{ $analyseId }}" @endif
        :auto-fetch="true"
    ></ai-assistant>
</div>

@push('scripts')
<script>
    // Register the component if it hasn't been registered yet
    document.addEventListener('app:mounted', function() {
        if (window.app && !app._context.components['ai-assistant']) {
            // Import and register the component
            import('{{ asset('build/assets/AiAssistant-iBB2XDef.js') }}')
                .then(module => {
                    app.component('ai-assistant', module.default);
                    console.log('AiAssistant component registered');
                })
                .catch(error => {
                    console.error('Failed to load AiAssistant component:', error);
                });
        }
    });
</script>
@endpush 