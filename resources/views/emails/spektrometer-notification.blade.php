@component('mail::message')
# Neue Spektrometer-Analyse

Eine neue Spektrometer-Analyse wurde erstellt.

## Basis Informationen
- **Name:** {{ $data['name'] ?? 'N/A' }}
- **Datum:** {{ $data['datum'] ?? 'N/A' }}
- **Uhrzeit:** {{ $data['uhrzeit'] ?? 'N/A' }}
- **Chargennummer:** {{ $data['chargennummer'] ?? 'N/A' }}
- **Abteilung:** {{ $data['abteilung'] ?? 'N/A' }}
- **Eisenmarke:** {{ $data['eisenmarke'] ?? 'N/A' }}
- **Fehlercode:** {{ $data['fehlercode'] ?? 'N/A' }}

## Analysewerte
@if(!empty($data['analysewerte']))
@component('mail::table')
| Element | Ist-Wert | Soll-Wert |
|:--------|:---------|:----------|
@foreach($data['analysewerte'] as $analyse)
| {{ $analyse['element'] }} | {{ $analyse['istWert'] }} | {{ $analyse['sollWert'] }} |
@endforeach
@endcomponent
@else
Keine Analysewerte vorhanden.
@endif

## Proben
@if(!empty($data['proben']))
@component('mail::table')
| Probennummer | GID Nummer |
|:-------------|:-----------|
@foreach($data['proben'] as $probe)
| {{ $probe['probenummer'] }} | {{ $probe['gidNummer'] }} |
@endforeach
@endcomponent
@else
Keine Proben vorhanden.
@endif

## Bemerkungen
{{ $data['bemerkungen'] ?? 'Keine Bemerkungen vorhanden.' }}

@component('mail::button', ['url' => route('spektrometer.show', $spektrometer->id)])
Zur Spektrometer-Analyse
@endcomponent

Mit freundlichen Grüßen,<br>
{{ config('app.name') }}
@endcomponent
