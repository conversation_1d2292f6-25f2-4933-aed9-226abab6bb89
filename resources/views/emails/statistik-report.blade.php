<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistik-Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #003062 0%, #005aaa 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #003062;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #005aaa;
            margin: 0;
        }
        .section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
        }
        .section h2 {
            color: #003062;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #005aaa;
            padding-bottom: 10px;
        }
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .list-item .name {
            font-weight: 500;
            color: #333;
        }
        .list-item .value {
            font-weight: bold;
            color: #005aaa;
            font-size: 18px;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            color: #6c757d;
            font-size: 14px;
        }
        .highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Rotekarten Statistik-Report</h1>
        <p>Zeitraum: {{ $zeitraum }}</p>
        <p>Erstellt am: {{ now()->format('d.m.Y H:i') }} Uhr</p>
    </div>

    <!-- Gesamtstatistiken -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Gesamt</h3>
            <div class="number">{{ $statistiken['gesamt']['total'] }}</div>
        </div>
        <div class="stat-card">
            <h3>Heute</h3>
            <div class="number">{{ $statistiken['gesamt']['heute'] }}</div>
        </div>
        <div class="stat-card">
            <h3>Diese Woche</h3>
            <div class="number">{{ $statistiken['gesamt']['diese_woche'] }}</div>
        </div>
        <div class="stat-card">
            <h3>Dieser Monat</h3>
            <div class="number">{{ $statistiken['gesamt']['dieser_monat'] }}</div>
        </div>
        <div class="stat-card">
            <h3>Ø pro Tag</h3>
            <div class="number">{{ $statistiken['gesamt']['durchschnitt_pro_tag'] }}</div>
        </div>
    </div>

    <!-- Status-Verteilung -->
    <div class="section">
        <h2>📋 Status-Verteilung</h2>
        @foreach($statistiken['status'] as $status)
        <div class="list-item">
            <span class="name">
                {{ $status['status'] }}
                @if($status['status'] === 'Abgeschlossen')
                    <span class="badge badge-success">✓</span>
                @elseif($status['status'] === 'In Bearbeitung')
                    <span class="badge badge-warning">⏳</span>
                @else
                    <span class="badge badge-danger">⚠️</span>
                @endif
            </span>
            <span class="value">{{ $status['anzahl'] }}</span>
        </div>
        @endforeach
    </div>

    <!-- Abteilungsanalyse -->
    <div class="section">
        <h2>🏭 Abteilungsanalyse</h2>
        @foreach($statistiken['abteilungen'] as $abteilung)
        <div class="list-item">
            <span class="name">{{ $abteilung['abteilung'] }}</span>
            <span class="value">{{ $abteilung['anzahl'] }}</span>
        </div>
        @endforeach
    </div>

    <!-- Qualitätsmetriken -->
    <div class="section">
        <h2>📈 Qualitätsmetriken</h2>
        <div class="highlight">
            <strong>Abschlussrate:</strong> {{ $statistiken['qualitaet']['abschluss_rate'] }}%
        </div>
        <div class="list-item">
            <span class="name">Abgeschlossen</span>
            <span class="value">{{ $statistiken['qualitaet']['abgeschlossen'] }}</span>
        </div>
        <div class="list-item">
            <span class="name">In Bearbeitung</span>
            <span class="value">{{ $statistiken['qualitaet']['in_bearbeitung'] }}</span>
        </div>
        <div class="list-item">
            <span class="name">Offen</span>
            <span class="value">{{ $statistiken['qualitaet']['offen'] }}</span>
        </div>
        <div class="list-item">
            <span class="name">Ø Bearbeitungszeit</span>
            <span class="value">{{ $statistiken['qualitaet']['avg_bearbeitungszeit_stunden'] }}h</span>
        </div>
    </div>

    <!-- Problembereiche -->
    @if(count($statistiken['problembereiche']) > 0)
    <div class="section">
        <h2>🚨 Problembereiche</h2>
        @foreach($statistiken['problembereiche'] as $problem)
        <div class="list-item">
            <span class="name">{{ $problem['bereich'] }}</span>
            <span class="value">{{ $problem['anzahl_probleme'] }}</span>
        </div>
        @endforeach
    </div>
    @endif

    <!-- Erweiterte Analysen -->
    @if(isset($statistiken['analyseabweichungen']) && count($statistiken['analyseabweichungen']) > 0)
    <div class="section">
        <h2>🧪 Top Analyseabweichungen</h2>
        @foreach($statistiken['analyseabweichungen']->take(5) as $abweichung)
        <div class="list-item">
            <span class="name">
                {{ $abweichung['element'] }}
                <span class="badge badge-warning">{{ $abweichung['durchschnitt_abweichung'] }}%</span>
            </span>
            <span class="value">{{ $abweichung['anzahl_abweichungen'] }}</span>
        </div>
        @endforeach
    </div>
    @endif

    @if(isset($statistiken['eisenmarken']) && count($statistiken['eisenmarken']) > 0)
    <div class="section">
        <h2>⚙️ Top Eisenmarken</h2>
        @foreach($statistiken['eisenmarken']->take(5) as $eisenmarke)
        <div class="list-item">
            <span class="name">{{ $eisenmarke['eisenmarke'] }}</span>
            <span class="value">{{ $eisenmarke['anzahl_rotekarten'] }}</span>
        </div>
        @endforeach
    </div>
    @endif

    @if(isset($statistiken['mitarbeiter']) && count($statistiken['mitarbeiter']) > 0)
    <div class="section">
        <h2>👥 Top Mitarbeiter</h2>
        @foreach($statistiken['mitarbeiter']->take(5) as $mitarbeiter)
        <div class="list-item">
            <span class="name">{{ $mitarbeiter['name'] }}</span>
            <span class="value">{{ $mitarbeiter['anzahl_rotekarten'] }}</span>
        </div>
        @endforeach
    </div>
    @endif

    <div class="footer">
        <p>Dieser Report wurde automatisch generiert von der Rotekarten-Verwaltung.</p>
        <p>Bei Fragen wenden Sie sich an das Qualitätsmanagement.</p>
    </div>
</body>
</html>
