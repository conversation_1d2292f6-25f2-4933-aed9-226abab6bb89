<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4F46E5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4F46E5;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 0.9em;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Rotekarte Nr. {{ $rotekarte->id }} - Formanlage abgeschlossen</h1>
    </div>

    <div class="content">
        <p>Die Formanlage hat die Bearbeitung der Rotekarte Nr. {{ $rotekarte->id }} abgeschlossen.</p>

        <h3>Formanlage Daten:</h3>
        <ul>
            <li>Verantwortlicher: {{ $rotekarte->formanlage_daten['verantwortlicher'] }}</li>
            <li>Gießdatum: {{ $rotekarte->formanlage_daten['giessdatum'] }}</li>
            <li>Gießzeit: {{ $rotekarte->formanlage_daten['giesszeit'] }}</li>
            <li>Anzahl Kästen: {{ $rotekarte->formanlage_daten['anzahl_kasten'] }}</li>
            <li>SAP Zahlnummer: {{ $rotekarte->formanlage_daten['sap_zahlnummer'] }}</li>
        </ul>

        <p>Du kannst die Rotekarte über die unten aufgeführten Links bearbeiten:</p>

        <p>
            <a href="{{ $gussnachbehandlungUrl }}" class="button">Zur Gussnachbehandlung</a>
        </p>
        <p>
            <a href="{{ $qsUrl }}" class="button">Zum QS</a>
        </p>

        <p>Die Rotekarte ist als PDF im Anhang beigefügt.</p>
    </div>

    <div class="footer">
        <p>Dies ist eine automatisch generierte E-Mail. Bitte antworten Sie nicht auf diese E-Mail.</p>
    </div>
</body>
</html>
