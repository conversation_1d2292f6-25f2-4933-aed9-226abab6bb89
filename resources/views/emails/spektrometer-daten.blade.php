<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .data-section {
            margin-bottom: 20px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
        }
        .action-button {
            display: inline-block;
            background-color: #4f46e5;
            color: white;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
            font-weight: 500;
        }
        .action-button:hover {
            background-color: #4338ca;
        }
        .action-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Neue Spektrometer Daten zur Analyseabweichung</h2>
            <p>Es wurden neue Spektrometer Daten für die Abteilung {{ $abteilungName = match($abteilung) {
                'NG' => 'Kleinguss',
                'HF' => 'Handformerei',
                'GG' => 'Grossguss',
                default => $abteilung,
            } }} erfasst.</p>
        </div>

        <div class="data-section">
            <h3>Basis Informationen</h3>
            <table class="data-table">
                <tr>
                    <th>Name</th>
                    <td>{{ $spektrometerDaten['name'] }}</td>
                </tr>
                <tr>
                    <th>Datum</th>
                    <td>{{ $spektrometerDaten['datum'] }}</td>
                </tr>
                <tr>
                    <th>Uhrzeit</th>
                    <td>{{ $spektrometerDaten['uhrzeit'] }}</td>
                </tr>
                <tr>
                    <th>Chargennummer</th>
                    <td>{{ $spektrometerDaten['chargennummer'] }}</td>
                </tr>
                <tr>
                    <th>Eisenmarke</th>
                    <td>{{ $spektrometerDaten['eisenmarke'] }}</td>
                </tr>
            </table>
        </div>

        @if(count($spektrometerDaten['proben']) > 0)
        <div class="data-section">
            <h3>Proben</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Probennummer</th>
                        <th>GID-Nummer</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($spektrometerDaten['proben'] as $probe)
                    <tr>
                        <td>{{ $probe['probenummer'] }}</td>
                        <td>{{ $probe['gidNummer'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        @if(count($spektrometerDaten['analysewerte']) > 0)
        <div class="data-section">
            <h3>Analysewerte</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Element</th>
                        <th>Ist-Wert</th>
                        <th>Soll-Wert</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($spektrometerDaten['analysewerte'] as $analyse)
                    <tr>
                        <td>{{ $analyse['element'] }}</td>
                        <td>{{ $analyse['istWert'] }}</td>
                        <td>{{ $analyse['sollWert'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        @if($spektrometerDaten['bemerkungen'])
        <div class="data-section">
            <h3>Bemerkungen</h3>
            <p>{{ $spektrometerDaten['bemerkungen'] }}</p>
        </div>
        @endif

        <div class="action-section">
            <p>Klick einfach auf den Button unten, um die Formanlage-Daten für diese Rotekarte einzugeben:</p>
            <a href="{{ url('/formanlage') }}?rotekarte={{ $rotekarteId }}" class="action-button">
                Formanlage-Daten eingeben
            </a>
        </div>
    </div>
</body>
</html>
