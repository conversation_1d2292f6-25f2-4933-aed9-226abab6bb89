<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Gelbkarte {{ $rotekarte->id }}</title>
    <style>
        @font-face {
            font-family: 'DejaVu Sans';
            src: url('/fonts/DejaVuSans.ttf') format('truetype');
        }
        body {
            font-family: 'DejaVu Sans', sans-serif;
            line-height: 1.4;
            margin: 0;
            padding: 0;
        }
        .page-header {
            background-color: #fbbf24;  /* Gelb */
            color: #92400e;  /* <PERSON><PERSON><PERSON> Gelb für Text */
            padding: 20px;
            margin-bottom: 20px;
        }
        .page-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .page-header h2 {
            margin: 5px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            color: #92400e;  /* <PERSON><PERSON><PERSON> */
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #fbbf24;  /* Gelb */
            padding-bottom: 5px;
        }
        .grid {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        .grid-row {
            display: table-row;
        }
        .grid-cell {
            display: table-cell;
            padding: 5px;
            width: 50%;
        }
        .label {
            font-weight: bold;
            color: #374151;
            font-size: 12px;
        }
        .value {
            color: #1f2937;
            font-size: 12px;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #92400e;  /* Dunkles Gelb */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 5px;
            text-align: left;
            border: 1px solid #fbbf24;  /* Gelb */
            font-size: 12px;
        }
        th {
            background-color: #fef3c7;  /* Helles Gelb */
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1>Analysenabweichung - Hinweis - Gelbe Karte</h1>
        <h2>Q-Leitstand</h2>
        <h2 style="margin-top: 10px; font-size: 16px;">Gelbe Karte RT-{{ $rotekarte->id }}</h2>
    </div>

    <div class="content">
        <!-- Spektrometer Section -->
        <div class="section">
            <div class="section-title">Spektrometer Daten</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Name:</span>
                        <span class="value">{{ $spektrometer['name'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Datum/Uhrzeit:</span>
                        <span class="value">{{ $spektrometer['datum'] ?? '-' }} {{ $spektrometer['uhrzeit'] ?? '' }}</span>
                    </div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Abteilung:</span>
                        <span class="value">{{ $spektrometer['abteilung'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Eisenmarke:</span>
                        <span class="value">{{ $spektrometer['eisenmarke'] ?? '-' }}</span>
                    </div>
                </div>
                @if(!empty($spektrometer['analysewerte']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Analysewerte:</span>
                        <table>
                            <tr>
                                <th>Element</th>
                                <th>Ist-Wert</th>
                                <th>Soll-Wert</th>
                            </tr>
                            @foreach($spektrometer['analysewerte'] as $analyse)
                            <tr>
                                <td>{{ $analyse['element'] }}</td>
                                <td>{{ $analyse['istWert'] }}</td>
                                <td>{!! str_replace('≤', '<span style="font-family: DejaVu Sans;">≤</span>', $analyse['sollWert']) !!}</td>
                            </tr>
                            @endforeach
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Proben Section -->
        @if(!empty($spektrometer['proben']))
        <div class="section">
            <div class="section-title">Proben</div>
            <table>
                <tr>
                    <th>Probennummer</th>
                    <th>GID Nummer</th>
                </tr>
                @foreach($spektrometer['proben'] as $probe)
                <tr>
                    <td>{{ $probe['probenummer'] }}</td>
                    <td>{{ $probe['gidNummer'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
        @endif

        <!-- Hinweis Section -->
        <div class="section">
            <div class="section-title">Hinweis</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="value" style="font-weight: bold; color: #92400e;">
                            Diese Gelbkarte wurde erstellt, da keine Teilesperrung erforderlich ist und keine spezifischen Maßnahmen notwendig sind.
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        Generiert am {{ date('d.m.Y H:i') }} | Gelbkarte #{{ $rotekarte->id }}
    </div>
</body>
</html> 