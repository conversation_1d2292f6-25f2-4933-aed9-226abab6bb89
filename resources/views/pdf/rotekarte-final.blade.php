<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rotekarte {{ $rotekarte->id }}</title>
    <style>
        @font-face {
            font-family: 'DejaVu Sans';
            src: url('/fonts/DejaVuSans.ttf') format('truetype');
        }
        body {
            font-family: 'DejaVu Sans', sans-serif;
            line-height: 1.4;
            margin: 0;
            padding: 0;
        }
        .page-header {
            background-color: #dc2626;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
        }
        .page-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .page-header h2 {
            margin: 5px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            color: #dc2626;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #dc2626;
            padding-bottom: 5px;
        }
        .grid {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        .grid-row {
            display: table-row;
        }
        .grid-cell {
            display: table-cell;
            padding: 5px;
            width: 50%;
        }
        .label {
            font-weight: bold;
            color: #374151;
            font-size: 12px;
        }
        .value {
            color: #1f2937;
            font-size: 12px;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #6b7280;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 5px;
            text-align: left;
            border: 1px solid #e5e7eb;
            font-size: 12px;
        }
        th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="page-header">
        <h1>Analysenabweichung</h1>
        <h2>Q-Leitstand</h2>
        <h2 style="margin-top: 10px; font-size: 16px;">Rotekarte RT-{{ $rotekarte->id }}</h2>
    </div>

    <div class="content">
        <!-- Spektrometer Section -->
        <div class="section">
            <div class="section-title">Spektrometer Daten</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Name:</span>
                        <span class="value">{{ $spektrometer['name'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Datum/Uhrzeit:</span>
                        <span class="value">{{ $spektrometer['datum'] ?? '-' }} {{ $spektrometer['uhrzeit'] ?? '' }}</span>
                    </div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Abteilung:</span>
                        <span class="value">{{ $spektrometer['abteilung'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Eisenmarke:</span>
                        <span class="value">{{ $spektrometer['eisenmarke'] ?? '-' }}</span>
                    </div>
                </div>
                @if(!empty($spektrometer['analysewerte']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Analysewerte:</span>
                        <table>
                            <tr>
                                <th>Element</th>
                                <th>Ist-Wert</th>
                                <th>Soll-Wert</th>
                                <th>Toleranz</th>
                            </tr>
                            @foreach($spektrometer['analysewerte'] as $analyse)
                            <tr>
                                <td>{{ $analyse['element'] }}</td>
                                <td>{{ $analyse['istWert'] }}</td>
                                <td>{!! str_replace('≤', '<span style="font-family: DejaVu Sans;">≤</span>', $analyse['sollWert']) !!}</td>
                                <td style="{{ isset($analyse['toleranz']) && $analyse['toleranz'] < 0 ? 'color: #dc2626;' : '' }}">
                                    {{ number_format($analyse['toleranz'], 3, ',', '.') ?? '-' }}
                                </td>
                            </tr>
                            @endforeach
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Formanlage Section -->
        @if($formanlage)
        <div class="section">
            <div class="section-title">Formanlage Daten</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Verantwortlicher:</span>
                        <span class="value">{{ $formanlage['verantwortlicher'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Datum/Zeit:</span>
                        <span class="value">{{ $formanlage['giessdatum'] ?? '-' }} {{ $formanlage['giesszeit'] ?? '' }}</span>
                    </div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Abteilung:</span>
                        <span class="value">{{ $formanlage['abteilung'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">SAP Zählnummer:</span>
                        <span class="value">{{ $formanlage['sap_zahlnummer'] ?? '-' }}</span>
                    </div>
                </div>
                @if(!empty($formanlage['teile']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Teile:</span>
                        <table>
                            <tr>
                                <th>Teilenummer</th>
                                <th>Anzahl</th>
                            </tr>
                            @foreach($formanlage['teile'] as $teil)
                            <tr>
                                <td>{{ $teil['teilenummer'] }}</td>
                                <td>{{ $teil['anzahl'] }}</td>
                            </tr>
                            @endforeach
                        </table>
                    </div>
                </div>
                @endif
                @if(!empty($formanlage['bemerkungen']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Bemerkungen:</span>
                        <span class="value">{{ $formanlage['bemerkungen'] }}</span>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Gussnachbehandlung Section -->
        @if($gussNachbehandlung)
        <div class="section">
            <div class="section-title">Gussnachbehandlung</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Verantwortlicher:</span>
                        <span class="value">{{ $gussNachbehandlung['verantwortlicher'] ?? '-' }}</span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Datum:</span>
                        <span class="value">{{ $gussNachbehandlung['datum'] ?? '-' }}</span>
                    </div>
                </div>
                @if(!empty($gussNachbehandlung['bemerkungen']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Bemerkungen:</span>
                        <span class="value">{{ $gussNachbehandlung['bemerkungen'] }}</span>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- QS Section -->
        @if($qs)
        <div class="section">
            <div class="section-title">Qualitätssicherung</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Prüfer:</span>
                        <span class="value">
                            @if(!empty($qs['pruefer']))
                                @foreach($qs['pruefer'] as $pruefer)
                                    {{ $pruefer['name'] }}@if(!$loop->last), @endif
                                @endforeach
                            @endif
                        </span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">Datum:</span>
                        <span class="value">
                            @if(!empty($qs['pruefer']))
                                {{ $qs['pruefer'][0]['datum'] ?? '-' }}
                            @endif
                        </span>
                    </div>
                </div>

                <!-- Abnahmebeauftragter Decision Section -->
                <div class="grid-row">
                    <div class="grid-cell">
                        <span class="label">Entscheidung Abnahmebeauftragter:</span>
                        <span class="value" style="font-weight: bold; color: {{ $qs['serie_status'] === 'Teile in die Serie einreihen' ? '#059669' : '#DC2626' }}">
                            {{ $qs['serie_status'] }}
                        </span>
                    </div>
                    <div class="grid-cell">
                        <span class="label">QS Status:</span>
                        <span class="value">{{ $qs['status'] }}</span>
                    </div>
                </div>

                @if(!empty($qs['teile']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Geprüfte Teile:</span>
                        <table>
                            <tr>
                                <th>Teilenummer</th>
                                <th>Anzahl</th>
                                <th>Geprüfte Teile</th>
                                <th>IO/NIO</th>
                            </tr>
                            @foreach($qs['teile'] as $teil)
                            <tr>
                                <td>{{ $teil['teilenummer'] }}</td>
                                <td>{{ $teil['anzahl'] }}</td>
                                <td>{{ $teil['gepruefteTeile'] }}</td>
                                <td>{{ $teil['io_nio'] }}</td>
                            </tr>
                            @endforeach
                        </table>
                    </div>
                </div>
                @endif

                @if(!empty($qs['bemerkungen']))
                <div class="grid-row">
                    <div class="grid-cell" colspan="2">
                        <span class="label">Bemerkungen:</span>
                        <div class="value" style="margin-top: 5px;">
                            @foreach($qs['bemerkungen'] as $bemerkung)
                                <div style="margin-bottom: 5px;">
                                    <strong>{{ $bemerkung['pruefer'] }}</strong> ({{ $bemerkung['timestamp'] }}):
                                    <br>{{ $bemerkung['text'] }}
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>

    <div class="footer">
        Generiert am {{ date('d.m.Y H:i') }} | Rotekarte #{{ $rotekarte->id }}
    </div>
</body>
</html> 