<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistik-Report</title>
    <style>
        @page {
            margin: 20mm;
            size: A4;
        }
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            background: #003062;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .header h1 {
            margin: 0;
            font-size: 24pt;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0 0 0;
            font-size: 12pt;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .stats-row {
            display: table-row;
        }
        .stat-card {
            display: table-cell;
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
            width: 20%;
            vertical-align: top;
        }
        .stat-card h3 {
            margin: 0 0 8px 0;
            color: #003062;
            font-size: 9pt;
            text-transform: uppercase;
            font-weight: bold;
        }
        .stat-card .number {
            font-size: 20pt;
            font-weight: bold;
            color: #005aaa;
            margin: 0;
        }
        .section {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #003062;
            margin: 0 0 15px 0;
            font-size: 14pt;
            font-weight: bold;
            border-bottom: 2px solid #005aaa;
            padding-bottom: 5px;
        }
        .list-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .list-table th {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            color: #003062;
        }
        .list-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .list-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
        .badge {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8pt;
            font-weight: bold;
        }
        .badge-success { background: #28a745; }
        .badge-warning { background: #ffc107; color: #212529; }
        .badge-danger { background: #dc3545; }
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #003062;
            color: white;
            text-align: center;
            padding: 10px;
            font-size: 8pt;
        }
        .page-break {
            page-break-before: always;
        }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .two-column {
            display: table;
            width: 100%;
        }
        .column {
            display: table-cell;
            width: 48%;
            vertical-align: top;
            padding-right: 2%;
        }
        .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #ddd;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Rotekarten Statistik-Report</h1>
        <p>Zeitraum: {{ $filter['start_date'] }} bis {{ $filter['end_date'] }}</p>
        <p>Erstellt am: {{ now()->format('d.m.Y H:i') }} Uhr</p>
    </div>

    <!-- Gesamtstatistiken -->
    <div class="stats-grid">
        <div class="stats-row">
            <div class="stat-card">
                <h3>Gesamt</h3>
                <div class="number">{{ $statistiken['gesamt']['total'] }}</div>
            </div>
            <div class="stat-card">
                <h3>Heute</h3>
                <div class="number">{{ $statistiken['gesamt']['heute'] }}</div>
            </div>
            <div class="stat-card">
                <h3>Diese Woche</h3>
                <div class="number">{{ $statistiken['gesamt']['diese_woche'] }}</div>
            </div>
            <div class="stat-card">
                <h3>Dieser Monat</h3>
                <div class="number">{{ $statistiken['gesamt']['dieser_monat'] }}</div>
            </div>
            <div class="stat-card">
                <h3>Ø pro Tag</h3>
                <div class="number">{{ $statistiken['gesamt']['durchschnitt_pro_tag'] }}</div>
            </div>
        </div>
    </div>

    <div class="two-column">
        <div class="column">
            <!-- Status-Verteilung -->
            <div class="section">
                <h2>📋 Status-Verteilung</h2>
                <table class="list-table">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th class="text-right">Anzahl</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($statistiken['status'] as $status)
                        <tr>
                            <td>
                                {{ $status['status'] }}
                                @if($status['status'] === 'Abgeschlossen')
                                    <span class="badge badge-success">✓</span>
                                @elseif($status['status'] === 'In Bearbeitung')
                                    <span class="badge badge-warning">⏳</span>
                                @else
                                    <span class="badge badge-danger">⚠️</span>
                                @endif
                            </td>
                            <td class="text-right font-bold">{{ $status['anzahl'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <div class="column">
            <!-- Abteilungsanalyse -->
            <div class="section">
                <h2>🏭 Abteilungsanalyse</h2>
                <table class="list-table">
                    <thead>
                        <tr>
                            <th>Abteilung</th>
                            <th class="text-right">Anzahl</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($statistiken['abteilungen'] as $abteilung)
                        <tr>
                            <td>{{ $abteilung['abteilung'] }}</td>
                            <td class="text-right font-bold">{{ $abteilung['anzahl'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Qualitätsmetriken -->
    <div class="section">
        <h2>📈 Qualitätsmetriken</h2>
        <div class="highlight">
            <strong>Abschlussrate: {{ $statistiken['qualitaet']['abschluss_rate'] }}%</strong>
        </div>
        <table class="list-table">
            <thead>
                <tr>
                    <th>Metrik</th>
                    <th class="text-right">Wert</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Abgeschlossen</td>
                    <td class="text-right font-bold">{{ $statistiken['qualitaet']['abgeschlossen'] }}</td>
                </tr>
                <tr>
                    <td>In Bearbeitung</td>
                    <td class="text-right font-bold">{{ $statistiken['qualitaet']['in_bearbeitung'] }}</td>
                </tr>
                <tr>
                    <td>Offen</td>
                    <td class="text-right font-bold">{{ $statistiken['qualitaet']['offen'] }}</td>
                </tr>
                <tr>
                    <td>Ø Bearbeitungszeit</td>
                    <td class="text-right font-bold">{{ $statistiken['qualitaet']['avg_bearbeitungszeit_stunden'] }}h</td>
                </tr>
            </tbody>
        </table>
    </div>

    @if(count($statistiken['problembereiche']) > 0)
    <!-- Problembereiche -->
    <div class="section">
        <h2>🚨 Problembereiche</h2>
        <table class="list-table">
            <thead>
                <tr>
                    <th>Bereich</th>
                    <th class="text-right">Anzahl Probleme</th>
                </tr>
            </thead>
            <tbody>
                @foreach($statistiken['problembereiche'] as $problem)
                <tr>
                    <td>{{ $problem['bereich'] }}</td>
                    <td class="text-right font-bold">{{ $problem['anzahl_probleme'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="page-break"></div>

    <!-- Erweiterte Analysen -->
    <h1 style="color: #003062; text-align: center; margin-bottom: 20px;">🔬 Erweiterte Analysen</h1>

    @if(isset($statistiken['analyseabweichungen']) && count($statistiken['analyseabweichungen']) > 0)
    <div class="section">
        <h2>🧪 Analyseabweichungen</h2>
        <table class="list-table">
            <thead>
                <tr>
                    <th>Element</th>
                    <th class="text-right">Anzahl</th>
                    <th class="text-right">Ø Abweichung</th>
                    <th class="text-right">Max Abweichung</th>
                </tr>
            </thead>
            <tbody>
                @foreach($statistiken['analyseabweichungen']->take(10) as $abweichung)
                <tr>
                    <td>{{ $abweichung['element'] }}</td>
                    <td class="text-right font-bold">{{ $abweichung['anzahl_abweichungen'] }}</td>
                    <td class="text-right">{{ $abweichung['durchschnitt_abweichung'] }}%</td>
                    <td class="text-right">{{ number_format($abweichung['max_abweichung'], 1) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="two-column">
        <div class="column">
            @if(isset($statistiken['eisenmarken']) && count($statistiken['eisenmarken']) > 0)
            <div class="section">
                <h2>⚙️ Top Eisenmarken</h2>
                <table class="list-table">
                    <thead>
                        <tr>
                            <th>Eisenmarke</th>
                            <th class="text-right">Anzahl</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($statistiken['eisenmarken']->take(10) as $eisenmarke)
                        <tr>
                            <td>{{ $eisenmarke['eisenmarke'] }}</td>
                            <td class="text-right font-bold">{{ $eisenmarke['anzahl_rotekarten'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif
        </div>

        <div class="column">
            @if(isset($statistiken['schichten']) && count($statistiken['schichten']) > 0)
            <div class="section">
                <h2>🕐 Schicht-Analyse</h2>
                <table class="list-table">
                    <thead>
                        <tr>
                            <th>Schicht</th>
                            <th class="text-right">Anzahl</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($statistiken['schichten'] as $schicht)
                        <tr>
                            <td>{{ str_replace(['(', ')'], '', explode(' ', $schicht['schicht'])[0]) }}</td>
                            <td class="text-right font-bold">{{ $schicht['anzahl_rotekarten'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif
        </div>
    </div>

    @if(isset($statistiken['mitarbeiter']) && count($statistiken['mitarbeiter']) > 0)
    <div class="section">
        <h2>👥 Mitarbeiter-Analyse</h2>
        <table class="list-table">
            <thead>
                <tr>
                    <th>Mitarbeiter</th>
                    <th class="text-right">Anzahl Rotekarten</th>
                    <th>Letzte Aktivität</th>
                </tr>
            </thead>
            <tbody>
                @foreach($statistiken['mitarbeiter']->take(15) as $mitarbeiter)
                <tr>
                    <td>{{ $mitarbeiter['name'] }}</td>
                    <td class="text-right font-bold">{{ $mitarbeiter['anzahl_rotekarten'] }}</td>
                    <td>{{ $mitarbeiter['letzte_aktivitaet'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($statistiken['fehlercodes']) && count($statistiken['fehlercodes']) > 0)
    <div class="section">
        <h2>⚠️ Häufigste Fehlercodes</h2>
        <table class="list-table">
            <thead>
                <tr>
                    <th>Fehlercode</th>
                    <th class="text-right">Anzahl</th>
                    <th>Letzte Rotekarte</th>
                </tr>
            </thead>
            <tbody>
                @foreach($statistiken['fehlercodes']->take(15) as $fehlercode)
                <tr>
                    <td>{{ $fehlercode['fehlercode'] }}</td>
                    <td class="text-right font-bold">{{ $fehlercode['anzahl_rotekarten'] }}</td>
                    <td>{{ $fehlercode['letzte_rotekarte'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>Rotekarten-Verwaltung | Automatisch generierter Report | Seite <span class="pagenum"></span></p>
    </div>
</body>
</html>
