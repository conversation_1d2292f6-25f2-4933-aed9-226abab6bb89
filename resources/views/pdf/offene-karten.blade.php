<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offene Rotekarten</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #333;
            margin-bottom: 20px;
            position: relative;
        }
        .logo {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 60px;
            height: auto;
        }
        .title {
            font-size: 22pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .subtitle {
            font-size: 12pt;
            color: #666;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
            padding: 0 15px;
        }
        .stat-value {
            font-size: 18pt;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            font-size: 10pt;
            color: #666;
        }
        .karte {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            page-break-inside: avoid;
        }
        .karte-header {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .karte-id {
            font-size: 14pt;
            font-weight: bold;
        }
        .abteilung {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 9pt;
            font-weight: bold;
        }
        .abteilung-ng {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .abteilung-gg {
            background-color: #dcfce7;
            color: #166534;
        }
        .abteilung-hf {
            background-color: #f3e8ff;
            color: #6b21a8;
        }
        .section {
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .fields {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .field {
            flex: 1;
            min-width: calc(33% - 10px);
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 3px;
        }
        .field-label {
            font-size: 8pt;
            color: #666;
            text-transform: uppercase;
            display: block;
            margin-bottom: 3px;
        }
        .field-value {
            font-size: 10pt;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 10pt;
        }
        th {
            background-color: #f5f5f5;
            text-align: left;
            padding: 8px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
            color: #333;
            text-transform: uppercase;
            font-size: 9pt;
        }
        td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .footer {
            text-align: center;
            font-size: 9pt;
            color: #666;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
        .tolerance-green {
            color: #16a34a;
        }
        .tolerance-yellow {
            color: #ca8a04;
        }
        .tolerance-red {
            color: #dc2626;
        }
        .row-green {
            background-color: #f0fdf4;
        }
        .row-yellow {
            background-color: #fefce8;
        }
        .row-red {
            background-color: #fef2f2;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{{ public_path('images/logo.png') }}" alt="Logo" class="logo">
        <div class="title">Übersicht Offene Rotekarten</div>
        <div class="subtitle">Stand: {{ $datum }} {{ $zeit }} Uhr</div>
    </div>

    <div class="stats">
        <div class="stat-item">
            <div class="stat-value">{{ count($offeneKarten) }}</div>
            <div class="stat-label">Offene Karten</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $offeneKarten->where('spektrometer_daten.abteilung', 'NG')->count() }}</div>
            <div class="stat-label">Nassgussanlage</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $offeneKarten->where('spektrometer_daten.abteilung', 'GG')->count() }}</div>
            <div class="stat-label">Großgussanlage</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $offeneKarten->where('spektrometer_daten.abteilung', 'HF')->count() }}</div>
            <div class="stat-label">Handformerei</div>
        </div>
    </div>

    @foreach($offeneKarten as $karte)
        <div class="karte">
            <div class="karte-header">
                <div>
                    <span class="karte-id">Rotekarte #{{ $karte->id }}</span>
                    @php
                        $abteilung = $karte->spektrometer_daten['abteilung'] ?? '';
                        $abteilungClass = '';
                        $abteilungText = '';
                        
                        if ($abteilung === 'NG') {
                            $abteilungClass = 'abteilung-ng';
                            $abteilungText = 'Nassgussanlage';
                        } elseif ($abteilung === 'GG') {
                            $abteilungClass = 'abteilung-gg';
                            $abteilungText = 'Großgussanlage';
                        } elseif ($abteilung === 'HF') {
                            $abteilungClass = 'abteilung-hf';
                            $abteilungText = 'Handformerei';
                        } else {
                            $abteilungText = $abteilung;
                        }
                    @endphp
                    <span class="abteilung {{ $abteilungClass }}">{{ $abteilungText }}</span>
                </div>
                <div>
                    <span style="font-size: 10pt;">Erstellt am: {{ \Carbon\Carbon::parse($karte->created_at)->format('d.m.Y') }}</span>
                </div>
            </div>

            @if(isset($karte->spektrometer_daten))
                <div class="section">
                    <div class="section-title">Spektrometer Daten</div>
                    <div class="fields">
                        <div class="field">
                            <span class="field-label">Name</span>
                            <span class="field-value">{{ $karte->spektrometer_daten['name'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Chargennummer</span>
                            <span class="field-value">{{ $karte->spektrometer_daten['chargennummer'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Eisenmarke</span>
                            <span class="field-value">{{ $karte->spektrometer_daten['eisenmarke'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Datum/Zeit</span>
                            <span class="field-value">
                                {{ $karte->spektrometer_daten['datum'] ?? '-' }}
                                {{ $karte->spektrometer_daten['uhrzeit'] ?? '' }}
                            </span>
                        </div>
                        @if(isset($karte->spektrometer_daten['fehlercode']))
                            <div class="field">
                                <span class="field-label">Fehlercode</span>
                                <span class="field-value">{{ $karte->spektrometer_daten['fehlercode'] }}</span>
                            </div>
                        @endif
                    </div>

                    @if(isset($karte->spektrometer_daten['analysewerte']) && count($karte->spektrometer_daten['analysewerte']) > 0)
                        <div style="margin-top: 10px;">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Element</th>
                                        <th>Ist-Wert</th>
                                        <th>Soll-Wert</th>
                                        <th>Toleranz</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($karte->spektrometer_daten['analysewerte'] as $analyse)
                                        @php
                                            $toleranzClass = '';
                                            $rowClass = '';
                                            if (isset($analyse['toleranz'])) {
                                                $toleranz = abs(floatval($analyse['toleranz']));
                                                if ($toleranz > 0.05) {
                                                    $toleranzClass = 'tolerance-red';
                                                    $rowClass = 'row-red';
                                                } elseif ($toleranz > 0.03) {
                                                    $toleranzClass = 'tolerance-yellow';
                                                    $rowClass = 'row-yellow';
                                                } else {
                                                    $toleranzClass = 'tolerance-green';
                                                    $rowClass = 'row-green';
                                                }
                                            }
                                        @endphp
                                        <tr class="{{ $rowClass }}">
                                            <td><strong>{{ $analyse['element'] }}</strong></td>
                                            <td>{{ $analyse['istWert'] }}</td>
                                            <td>{{ $analyse['sollWert'] }}</td>
                                            <td class="{{ $toleranzClass }}">{{ $analyse['toleranz'] ?? '-' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            @endif

            @if(isset($karte->formanlage_daten))
                <div class="section">
                    <div class="section-title">Formanlage Daten</div>
                    <div class="fields">
                        <div class="field">
                            <span class="field-label">Verantwortlicher</span>
                            <span class="field-value">{{ $karte->formanlage_daten['verantwortlicher'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Gießdatum</span>
                            <span class="field-value">{{ $karte->formanlage_daten['giessdatum'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Gießzeit</span>
                            <span class="field-value">{{ $karte->formanlage_daten['giesszeit'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">SAP Zählnummer</span>
                            <span class="field-value">{{ $karte->formanlage_daten['sap_zahlnummer'] ?? '-' }}</span>
                        </div>
                        <div class="field">
                            <span class="field-label">Anzahl Kästen</span>
                            <span class="field-value">{{ $karte->formanlage_daten['anzahl_kasten'] ?? '-' }}</span>
                        </div>
                    </div>

                    @if(isset($karte->formanlage_daten['teile']) && count($karte->formanlage_daten['teile']) > 0)
                        <div style="margin-top: 10px;">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Teilenummer</th>
                                        <th>Anzahl</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($karte->formanlage_daten['teile'] as $teil)
                                        <tr>
                                            <td><strong>{{ $teil['teilenummer'] }}</strong></td>
                                            <td>{{ $teil['anzahl'] }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        @if(!$loop->last && $loop->iteration % 2 == 0)
            <div class="page-break"></div>
        @endif
    @endforeach

    <div class="footer">
        <p>Dieses Dokument wurde automatisch generiert am {{ $datum }} um {{ $zeit }} Uhr.</p>
        <p>© {{ date('Y') }} - Rotekarten-Verwaltungssystem</p>
    </div>
</body>
</html> 