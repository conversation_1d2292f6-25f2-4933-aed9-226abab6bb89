<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysenabweichung</title>
    <style>
        @font-face {
            font-family: 'DejaVu Sans';
            src: url('/fonts/DejaVuSans.ttf') format('truetype');
        }
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #dc2626;
            color: white;
            padding: 10px;
            margin-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header h2 {
            margin: 3px 0 0;
            font-size: 14px;
            font-weight: normal;
        }
        .content {
            padding: 0 10px;
        }
        .section {
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #dc2626;
            border-bottom: 1px solid #dc2626;
            margin-bottom: 8px;
            padding-bottom: 3px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .label {
            font-weight: bold;
            color: #666;
        }
        .value {
            margin-left: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        th {
            background-color: #f3f4f6;
            text-align: left;
            padding: 5px;
            font-size: 10px;
            font-weight: bold;
            color: #374151;
        }
        td {
            padding: 5px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 10px;
        }
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            padding: 5px 10px;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Analysenabweichung</h1>
        <h2>Q-Leitstand</h2>
        <h2 style="margin-top: 10px; font-size: 16px;">Rotekarte RT-{{ $rotekarte->id }}</h2>
    </div>

    <div class="content">
        <!-- Spektrometer Daten -->
        <div class="section">
            <div class="section-title">Spektrometer Daten</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Name:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['name'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Datum:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['datum'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Uhrzeit:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['uhrzeit'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Chargennummer:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['chargennummer'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Abteilung:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['abteilung'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Eisenmarke:</span>
                    <span class="value">{{ $rotekarte->spektrometer_daten['eisenmarke'] ?? '-' }}</span>
                </div>
            </div>

            <!-- Proben -->
            @if(!empty($rotekarte->spektrometer_daten['proben']))
            <table>
                <thead>
                    <tr>
                        <th>Probennummer</th>
                        <th>GID Nummer</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($rotekarte->spektrometer_daten['proben'] as $probe)
                    <tr>
                        <td>{{ $probe['probenummer'] }}</td>
                        <td>{{ $probe['gidNummer'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif

            <!-- Analysewerte -->
            @if(!empty($rotekarte->spektrometer_daten['analysewerte']))
            <table>
                <thead>
                    <tr>
                        <th>Element</th>
                        <th>Ist-Wert</th>
                        <th>Soll-Wert</th>
                        <th>Toleranz</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($rotekarte->spektrometer_daten['analysewerte'] as $analyse)
                    <tr>
                        <td>{{ $analyse['element'] }}</td>
                        <td>{{ $analyse['istWert'] }}</td>
                        <td>{!! str_replace('≤', '<span style="font-family: DejaVu Sans;">≤</span>', $analyse['sollWert']) !!}</td>
                        <td style="{{ isset($analyse['toleranz']) && $analyse['toleranz'] < 0 ? 'color: #dc2626;' : '' }}">
                            {{ number_format($analyse['toleranz'], 3, ',', '.') ?? '-' }}
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>

        <!-- Formanlage Daten -->
        <div class="section">
            <div class="section-title">Formanlage Daten</div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Verantwortlicher:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['verantwortlicher'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Gießdatum:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['giessdatum'] ?? '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Gießzeit:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['giesszeit'] ?? '-' }}</span>
                </div>
            </div>

            <!-- Checkboxen Status -->
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Formen gekennzeichnet:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['formen_gekennzeichnet'] ? 'Ja' : 'Nein' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Formen an Anlage:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['formen_an_anlage'] ? 'Ja' : 'Nein' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Ausschussseparation:</span>
                    <span class="value">{{ $rotekarte->formanlage_daten['ausschussseparation'] ? 'Ja' : 'Nein' }}</span>
                </div>
            </div>

            <!-- Teile -->
            @if(!empty($rotekarte->formanlage_daten['teile']))
            <table>
                <thead>
                    <tr>
                        <th>Teilenummer</th>
                        <th>Anzahl</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($rotekarte->formanlage_daten['teile'] as $teil)
                    <tr>
                        <td>{{ $teil['teilenummer'] }}</td>
                        <td>{{ $teil['anzahl'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
    </div>

    <div class="footer">
        Erstellt am: {{ now()->format('d.m.Y H:i') }} | Rotekarte Nr. {{ $rotekarte->id }}
    </div>
</body>
</html>
