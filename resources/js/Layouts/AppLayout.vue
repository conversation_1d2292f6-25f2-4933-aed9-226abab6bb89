<script setup>
import { ref, computed } from 'vue';
import { Link, usePage } from '@inertiajs/vue3';

const page = usePage();
const currentPath = computed(() => page.url);

const navigationTabs = [
    { name: 'E-Mail-Verteiler', route: 'email-verteiler' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', route: 'haertewerte' }
];

const isCurrentRoute = (route) => {
    return currentPath.value === '/' + route;
};
</script>

<template>
    <div class="min-h-screen bg-gray-100">
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                <div class="flex items-center space-x-6">
                    <Link
                        href="/"
                        class="flex items-center gap-3 text-2xl font-bold text-gray-900 hover:text-gray-600 transition-colors duration-200"
                    >
                        <img src="/images/logo.png" alt="Logo" class="h-8 w-8" />
                        Rotekarten Verwaltung
                    </Link>
                </div>

                <!-- Settings Dropdown -->
                <div class="ml-3 relative">
                    <!-- Sollvorgaben Icon -->
                    <Link
                        :href="route('sollvorgaben.index')"
                        class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('sollvorgaben.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                    </Link>

                    <!-- Pruefkatalog Icon -->
                    <Link
                        :href="route('pruefkatalog.index')"
                        class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out ml-4"
                        :class="[
                            route().current('pruefkatalog.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </Link>

                    <!-- Teilenummer Icon -->
                    <Link
                        :href="route('teilnummer.index')"
                        class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out ml-4"
                        :class="[
                            route().current('teilnummer.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                        title="Teilenummer Verwaltung"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                    </Link>

                    <!-- Settings Icon -->
                    <Link
                        :href="route('einstellungen.index')"
                        class="ml-3 inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('einstellungen.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                    >
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </Link>

                    <!-- Härtewerte Icon -->
                    <Link
                        :href="route('haertewerte.index')"
                        class="ml-3 inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('haertewerte.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                        title="Härtewerte"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </Link>

                    <!-- HBW-Table Icon -->
                    <Link
                        :href="route('hbwtable.index')"
                        class="ml-3 inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('hbwtable.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                        title="HBW-Tabelle"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </Link>
                    
                    <!-- Statistik Icon -->
                    <Link
                        :href="route('statistik.index')"
                        class="ml-3 inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('statistik.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                        title="Intelligente Statistiken"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </Link>

                    <!-- AI Assistant Icon -->
                    <Link
                        :href="route('ai-assistant.page')"
                        class="ml-3 inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out"
                        :class="[
                            route().current('ai-assistant.*')
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        ]"
                        title="KI-Assistent"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </Link>
                </div>
            </div>
        </header>

        <main class="py-10">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <slot />
            </div>
        </main>
    </div>
</template>
