import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// CSRF-Token für Laravel aus dem Meta-Tag extrahieren, falls vorhanden
const token = document.head.querySelector('meta[name="csrf-token"]');

if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.warn('CSRF-Token nicht gefunden. Bitte stellen Sie sicher, dass das Meta-Tag vorhanden ist.');
}

// Axios automatisch mit Cookies senden lassen (wichtig für Session-Cookie)
window.axios.defaults.withCredentials = true;

// Hier können Sie globale Bibliotheken oder Konfigurationen importieren
