const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"dashboard":{"uri":"\/","methods":["GET","HEAD"]},"spektrometer":{"uri":"spektrometer","methods":["GET","HEAD"]},"spektrometer.store":{"uri":"spektrometer","methods":["POST"]},"formanlage":{"uri":"formanlage","methods":["GET","HEAD"]},"formanlage.store":{"uri":"formanlage","methods":["POST"]},"gussnachbehandlung":{"uri":"gussnachbehandlung","methods":["GET","HEAD"]},"gussnachbehandlung.store":{"uri":"gussnachbehandlung","methods":["POST"]},"qs":{"uri":"qs","methods":["GET","HEAD"]},"qs.store":{"uri":"qs","methods":["POST"]},"einstellungen":{"uri":"einstellungen","methods":["GET","HEAD"]},"einstellungen.save":{"uri":"einstellungen","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
