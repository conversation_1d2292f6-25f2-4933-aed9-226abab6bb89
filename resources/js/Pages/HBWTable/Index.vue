<script setup>
import { ref, computed, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';
import Swal from 'sweetalert2';

const props = defineProps({
    initialMaterials: {
        type: Array,
        required: true
    }
});

const searchTerm = ref('');
const selectedMaterial = ref('all');
const materials = ref(props.initialMaterials || []);
const isModalOpen = ref(false);
const editingEntry = ref(null);
const currentEntry = ref({
    material_type: '',
    thickness_range: '',
    min_hbw: null,
    max_hbw: null
});

const uniqueMaterials = computed(() => {
    return ['all', ...new Set(materials.value.map(m => m.type))];
});

const filteredMaterials = computed(() => {
    return materials.value.filter(material =>
        (selectedMaterial.value === 'all' || material.type === selectedMaterial.value) &&
        material.type.toLowerCase().includes(searchTerm.value.toLowerCase())
    );
});

const openAddModal = () => {
    editingEntry.value = null;
    currentEntry.value = {
        material_type: '',
        thickness_range: '',
        min_hbw: null,
        max_hbw: null
    };
    isModalOpen.value = true;
};

const openEditModal = (material, range) => {
    editingEntry.value = range;
    currentEntry.value = {
        material_type: material.type,
        thickness_range: range.thickness,
        min_hbw: range.min,
        max_hbw: range.max
    };
    isModalOpen.value = true;
};

const closeModal = () => {
    isModalOpen.value = false;
    editingEntry.value = null;
    currentEntry.value = {
        material_type: '',
        thickness_range: '',
        min_hbw: null,
        max_hbw: null
    };
};

const saveEntry = async () => {
    try {
        const endpoint = editingEntry.value
            ? `/hbwtable/${editingEntry.value.id}`
            : '/hbwtable';
        
        const method = editingEntry.value ? 'put' : 'post';
        
        const response = await axios[method](endpoint, currentEntry.value);
        
        if (response.data.success) {
            // Aktualisiere die lokale Liste
            if (editingEntry.value) {
                // Finde und aktualisiere den bestehenden Eintrag
                const materialIndex = materials.value.findIndex(m => m.type === currentEntry.value.material_type);
                if (materialIndex !== -1) {
                    const rangeIndex = materials.value[materialIndex].ranges.findIndex(r => r.id === editingEntry.value.id);
                    if (rangeIndex !== -1) {
                        materials.value[materialIndex].ranges[rangeIndex] = {
                            id: editingEntry.value.id,
                            thickness: currentEntry.value.thickness_range,
                            min: currentEntry.value.min_hbw,
                            max: currentEntry.value.max_hbw
                        };
                    }
                }
            } else {
                // Füge einen neuen Eintrag hinzu
                const materialIndex = materials.value.findIndex(m => m.type === currentEntry.value.material_type);
                const newRange = {
                    id: response.data.data.id,
                    thickness: currentEntry.value.thickness_range,
                    min: currentEntry.value.min_hbw,
                    max: currentEntry.value.max_hbw
                };

                if (materialIndex !== -1) {
                    materials.value[materialIndex].ranges.push(newRange);
                } else {
                    materials.value.push({
                        type: currentEntry.value.material_type,
                        ranges: [newRange]
                    });
                }
            }

            Swal.fire({
                title: 'Erfolg!',
                text: response.data.message,
                icon: 'success',
                confirmButtonText: 'OK'
            });

            closeModal();
        }
    } catch (error) {
        console.error('Fehler beim Speichern:', error);
        Swal.fire({
            title: 'Fehler!',
            text: error.response?.data?.message || 'Ein Fehler ist aufgetreten',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }
};

const deleteEntry = async (material, range) => {
    try {
        const result = await Swal.fire({
            title: 'Sind Sie sicher?',
            text: 'Dieser Eintrag wird unwiderruflich gelöscht!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ja, löschen!',
            cancelButtonText: 'Abbrechen'
        });

        if (result.isConfirmed) {
            const response = await axios.delete(`/hbwtable/${range.id}`);
            
            if (response.data.success) {
                // Entferne den Eintrag aus der lokalen Liste
                const materialIndex = materials.value.findIndex(m => m.type === material.type);
                if (materialIndex !== -1) {
                    materials.value[materialIndex].ranges = materials.value[materialIndex].ranges.filter(r => r.id !== range.id);
                    
                    // Wenn keine Bereiche mehr übrig sind, entferne den Material-Typ
                    if (materials.value[materialIndex].ranges.length === 0) {
                        materials.value.splice(materialIndex, 1);
                    }
                }

                Swal.fire({
                    title: 'Gelöscht!',
                    text: response.data.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            }
        }
    } catch (error) {
        console.error('Fehler beim Löschen:', error);
        Swal.fire({
            title: 'Fehler!',
            text: error.response?.data?.message || 'Ein Fehler ist aufgetreten',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }
};
</script>

<template>
    <AppLayout title="HBW-Tabelle">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    HBW-Tabelle
                </h2>
                <button
                    @click="openAddModal"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    Neuer Eintrag
                </button>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <!-- Suchleiste und Filter -->
                    <div class="flex gap-4 mb-6">
                        <input
                            type="text"
                            v-model="searchTerm"
                            placeholder="Suchen..."
                            class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        />
                        <select
                            v-model="selectedMaterial"
                            class="rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        >
                            <option v-for="material in uniqueMaterials" :key="material" :value="material">
                                {{ material === 'all' ? 'Alle Materialien' : material }}
                            </option>
                        </select>
                    </div>

                    <!-- Tabelle -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wanddicke [mm]</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min. HBW</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max. HBW</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aktionen</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template v-for="material in filteredMaterials" :key="material.type">
                                    <tr v-for="range in material.ranges" :key="range.id">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ material.type }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ range.thickness }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ range.min }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ range.max }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <button
                                                @click="openEditModal(material, range)"
                                                class="text-blue-600 hover:text-blue-900 mr-3"
                                            >
                                                Bearbeiten
                                            </button>
                                            <button
                                                @click="deleteEntry(material, range)"
                                                class="text-red-600 hover:text-red-900"
                                            >
                                                Löschen
                                            </button>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal -->
        <TransitionRoot appear :show="isModalOpen" as="template">
            <Dialog as="div" @close="closeModal" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95"
                        >
                            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 mb-4">
                                    {{ editingEntry ? 'Eintrag bearbeiten' : 'Neuer Eintrag' }}
                                </DialogTitle>

                                <div class="mt-4 space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Material</label>
                                        <input
                                            type="text"
                                            v-model="currentEntry.material_type"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Wanddicke [mm]</label>
                                        <input
                                            type="text"
                                            v-model="currentEntry.thickness_range"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Min. HBW</label>
                                        <input
                                            type="number"
                                            v-model="currentEntry.min_hbw"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Max. HBW</label>
                                        <input
                                            type="number"
                                            v-model="currentEntry.max_hbw"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </div>
                                </div>

                                <div class="mt-6 flex justify-end space-x-3">
                                    <button
                                        @click="closeModal"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                                    >
                                        Abbrechen
                                    </button>
                                    <button
                                        @click="saveEntry"
                                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        {{ editingEntry ? 'Aktualisieren' : 'Speichern' }}
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </AppLayout>
</template>
