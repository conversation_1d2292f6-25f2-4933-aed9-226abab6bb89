<template>
    <AppLayout title="Härtewerte">
        <div class="min-h-screen bg-gray-50 text-black">
            <!-- Main Container mit zwei Spalten -->
            <div class="container mx-auto px-4 py-8">
                <div class="lg:grid lg:grid-cols-3 gap-8">
                    <!-- <PERSON><PERSON> (2/3) - Auftragsdaten und Verlauf -->
                    <div class="lg:col-span-2 space-y-6 mb-8 lg:mb-0">
                        <!-- Auftragsdaten -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-xl font-semibold">Auftragsdaten</h2>
                                <button 
                                    type="button" 
                                    @click="resetForm" 
                                    class="text-gray-500 hover:text-indigo-600 flex items-center text-sm"
                                    title="Auftragsdaten zurücksetzen">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    Zurücksetzen
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium">Teilenummer</label>
                                    <input v-model="form.teilenummer" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium">Auftragsnummer</label>
                                    <input v-model="form.auftragsnummer" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium">Chargennummer</label>
                                    <input v-model="form.chargenummer" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                </div>
                                
                                <!-- Eisenmarke Dropdown -->
                                <div>
                                    <label class="block text-sm font-medium">Eisenmarke</label>
                                    <select v-model="form.eisenmarke" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                            required
                                            @change="updateSollwerteFromEisenmarke">
                                        <option value="" disabled selected>Bitte wählen</option>
                                        <option v-for="eisenmarke in eisenmarken" :key="eisenmarke" :value="eisenmarke">
                                            {{ eisenmarke }}
                                        </option>
                                    </select>
                                </div>
                                
                                <!-- Wanddicke wird jetzt im Modal angezeigt -->
                                <div>
                                    <label class="block text-sm font-medium">Wanddicke (mm)</label>
                                    <div class="flex items-center mt-1">
                                        <input v-model="selectedWanddicke" 
                                            type="text" 
                                            readonly
                                            class="block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            placeholder="Nicht ausgewählt">
                                        <button 
                                            type="button"
                                            @click="showWanddickeModal = true"
                                            class="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                            :disabled="verfuegbareWanddicken.length === 0">
                                            Auswählen
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Soll von und Soll bis nebeneinander -->
                                <div class="col-span-1 md:col-span-2">
                                    <label class="block text-sm font-medium">Sollbereich</label>
                                    <div class="grid grid-cols-2 gap-2 mt-1">
                                        <div>
                                            <div class="text-xs mb-1">Von</div>
                                            <input v-model="form.soll_von" type="number" step="0.01" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                </div>
                                <div>
                                            <div class="text-xs mb-1">Bis</div>
                                            <input v-model="form.soll_bis" type="number" step="0.01" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Aktuelle Härtewerte -->
                        <div v-if="temporaryHaertewerte.length > 0" class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-xl font-semibold mb-4">Aktuelle Härtewerte</h2>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium uppercase tracking-wider">Position</th>
                                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium uppercase tracking-wider">Nest</th>
                                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium uppercase tracking-wider">Ist-Wert</th>
                                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium uppercase tracking-wider">Wanddicke</th>
                                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 bg-gray-50"></th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-for="(haertewert, index) in temporaryHaertewerte" :key="index"
                                            :class="{'bg-red-50': !isWithinRange(haertewert)}">
                                            <td class="px-6 py-4 whitespace-nowrap">{{ haertewert.pruefposition }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">{{ haertewert.nestnummer || '-' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap font-medium">{{ haertewert.ist_wert }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">{{ haertewert.wanddicke ? haertewert.wanddicke + ' mm' : '-' }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="{'text-green-600': isWithinRange(haertewert), 'text-red-600': !isWithinRange(haertewert)}"
                                                      class="px-2 py-1 rounded text-sm font-medium inline-block w-12 text-center" 
                                                      :style="{'background-color': isWithinRange(haertewert) ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)'}">
                                                    {{ isWithinRange(haertewert) ? 'IO' : 'NIO' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-right">
                                                <button @click="removeTemporaryHaertewert(index)" 
                                                        class="text-red-600 hover:text-red-900 bg-red-50 p-1 rounded">
                                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Verlaufsansicht für gespeicherte Härtewerte -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-semibold">Verlauf - Gespeicherte Härtewerte</h2>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                    <input v-model="searchQuery" 
                                        type="text" 
                                        placeholder="" 
                                        class="block w-20 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:w-60 focus:outline-none focus:placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 sm:text-sm">
                                </div>
                            </div>
                            
                            <div v-if="gespeicherteHaertewerte.length === 0" class="text-center py-4">
                                Noch keine Härtewerte gespeichert.
                            </div>
                            
                            <div v-else-if="filteredGruppierteDaten.length === 0" class="text-center py-4">
                                Keine Treffer für "{{ searchQuery }}".
                            </div>
                            
                            <div v-else class="space-y-4">
                                <div v-for="(gruppe, index) in filteredGruppierteDaten" :key="index" class="border rounded-lg overflow-hidden">
                                    <!-- Header mit Toggle, Auftragsdaten und Durchschnittswert -->
                                    <div class="bg-gray-50 p-4 cursor-pointer"
                                         @click="toggleGruppe(index)">
                                        <div class="flex flex-wrap justify-between items-center">
                                            <div class="flex items-center space-x-2">
                                                <svg :class="{'transform rotate-90': expandedGroups[index]}" 
                                                     class="h-5 w-5 transition-transform duration-200" 
                                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                                <h3 class="text-base font-medium">
                                                    Auftrag: <span v-html="highlightMatch(gruppe.auftragsnummer)"></span> | 
                                                    Teil: <span v-html="highlightMatch(gruppe.teilenummer)"></span> | 
                                                    Charge: <span v-html="highlightMatch(gruppe.chargenummer)"></span> | 
                                                    Eisenmarke: <span v-html="highlightMatch(gruppe.eisenmarke || '-')"></span>
                                                </h3>
                                            </div>
                                            <div class="flex items-center space-x-4">
                                                <div class="text-sm text-gray-500">
                                                    {{ new Date(gruppe.datum).toLocaleDateString('de-DE') }}
                                                </div>
                                                <div class="font-medium text-indigo-700">
                                                    Ø HBW: {{ calculateAverage(gruppe.haertewerte) }}
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Aktionen für die gesamte Gruppe -->
                                        <div class="flex justify-end mt-2 space-x-2">
                                            <button @click.stop="exportToPDF(gruppe)" 
                                                   class="text-black bg-indigo-50 px-3 py-1 rounded text-sm hover:bg-indigo-100 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                PDF Export
                                            </button>
                                            <button @click.stop="copyToForm(gruppe)" 
                                                   class="text-black bg-green-50 px-3 py-1 rounded text-sm hover:bg-green-100 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                In Formular übernehmen
                                            </button>
                                            <button @click.stop="deleteGroup(gruppe)" 
                                                   class="text-black bg-red-50 px-3 py-1 rounded text-sm hover:bg-red-100 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                Löschen
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Tabelle mit Härtewerten (nur anzeigen, wenn expandiert) -->
                                    <div v-show="expandedGroups[index]" class="overflow-x-auto transition-all duration-200 ease-in-out">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead>
                                                <tr>
                                                    <th class="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                                    <th class="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nest</th>
                                                    <th class="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ist-Wert</th>
                                                    <th class="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wanddicke (mm)</th>
                                                    <th class="px-4 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                                    <th class="px-4 py-2 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Aktionen</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <tr v-for="(haertewert, hIndex) in gruppe.haertewerte" :key="hIndex"
                                                    :class="{'bg-red-50': !isWertInRange(haertewert.ist_wert, haertewert.soll_von, haertewert.soll_bis)}">
                                                    <td class="px-4 py-2 whitespace-nowrap">{{ haertewert.pruefposition }}</td>
                                                    <td class="px-4 py-2 whitespace-nowrap">{{ haertewert.nestnummer || '-' }}</td>
                                                    <td class="px-4 py-2 whitespace-nowrap font-medium">{{ haertewert.ist_wert }}</td>
                                                    <td class="px-4 py-2 whitespace-nowrap">{{ haertewert.wanddicke ? haertewert.wanddicke + ' mm' : '-' }}</td>
                                                    <td class="px-4 py-2 whitespace-nowrap">
                                                        <span :class="{'text-green-600': isWertInRange(haertewert.ist_wert, haertewert.soll_von, haertewert.soll_bis), 
                                                                    'text-red-600': !isWertInRange(haertewert.ist_wert, haertewert.soll_von, haertewert.soll_bis)}">
                                                            {{ isWertInRange(haertewert.ist_wert, haertewert.soll_von, haertewert.soll_bis) ? 'IO' : 'NIO' }}
                                                        </span>
                                                    </td>
                                                    <td class="px-4 py-2 whitespace-nowrap text-center">
                                                        <div class="flex justify-center space-x-2">
                                                            <button @click="deleteHaertewert(haertewert.id)" class="text-red-600 hover:text-red-900">
                                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rechte Spalte (1/3) - IST-Wert Eingabe -->
                    <div class="lg:col-span-1 rechte-spalte mt-8 lg:mt-0">
                        <div class="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                            <h2 class="text-xl font-semibold mb-6">Messwert Erfassung</h2>
                            <form @submit.prevent="addHaertewert" class="space-y-6">
                                <!-- IST-Wert Input - mit dynamischem Hintergrund -->
                                <div>
                                    <label class="block text-lg font-medium text-gray-700">IST-Wert</label>
                                    <div class="relative flex mt-1">
                                        <input v-model="currentHaertewert.ist_wert" 
                                               type="number" 
                                               step="0.01"
                                               :class="{
                                                   'block w-full h-16 text-2xl font-bold rounded-md shadow-sm focus:ring-2': true,
                                                   'bg-green-50 border-green-300 text-green-800 focus:border-green-500 focus:ring-green-500': isValidInputAndInRange,
                                                   'bg-red-50 border-red-300 text-red-800 focus:border-red-500 focus:ring-red-500': isValidInputAndOutOfRange,
                                                   'bg-white border-gray-300 focus:border-indigo-500 focus:ring-indigo-500': !isValidInput
                                               }"
                                               required
                                               ref="istWertInput"
                                               inputmode="decimal"
                                               @keydown.enter.prevent="addHaertewert">
                                        
                                        <!-- Mobile-only submit button -->
                                        <button type="button"
                                                @click="addHaertewert"
                                                class="md:hidden absolute right-1 top-1/2 transform -translate-y-1/2 h-14 px-3 py-2 bg-indigo-600 text-white rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <div v-if="isValidInput" class="mt-1 text-sm">
                                        <span v-if="isValidInputAndInRange" class="text-green-600">✓ Wert im Sollbereich</span>
                                        <span v-else class="text-red-600">⚠ Wert außerhalb des Sollbereichs ({{ form.soll_von }} - {{ form.soll_bis }})</span>
                                    </div>
                                </div>

                                <!-- Nestnummer Input -->
                                <div>
                                    <div class="flex justify-between">
                                        <label class="block text-sm font-medium text-black">Nestnummer</label>
                                        <button type="button" 
                                                @click="currentHaertewert.nestnummer = ''" 
                                                class="text-xs text-black hover:text-black">
                                            Zurücksetzen
                                        </button>
                                    </div>
                                    <select v-model="currentHaertewert.nestnummer" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="" disabled>Bitte wählen</option>
                                        <option v-for="n in 30" :key="n" :value="n.toString()">
                                            {{ n }}
                                        </option>
                                    </select>
                                </div>

                                <!-- Position Dropdown -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Position</label>
                                    <select v-model="currentHaertewert.pruefposition" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                            required>
                                        <option value="UT">UT</option>
                                        <option value="OT">OT</option>
                                        <option value="Stehend">Stehend</option>
                                        <option value="Zugstrebe">Zugstrebe</option>
                                        <option value="Zahnkranz">Zahnkranz</option>
                                        <option value="Andere">Andere</option>
                                    </select>
                                </div>

                                <!-- Benutzerdefinierte Position (nur anzeigen, wenn "Andere" ausgewählt ist) -->
                                <div v-if="currentHaertewert.pruefposition === 'Andere'">
                                    <label class="block text-sm font-medium text-gray-700 mt-3">Benutzerdefinierte Position</label>
                                    <input v-model="customPosition" 
                                           type="text" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                           placeholder="Position eingeben" 
                                           required>
                                </div>

                                <!-- Prüfverfahren Dropdown -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Prüfverfahren</label>
                                    <select v-model="currentHaertewert.pruefverfahren" 
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                            required>
                                        <option value="Brinellhärte Ø 10mm">Brinellhärte Ø 10mm</option>
                                        <option value="Brinellhärte Ø 5mm">Brinellhärte Ø 5mm</option>
                                        <option value="Scherkraft-Härteprüfer">Scherkraft-Härteprüfer</option>
                                        <option value="Optisches Auslesen">Optisches Auslesen</option>
                                    </select>
                                </div>

                                <!-- Geprüft von Input -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Geprüft von</label>
                                    <input v-model="form.geprueft_von" type="text" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                           required>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-4">
                                    <button type="button"
                                            @click.prevent="addHaertewert"
                                            class="bg-indigo-600 text-white px-4 py-3 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                        Härtewert hinzufügen
                                    </button>
                                    <button type="button" 
                                            @click="saveAllHaertewerte"
                                            :disabled="!hasHaertewerte"
                                            class="bg-green-600 text-white px-4 py-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50">
                                        Alle speichern
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal zur Wanddickenauswahl -->
        <div v-if="showWanddickeModal" class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity" aria-hidden="true" @click="showWanddickeModal = false">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>

                <!-- Modal-Panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full text-black">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium mb-4 text-black">Wanddicke auswählen</h3>
                                
                                <div class="grid grid-cols-1 gap-4">
                                    <div v-for="wanddicke in verfuegbareWanddicken" :key="wanddicke" 
                                        class="border rounded-md p-3 cursor-pointer hover:bg-indigo-50"
                                        :class="{'bg-indigo-100 border-indigo-500': selectedWanddicke === wanddicke}"
                                        @click="setWanddicke(wanddicke)">
                                        <div class="font-medium text-black">{{ wanddicke }} mm</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';
import Swal from 'sweetalert2';

// Get props from the controller
const props = defineProps({
    haertewerte: Array,
    eisenmarken: Array
});

const temporaryHaertewerte = ref([]);
const gespeicherteHaertewerte = ref([]); // Gespeicherte Härtewerte aus der Datenbank
const istWertInput = ref(null);  // Define the ref for the IST-Wert input field
const verfuegbareWanddicken = ref([]); // Verfügbare Wanddicken basierend auf der Eisenmarke
const selectedWanddicke = ref(''); // Ausgewählte Wanddicke
const isLoading = ref(false); // Status-Variable für Lade-Vorgänge
const form = useForm({
    teilenummer: '',
    auftragsnummer: '',
    chargenummer: '',
    eisenmarke: '',
    soll_von: '',
    soll_bis: '',
    geprueft_von: 'A. Calhan',
});

// Die LocalStorage-Schlüssel als Konstanten definieren
const TEMP_HAERTEWERTE_KEY = 'temporaryHaertewerte';
const FORM_DATA_KEY = 'haertewerteFormData';
const SELECTED_WANDDICKE_KEY = 'selectedWanddicke';

const currentHaertewert = ref({
    ist_wert: '',
    pruefposition: 'UT',
    pruefverfahren: 'Brinellhärte Ø 10mm',
    nestnummer: '',
    bemerkung: null
});

const hasHaertewerte = computed(() => temporaryHaertewerte.value.length > 0);

// Store HBW data for the selected Eisenmarke
const selectedEisenmarkeData = ref([]);

// Ergänze folgende computed properties
// Prüft, ob die Eingabe gültig ist und im Sollbereich liegt
const isValidInput = computed(() => {
    const istWert = currentHaertewert.value.ist_wert;
    return istWert !== '' && istWert !== null && istWert !== undefined && !isNaN(parseFloat(istWert));
});

// Prüft, ob die Eingabe gültig ist und im Sollbereich liegt
const isValidInputAndInRange = computed(() => {
    if (!isValidInput.value) return false;
    if (!form.soll_von || !form.soll_bis) return false;
    
    const istWert = parseFloat(currentHaertewert.value.ist_wert);
    const sollVon = parseFloat(form.soll_von);
    const sollBis = parseFloat(form.soll_bis);
    
    return istWert >= sollVon && istWert <= sollBis;
});

// Prüft, ob die Eingabe gültig ist aber außerhalb des Sollbereichs liegt
const isValidInputAndOutOfRange = computed(() => {
    return isValidInput.value && !isValidInputAndInRange.value;
});

// Gruppierte Daten für die Verlaufsansicht
const gruppierteDaten = computed(() => {
    const gruppen = {};
    
    // Gruppieren nach Auftragsnummer, Teilenummer und Chargenummer
    gespeicherteHaertewerte.value.forEach(hw => {
        const key = `${hw.auftragsnummer}-${hw.teilenummer}-${hw.chargenummer}`;
        
        if (!gruppen[key]) {
            gruppen[key] = {
                auftragsnummer: hw.auftragsnummer,
                teilenummer: hw.teilenummer,
                chargenummer: hw.chargenummer,
                eisenmarke: hw.eisenmarke,
                datum: hw.created_at,
                haertewerte: []
            };
        }
        
        gruppen[key].haertewerte.push(hw);
    });
    
    // In Array umwandeln und nach Datum sortieren (neueste zuerst)
    return Object.values(gruppen).sort((a, b) => new Date(b.datum) - new Date(a.datum));
});

// Funktion zum Laden der gespeicherten Härtewerte
const ladeGespeicherteHaertewerte = async () => {
    try {
        const response = await axios.get(route('haertewerte.index'));
        gespeicherteHaertewerte.value = response.data;
    } catch (error) {
        console.error('Fehler beim Laden der gespeicherten Härtewerte:', error);
    }
};

// Hilfsfunktion zum Überprüfen, ob ein Wert innerhalb eines Bereichs liegt
const isWertInRange = (wert, von, bis) => {
    try {
        const istWert = parseFloat(wert);
        const sollVon = parseFloat(von);
        const sollBis = parseFloat(bis);
        
        if (isNaN(istWert) || isNaN(sollVon) || isNaN(sollBis)) {
            return false;
        }
        
        return istWert >= sollVon && istWert <= sollBis;
    } catch (error) {
        return false;
    }
};

const customPosition = ref('');

const addHaertewert = () => {
    // Wert direkt am Anfang sichern, bevor er manipuliert werden könnte
    const currentIstWert = currentHaertewert.value.ist_wert;
    const currentNestnummer = currentHaertewert.value.nestnummer;
    
    // Validierung: IST-Wert
    if (currentIstWert === '' || currentIstWert === null || currentIstWert === undefined) {
        Swal.fire({
            title: 'Fehler',
            text: 'Bitte geben Sie einen IST-Wert ein.',
            icon: 'error'
        });
        return;
    }
    
    // Validierung: Nestnummer
    if (currentNestnummer === '' || currentNestnummer === null || currentNestnummer === undefined) {
        Swal.fire({
            title: 'Fehler',
            text: 'Bitte wählen Sie eine Nestnummer aus.',
            icon: 'error'
        });
        return;
    }
    
    // Validierung: Benutzerdefinierte Position, wenn "Andere" ausgewählt ist
    if (currentHaertewert.value.pruefposition === 'Andere' && (!customPosition.value || customPosition.value.trim() === '')) {
        Swal.fire({
            title: 'Fehler',
            text: 'Bitte geben Sie eine benutzerdefinierte Position ein.',
            icon: 'error'
        });
        return;
    }
    
    try {
        // Werte explizit umwandeln und prüfen
        const istWert = parseFloat(currentIstWert);
        
        if (isNaN(istWert)) {
            Swal.fire({
                title: 'Fehler',
                text: 'Der IST-Wert muss eine gültige Zahl sein.',
                icon: 'error'
            });
            return;
        }
        
        // Wanddicke ohne 'mm' am Ende speichern
        const cleanWanddicke = selectedWanddicke.value ? selectedWanddicke.value.replace(/\s*mm$/, '') : '';

        // Bei "Andere" die benutzerdefinierte Position verwenden
        const finalPosition = currentHaertewert.value.pruefposition === 'Andere' ? customPosition.value : currentHaertewert.value.pruefposition;

        const newHaertewert = {
            teilenummer: form.teilenummer || '',
            auftragsnummer: form.auftragsnummer || '',
            chargenummer: form.chargenummer || '',
            eisenmarke: form.eisenmarke || '',
            soll_von: form.soll_von !== null && form.soll_von !== undefined && form.soll_von !== '' ? parseFloat(form.soll_von) : 0,
            soll_bis: form.soll_bis !== null && form.soll_bis !== undefined && form.soll_bis !== '' ? parseFloat(form.soll_bis) : 0,
            geprueft_von: form.geprueft_von || '',
            ist_wert: istWert,
            pruefposition: finalPosition,
            pruefverfahren: currentHaertewert.value.pruefverfahren || 'Brinellhärte',
            nestnummer: currentHaertewert.value.nestnummer || '',
            wanddicke: cleanWanddicke
        };
        
        // Härtewert zum Array hinzufügen
        temporaryHaertewerte.value.push(newHaertewert);
        
        // Im localStorage speichern
        saveToLocalStorage();
        
        // Store current scroll position
        const erfassungBereich = document.querySelector('.rechte-spalte');
        const scrollPosition = erfassungBereich ? erfassungBereich.getBoundingClientRect().top + window.pageYOffset : 0;
        
        // Reset nach dem Hinzufügen
        nextTick(() => {
            // Nur den IST-Wert zurücksetzen
            currentHaertewert.value.ist_wert = '';
            
            // Prüfen, ob es ein Mobilgerät ist
            const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
            
            // Fokus auf IST-Wert setzen - jetzt auch auf mobilen Geräten
            if (istWertInput.value) {
                istWertInput.value.focus();
            } else {
                document.querySelector('.rechte-spalte input[type="number"]')?.focus();
            }
            
            // Auf mobilen Geräten zusätzlich die Scroll-Position wiederherstellen
            if (isMobile && erfassungBereich) {
                setTimeout(() => {
                    window.scrollTo({
                        top: scrollPosition,
                        behavior: 'auto'
                    });
                }, 100); // Kurze Verzögerung, um sicherzustellen, dass der Fokus gesetzt wurde
            }
        });
    } catch (error) {
        console.error('Fehler beim Hinzufügen des Härtewerts:', error);
        Swal.fire({
            title: 'Fehler',
            text: 'Ein unerwarteter Fehler ist aufgetreten: ' + error.message,
            icon: 'error'
        });
    }
};

const saveAllHaertewerte = async () => {
    if (temporaryHaertewerte.value.length === 0) {
        Swal.fire('Fehler', 'Keine Härtewerte zum Speichern vorhanden.', 'error');
        return;
    }
    
    if (!form.teilenummer || !form.auftragsnummer || !form.chargenummer || !form.eisenmarke || !form.geprueft_von) {
        Swal.fire('Fehler', 'Bitte alle Pflichtfelder ausfüllen (Teile-, Auftrags-, Chargennummer, Eisenmarke und Geprüft von).', 'error');
        return;
    }
    
    if (!form.soll_von || !form.soll_bis) {
        Swal.fire('Fehler', 'Bitte Sollwerte eingeben (von/bis).', 'error');
        return;
    }
    
    // Store current scroll position for mobile devices
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    const erfassungBereich = document.querySelector('.rechte-spalte');
    const scrollPosition = isMobile && erfassungBereich ? 
        erfassungBereich.getBoundingClientRect().top + window.pageYOffset : 0;
    
    isLoading.value = true;
    
    // Stelle sicher, dass alle erforderlichen Felder im korrekten Format vorliegen
    const preparedHaertewerte = temporaryHaertewerte.value.map(hw => {
        return {
            ist_wert: parseFloat(hw.ist_wert),
            pruefposition: hw.pruefposition || 'UT',
            pruefverfahren: hw.pruefverfahren || 'Brinellhärte',
            nestnummer: hw.nestnummer || null,
            bemerkung: hw.bemerkung || null,
            wanddicke: typeof hw.wanddicke === 'string' ? hw.wanddicke.replace(/\s*mm$/, '') : hw.wanddicke || null
        };
    });
    
    // Bereite die Daten entsprechend den Server-Erwartungen vor
    const saveData = {
        teilenummer: form.teilenummer,
        auftragsnummer: form.auftragsnummer,
        chargenummer: form.chargenummer,
        eisenmarke: form.eisenmarke,
        soll_von: parseFloat(form.soll_von),
        soll_bis: parseFloat(form.soll_bis),
        geprueft_von: form.geprueft_von,
        wanddicke: selectedWanddicke.value ? selectedWanddicke.value.replace(/\s*mm$/, '') : null,
        haertewerte: preparedHaertewerte
    };
    
    console.log('Sende Daten zum Server:', saveData);
    
    axios.post(route('haertewerte.store'), saveData)
        .then(response => {
            if (response.data.success) {
                // Alle temporären Härtewerte zurücksetzen
                temporaryHaertewerte.value = [];
                
                // Auftragsdaten zurücksetzen
                form.reset();
                form.geprueft_von = 'A. Calhan'; // Standard-Wert wiederherstellen
                
                // Wanddicke zurücksetzen
                selectedWanddicke.value = '';
                verfuegbareWanddicken.value = [];
                
                // Im Erfolgsfall die Daten aus dem localStorage löschen
                clearLocalStorage();
                
                ladeGespeicherteHaertewerte();
                Swal.fire('Erfolg', 'Härtewerte erfolgreich gespeichert.', 'success');
                
                // Restore scroll position on mobile devices
                if (isMobile && erfassungBereich) {
                    nextTick(() => {
                        window.scrollTo({
                            top: scrollPosition,
                            behavior: 'auto'
                        });
                    });
                }
            } else {
                console.error('Fehler beim Speichern:', response.data);
                Swal.fire('Fehler', 'Beim Speichern ist ein Fehler aufgetreten.', 'error');
            }
        })
        .catch(error => {
            console.error('Axios-Fehler:', error);
            let errorMsg = 'Beim Speichern ist ein Fehler aufgetreten.';
            
            // Detaillierte Fehlermeldungen anzeigen
            if (error.response && error.response.data) {
                if (error.response.data.message) {
                    errorMsg = error.response.data.message;
                }
                
                // Zeige spezifische Validierungsfehler an
                if (error.response.data.errors) {
                    const errors = error.response.data.errors;
                    const errorList = [];
                    
                    for (const field in errors) {
                        errorList.push(...errors[field]);
                    }
                    
                    if (errorList.length > 0) {
                        errorMsg += '<br><br>' + errorList.join('<br>');
                    }
                }
            }
            
            Swal.fire({
                title: 'Fehler',
                html: errorMsg,
                icon: 'error'
            });
        })
        .finally(() => {
            isLoading.value = false;
        });
};

const removeTemporaryHaertewert = (index) => {
    temporaryHaertewerte.value.splice(index, 1);
    // Nach dem Entfernen die Änderungen im localStorage speichern
    saveToLocalStorage();
};

const isWithinRange = (haertewert) => {
    try {
        // Sicherstellen, dass alle Werte als Zahlen behandelt werden
        const istWert = parseFloat(haertewert.ist_wert);
        const sollVon = parseFloat(haertewert.soll_von || form.soll_von || 0);
        const sollBis = parseFloat(haertewert.soll_bis || form.soll_bis || 0);
        
        // Überprüfen, ob nach dem Parsen gültige Zahlen vorhanden sind
        if (isNaN(istWert) || isNaN(sollVon) || isNaN(sollBis)) {
            console.warn('Ungültige Zahlen in isWithinRange:', { istWert, sollVon, sollBis, 
                raw: { 
                    ist: haertewert.ist_wert, 
                    von: haertewert.soll_von || form.soll_von, 
                    bis: haertewert.soll_bis || form.soll_bis 
                }
            });
            return false;
        }
        
        // Überprüfen, ob der Wert im Bereich liegt
        return istWert >= sollVon && istWert <= sollBis;
    } catch (error) {
        console.error('Fehler in isWithinRange:', error, haertewert);
        return false;
    }
};

const showWanddickeModal = ref(false);

const updateSollwerteFromEisenmarke = async () => {
    if (!form.eisenmarke) return;
    
    try {
        const response = await axios.get(route('haertewerte.sollwerte-by-eisenmarke', { 
            eisenmarke: form.eisenmarke 
        }));
        
        if (response.data.success) {
            verfuegbareWanddicken.value = response.data.data.wanddicken;
            
            // Nur zurücksetzen, wenn noch keine Wanddicke aus localStorage geladen wurde
            const savedWanddicke = localStorage.getItem(SELECTED_WANDDICKE_KEY);
            if (!savedWanddicke) {
                selectedWanddicke.value = ''; // Wanddicke zurücksetzen
                form.soll_von = ''; // Soll-Werte zurücksetzen
                form.soll_bis = '';
                
                // Modal automatisch anzeigen, wenn Wanddicken verfügbar sind und noch keine ausgewählt
                if (verfuegbareWanddicken.value.length > 0) {
                    showWanddickeModal.value = true;
                }
            } else {
                // Wenn bereits eine Wanddicke aus localStorage geladen wurde,
                // keine Sollwerte zurücksetzen und kein Modal anzeigen
            }
            
            // Änderungen in localStorage speichern
            saveToLocalStorage();
        } else {
            console.error('Fehler beim Abrufen der Sollwerte:', response.data.message);
        }
    } catch (error) {
        console.error('Fehler beim Abrufen der Sollwerte:', error);
    }
};

// Neue Funktion, die direkt die Wanddicke setzt und das Modal schließt
const setWanddicke = (wanddicke) => {
    selectedWanddicke.value = wanddicke;
    showWanddickeModal.value = false;
    updateSollwerteFromWanddicke();
    // Speichern in localStorage nach Änderung der Wanddicke
    saveToLocalStorage();
};

const updateSollwerteFromWanddicke = async () => {
    if (!form.eisenmarke || !selectedWanddicke.value) return;
    
    try {
        const response = await axios.get(route('haertewerte.sollwerte-by-eisenmarke', { 
            eisenmarke: form.eisenmarke 
        }));
        
        if (response.data.success && response.data.data.werte[selectedWanddicke.value]) {
            const werte = response.data.data.werte[selectedWanddicke.value];
            form.soll_von = werte.von;
            form.soll_bis = werte.bis;
            
            // Änderungen in localStorage speichern
            saveToLocalStorage();
        } else {
            console.error('Fehler beim Abrufen der Sollwerte für die Wanddicke');
        }
    } catch (error) {
        console.error('Fehler beim Abrufen der Sollwerte für die Wanddicke:', error);
    }
};

// Fokus auf IST-Wert Input beim Laden und Laden der gespeicherten Daten
onMounted(async () => {
    // Zuerst die temporären Daten aus dem localStorage laden
    loadFromLocalStorage();
    
    nextTick(() => {
        document.querySelector('.rechte-spalte input[type="number"]')?.focus();
    });
    
    await ladeGespeicherteHaertewerte();
});

// Tracking für expandierte Gruppen
const expandedGroups = ref({});

// Funktion zum Umschalten der Expansion
const toggleGruppe = (index) => {
    expandedGroups.value[index] = !expandedGroups.value[index];
};

// Durchschnitt der Härtewerte berechnen
const calculateAverage = (haertewerte) => {
    if (!haertewerte || haertewerte.length === 0) return 'N/A';
    
    const sum = haertewerte.reduce((total, hw) => {
        // Sicherstellen, dass es eine gültige Zahl ist
        const istWert = parseFloat(hw.ist_wert);
        return isNaN(istWert) ? total : total + istWert;
    }, 0);
    
    return (sum / haertewerte.length).toFixed(1);
};

// Löschen einer Härtewert-Gruppe
const deleteGroup = async (gruppe) => {
    try {
        // Bestätigung anfordern
        const result = await Swal.fire({
            title: 'Löschen bestätigen',
            text: `Möchten Sie alle Härtewerte für Auftrag ${gruppe.auftragsnummer} wirklich löschen?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ja, löschen',
            cancelButtonText: 'Abbrechen'
        });
        
        if (result.isConfirmed) {
            // Löschen aller Härtewerte in der Gruppe
            const haertewertIds = gruppe.haertewerte.map(hw => hw.id);
            
            // API-Aufruf zum Löschen
            const response = await axios.delete(route('haertewerte.destroy'), {
                data: { ids: haertewertIds }
            });
            
            if (response.data.success) {
                Swal.fire(
                    'Gelöscht!',
                    'Die Härtewerte wurden erfolgreich gelöscht.',
                    'success'
                );
                
                // Gespeicherte Härtewerte neu laden
                await ladeGespeicherteHaertewerte();
            }
        }
    } catch (error) {
        console.error('Fehler beim Löschen der Härtewerte:', error);
        Swal.fire(
            'Fehler',
            'Beim Löschen der Härtewerte ist ein Fehler aufgetreten.',
            'error'
        );
    }
};

// Löschen eines einzelnen Härtewerts
const deleteHaertewert = async (id) => {
    try {
        // Bestätigung anfordern
        const result = await Swal.fire({
            title: 'Löschen bestätigen',
            text: 'Möchten Sie diesen Härtewert wirklich löschen?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ja, löschen',
            cancelButtonText: 'Abbrechen'
        });
        
        if (result.isConfirmed) {
            // API-Aufruf zum Löschen
            const response = await axios.delete(route('haertewerte.destroy'), {
                data: { ids: [id] }
            });
            
            if (response.data.success) {
                Swal.fire(
                    'Gelöscht!',
                    'Der Härtewert wurde erfolgreich gelöscht.',
                    'success'
                );
                
                // Gespeicherte Härtewerte neu laden
                await ladeGespeicherteHaertewerte();
            }
        }
    } catch (error) {
        console.error('Fehler beim Löschen des Härtewerts:', error);
        Swal.fire(
            'Fehler',
            'Beim Löschen des Härtewerts ist ein Fehler aufgetreten.',
            'error'
        );
    }
};

// Aktualisiere die PDF-Exportfunktion, um Gruppenkommentar zu integrieren
const exportToPDF = async (gruppe) => {
    try {
        // PDF Generierungs-Logik
        const { jsPDF } = await import('jspdf');
        const { autoTable } = await import('jspdf-autotable');
        
        // Neues PDF im A4 Format erstellen
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // Heidelberg Blau Farbdefinitionen (Corporate Design)
        const colors = {
            primary: [0, 48, 98],         // Heidelberg Dunkelblau
            secondary: [0, 90, 170],      // Heidelberg Mittelblau
            tertiary: [100, 160, 200],    // Heidelberg Hellblau
            success: [39, 174, 96],       // Grün
            danger: [192, 57, 43],        // Rot
            warning: [255, 152, 0],       // Orange
            info: [0, 80, 140],           // Heidelberg Blau Variante
            dark: [33, 33, 33],           // Dunkelgrau
            light: [245, 245, 245],       // Hellgrau
            text: [68, 68, 68],           // Textfarbe
            lightText: [120, 120, 120]    // Helle Textfarbe
        };
        
        // Header mit Farbverlauf im Heidelberg Blau
        pdf.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        pdf.rect(0, 0, 210, 30, 'F');
        
        // Weißes Rechteck für Logo-Hintergrund
        pdf.setFillColor(255, 255, 255);
        pdf.roundedRect(5, 2.5, 35, 25, 3, 3, 'F');
        
        // Logo in den Header einfügen (links oben)
        // Verwende das lokal gespeicherte Heidelberger Logo
        const logoPath = '/images/HD_Logo_175.png';
        
        // Füge das Logo direkt in den Header ein
        pdf.addImage(logoPath, 'PNG', 10, 5, 25, 20);
        
        // Title ohne Datum
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(22);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Härtewertprüfung Protokoll', 105, 15, { align: 'center' });
        
        // Aktuelles Datum für das PDF
        const today = new Date();
        const dateStr = today.toLocaleDateString('de-DE');
        
        // Infobox für Auftragsdaten
        pdf.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        pdf.setFillColor(240, 245, 255); // Sehr helles Heidelberg Blau
        pdf.roundedRect(10, 35, 90, 40, 3, 3, 'FD');
        
        // Auftragsdaten
        pdf.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Auftragsdaten', 15, 43);
        
        pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.text(`Auftragsnummer:`, 15, 51);
        pdf.text(`Teilenummer:`, 15, 57);
        pdf.text(`Chargennummer:`, 15, 63);
        pdf.text(`Eisenmarke:`, 15, 69);
        
        pdf.setFont('helvetica', 'bold');
        pdf.text(`${gruppe.auftragsnummer}`, 50, 51);
        pdf.text(`${gruppe.teilenummer}`, 50, 57);
        pdf.text(`${gruppe.chargenummer}`, 50, 63);
        pdf.text(`${gruppe.eisenmarke || '-'}`, 50, 69);
        
        // Infobox für Sollwerte
        pdf.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        pdf.setFillColor(240, 245, 255); // Sehr helles Heidelberg Blau
        pdf.roundedRect(110, 35, 90, 40, 3, 3, 'FD');
        
        // Sollwerte - jetzt mit mehr Zeilen für die zusätzlichen Daten
        pdf.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Prüfdaten', 115, 43);
        
        pdf.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        
        // Bestimme Zeilenhöhen für mehr Inhalte
        const rowHeight = 5;
        let row = 1;
        
        pdf.text(`Sollwert von:`, 115, 43 + rowHeight * row);
        row++;
        pdf.text(`Sollwert bis:`, 115, 43 + rowHeight * row);
        row++;
        pdf.text(`Prüfverfahren:`, 115, 43 + rowHeight * row);
        row++;
        pdf.text(`Datum:`, 115, 43 + rowHeight * row);
        row++;
        pdf.text(`Geprüft von:`, 115, 43 + rowHeight * row);
        
        // Werte rechts einfügen
        pdf.setFont('helvetica', 'bold');
        row = 1;
        
        pdf.text(`${gruppe.haertewerte[0]?.soll_von || '-'}`, 150, 43 + rowHeight * row);
        row++;
        pdf.text(`${gruppe.haertewerte[0]?.soll_bis || '-'}`, 150, 43 + rowHeight * row);
        row++;
        pdf.text(`${gruppe.haertewerte[0]?.pruefverfahren || '-'}`, 150, 43 + rowHeight * row);
        row++;
        pdf.text(`${dateStr}`, 150, 43 + rowHeight * row);
        row++;
        pdf.text(`${gruppe.haertewerte[0]?.geprueft_von || '-'}`, 150, 43 + rowHeight * row);
        
        // Durchschnittswert mit visuellem Indikator
        const durchschnitt = calculateAverage(gruppe.haertewerte);
        const sollVon = parseFloat(gruppe.haertewerte[0]?.soll_von || 0);
        const sollBis = parseFloat(gruppe.haertewerte[0]?.soll_bis || 0);
        const durchschnittNum = parseFloat(durchschnitt);
        const istDurchschnittIO = !isNaN(durchschnittNum) && !isNaN(sollVon) && !isNaN(sollBis) && 
                               durchschnittNum >= sollVon && durchschnittNum <= sollBis;
        
        // Box für Durchschnittswert
        pdf.setDrawColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        pdf.setFillColor(
            ...(istDurchschnittIO 
                ? [colors.success[0] * 0.9, colors.success[1] * 0.9, colors.success[2] * 0.9]
                : [colors.danger[0] * 0.9, colors.danger[1] * 0.9, colors.danger[2] * 0.9])
        );
        pdf.roundedRect(110, 80, 90, 16, 3, 3, 'FD');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Durchschnitt Ø HBW:', 115, 89);
        pdf.setFontSize(12);
        pdf.text(`${durchschnitt}`, 175, 89, { align: 'right' });
        
        // IO/NIO Zusammenfassung
        const ioCount = gruppe.haertewerte.filter(hw => 
            isWertInRange(hw.ist_wert, hw.soll_von, hw.soll_bis)).length;
        const nioCount = gruppe.haertewerte.length - ioCount;
        const ioPercent = Math.round((ioCount / gruppe.haertewerte.length) * 100);
        
        // Box für IO/NIO Zusammenfassung
        pdf.setDrawColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        pdf.setFillColor(245, 245, 245);
        pdf.roundedRect(10, 80, 90, 16, 3, 3, 'FD');
        
        pdf.setTextColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Prüfergebnis:', 15, 89);
        
        // IO-Werte
        pdf.setTextColor(colors.success[0], colors.success[1], colors.success[2]);
        pdf.text(`IO: ${ioCount} (${ioPercent}%)`, 55, 89);
        
        // NIO-Werte
        pdf.setTextColor(colors.danger[0], colors.danger[1], colors.danger[2]);
        pdf.text(`NIO: ${nioCount}`, 85, 89);
        
        // Kommentar einfügen, wenn vorhanden
        if (gruppe.bemerkung) {
            pdf.setFillColor(colors.dark[0], colors.dark[1], colors.dark[2]);
            pdf.setFontSize(10);
            pdf.text(gruppe.bemerkung, 10, 100);
        }
        
        // Tabelle mit Härtewerten - jetzt mit Nr. Spalte
        autoTable(pdf, {
            startY: 105,
            head: [['Nr.', 'Position', 'Nest', 'Wanddicke (mm)', 'IST-Wert', 'Status']],
            body: gruppe.haertewerte.map((hw, index) => {
                const isIo = isWertInRange(hw.ist_wert, hw.soll_von, hw.soll_bis);
                return [
                    index + 1, // Zeilennummer
                    hw.pruefposition,
                    hw.nestnummer || '-',
                    hw.wanddicke ? hw.wanddicke + ' mm' : '-',
                    hw.ist_wert,
                    {
                        content: isIo ? 'IO' : 'NIO',
                        styles: {
                            halign: 'center',
                            fillColor: isIo ? [230, 246, 236] : [252, 232, 232],
                            textColor: isIo ? [39, 174, 96] : [192, 57, 43],
                            fontStyle: 'bold'
                        }
                    }
                ];
            }),
            headStyles: {
                fillColor: colors.primary,
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                halign: 'center',
                valign: 'middle'
            },
            styles: {
                fontSize: 10,
                cellPadding: 5,
                lineColor: [200, 200, 200],
                lineWidth: 0.1,
                halign: 'center', 
                valign: 'middle',
                overflow: 'linebreak'
            },
            alternateRowStyles: {
                fillColor: [248, 248, 248]
            },
            // Optimierte Spaltenbreiten für volle Seitenbreite
            columnStyles: {
                0: { cellWidth: '8%', halign: 'center' },        // Nr.
                1: { cellWidth: '22%', halign: 'center' },      // Position
                2: { cellWidth: '15%', halign: 'center' },      // Nest
                3: { cellWidth: '22%', halign: 'center' },      // Wanddicke
                4: { cellWidth: '18%', halign: 'center' },      // IST-Wert
                5: { cellWidth: '15%', halign: 'center' }       // Status
            },
            margin: { left: 10, right: 10 },
            tableWidth: '100%',
            didDrawPage: function (data) {
                // Seitennummer in der Fußzeile aktualisieren
                const currentPage = pdf.internal.getCurrentPageInfo().pageNumber;
                pdf.setPage(currentPage);
            },
            theme: 'grid',
            didParseCell: function(data) {
                // Tabellentitel hervorheben
                if (data.row.index === 0) {
                    data.cell.styles.fontStyle = 'bold';
                }
            }
        });
        
        // Fußzeile
        const pageCount = pdf.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            pdf.setPage(i);
            pdf.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
            pdf.rect(0, 280, 210, 17, 'F');
            
            pdf.setFontSize(9);
            pdf.setTextColor(255, 255, 255);
            pdf.setFont('helvetica', 'normal');
            pdf.text(`Seite ${i} von ${pageCount}`, 195, 287, { align: 'right' });
        }
        
        // Dateiname generieren
        const fileName = `Haertewert_${gruppe.auftragsnummer}_${gruppe.teilenummer}_${dateStr.replace(/\./g, '')}.pdf`;
        
        // PDF herunterladen
        pdf.save(fileName);
        
        // Erfolgsbenachrichtigung
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
        
        Toast.fire({
            icon: 'success',
            title: 'PDF wurde erfolgreich erstellt'
        });
        
    } catch (error) {
        console.error('Fehler beim PDF-Export:', error);
        Swal.fire(
            'Fehler',
            'Beim Erstellen des PDFs ist ein Fehler aufgetreten.',
            'error'
        );
    }
};

// Funktion zum Kopieren der Daten in das Formular
const copyToForm = (gruppe) => {
    if (!gruppe || !gruppe.haertewerte || gruppe.haertewerte.length === 0) {
        return;
    }
    
    // Erste Härtewert-Eintrag für die Referenzdaten
    const referenceEntry = gruppe.haertewerte[0];
    
    // Grundlegende Daten übernehmen
    form.teilenummer = gruppe.teilenummer;
    form.auftragsnummer = gruppe.auftragsnummer;
    form.chargenummer = gruppe.chargenummer;
    form.eisenmarke = referenceEntry.eisenmarke;
    form.soll_von = referenceEntry.soll_von;
    form.soll_bis = referenceEntry.soll_bis;
    form.geprueft_von = referenceEntry.geprueft_von;
    
    // Wenn Wanddicke vorhanden ist, diese übernehmen
    if (referenceEntry.wanddicke) {
        selectedWanddicke.value = referenceEntry.wanddicke + (referenceEntry.wanddicke.includes('mm') ? '' : ' mm');
        
        // Verfügbare Wanddicken für die gewählte Eisenmarke laden
        updateSollwerteFromEisenmarke().then(() => {
            // Nach dem Laden der Daten, sicherstellen dass die Wanddicke gesetzt ist
            nextTick(() => {
                selectedWanddicke.value = referenceEntry.wanddicke + (referenceEntry.wanddicke.includes('mm') ? '' : ' mm');
            });
        });
    }
    
    // Erfolgsbenachrichtigung anzeigen
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });
    
    Toast.fire({
        icon: 'success',
        title: 'Daten wurden ins Formular übernommen'
    });
    
    // Zum Formular scrollen
    document.querySelector('.rechte-spalte')?.scrollIntoView({ behavior: 'smooth' });
};

// Filter für die Verlaufsansicht
const searchQuery = ref('');
const filteredGruppierteDaten = computed(() => {
    if (!searchQuery.value.trim()) return gruppierteDaten.value;
    
    const search = searchQuery.value.toLowerCase().trim();
    
    return gruppierteDaten.value.filter(gruppe => {
        return (
            (gruppe.auftragsnummer || '').toLowerCase().includes(search) ||
            (gruppe.teilenummer || '').toLowerCase().includes(search) ||
            (gruppe.chargenummer || '').toLowerCase().includes(search) ||
            (gruppe.eisenmarke || '').toLowerCase().includes(search)
        );
    });
});

// Helferfunktion, um zu prüfen, ob ein String den Suchbegriff enthält
const containsSearchQuery = (text) => {
    if (!searchQuery.value || !text) return false;
    return text.toLowerCase().includes(searchQuery.value.toLowerCase().trim());
};

// HTML mit markierten Suchergebnissen
const highlightMatch = (text) => {
    if (!searchQuery.value.trim() || !text) return text;
    
    const regex = new RegExp(`(${searchQuery.value.trim()})`, 'gi');
    return text.replace(regex, '<span class="bg-yellow-200">$1</span>');
};

// Neue Funktion zum Speichern der temporären Härtewerte in localStorage
const saveToLocalStorage = () => {
    try {
        // Temporäre Härtewerte speichern
        localStorage.setItem(TEMP_HAERTEWERTE_KEY, JSON.stringify(temporaryHaertewerte.value));
        
        // Formular-Daten speichern
        const formData = {
            teilenummer: form.teilenummer,
            auftragsnummer: form.auftragsnummer,
            chargenummer: form.chargenummer,
            eisenmarke: form.eisenmarke,
            soll_von: form.soll_von,
            soll_bis: form.soll_bis,
            geprueft_von: form.geprueft_von
        };
        localStorage.setItem(FORM_DATA_KEY, JSON.stringify(formData));
        
        // Ausgewählte Wanddicke speichern
        localStorage.setItem(SELECTED_WANDDICKE_KEY, selectedWanddicke.value);
    } catch (error) {
        console.error('Fehler beim Speichern in localStorage:', error);
    }
};

// Neue Funktion zum Laden der temporären Härtewerte aus localStorage
const loadFromLocalStorage = () => {
    try {
        // Temporäre Härtewerte laden
        const savedHaertewerte = localStorage.getItem(TEMP_HAERTEWERTE_KEY);
        if (savedHaertewerte) {
            temporaryHaertewerte.value = JSON.parse(savedHaertewerte);
        }
        
        // Formular-Daten laden
        const savedFormData = localStorage.getItem(FORM_DATA_KEY);
        if (savedFormData) {
            const formData = JSON.parse(savedFormData);
            form.teilenummer = formData.teilenummer || '';
            form.auftragsnummer = formData.auftragsnummer || '';
            form.chargenummer = formData.chargenummer || '';
            form.eisenmarke = formData.eisenmarke || '';
            form.soll_von = formData.soll_von || '';
            form.soll_bis = formData.soll_bis || '';
            form.geprueft_von = formData.geprueft_von || 'A. Calhan';
        }
        
        // Ausgewählte Wanddicke laden
        const savedWanddicke = localStorage.getItem(SELECTED_WANDDICKE_KEY);
        if (savedWanddicke) {
            selectedWanddicke.value = savedWanddicke;
        }
        
        // Wenn Eisenmarke und Wanddicke geladen wurden, entsprechende Sollwerte laden,
        // aber das Modal nicht automatisch anzeigen
        if (form.eisenmarke) {
            // Zuerst die verfügbaren Wanddicken für diese Eisenmarke laden
            axios.get(route('haertewerte.sollwerte-by-eisenmarke', { 
                eisenmarke: form.eisenmarke 
            })).then(response => {
                if (response.data.success) {
                    verfuegbareWanddicken.value = response.data.data.wanddicken;
                    
                    // Wenn bereits eine Wanddicke ausgewählt ist und entsprechende Sollwerte existieren,
                    // diese setzen
                    if (selectedWanddicke.value && response.data.data.werte[selectedWanddicke.value]) {
                        const werte = response.data.data.werte[selectedWanddicke.value];
                        form.soll_von = werte.von;
                        form.soll_bis = werte.bis;
                    }
                }
            }).catch(error => {
                console.error('Fehler beim Abrufen der Sollwerte beim Laden:', error);
            });
        }
    } catch (error) {
        console.error('Fehler beim Laden aus localStorage:', error);
    }
};

// Funktion zum Löschen der Daten aus localStorage
const clearLocalStorage = () => {
    try {
        localStorage.removeItem(TEMP_HAERTEWERTE_KEY);
        localStorage.removeItem(FORM_DATA_KEY);
        localStorage.removeItem(SELECTED_WANDDICKE_KEY);
    } catch (error) {
        console.error('Fehler beim Löschen aus localStorage:', error);
    }
};

// Nach der Deklaration der variablen form und temporaryHaertewerte
// Beobachte Änderungen an den Formulardaten und speichere sie in localStorage
watch(
    () => [
        form.teilenummer,
        form.auftragsnummer, 
        form.chargenummer,
        form.eisenmarke,
        form.soll_von,
        form.soll_bis,
        form.geprueft_von
    ],
    () => {
        saveToLocalStorage();
    },
    { deep: true }
);

// Add this function before the onMounted lifecycle hook
const resetForm = () => {
    Swal.fire({
        title: 'Zurücksetzen bestätigen',
        text: 'Möchten Sie alle Auftragsdaten zurücksetzen?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ja, zurücksetzen',
        cancelButtonText: 'Abbrechen'
    }).then((result) => {
        if (result.isConfirmed) {
            // Auftragsdaten zurücksetzen
            form.reset();
            form.geprueft_von = 'A. Calhan'; // Standard-Wert wiederherstellen
            
            // Wanddicke zurücksetzen
            selectedWanddicke.value = '';
            verfuegbareWanddicken.value = [];
            
            // Bei Bedarf temporäre Härtewerte behalten oder löschen
            // Wenn sie auch gelöscht werden sollen, aktiviere den folgenden Code:
            // temporaryHaertewerte.value = [];
            
            // LocalStorage Daten löschen
            try {
                localStorage.removeItem(FORM_DATA_KEY);
                localStorage.removeItem(SELECTED_WANDDICKE_KEY);
                // Wenn auch die temporären Härtewerte gelöscht werden sollen:
                // localStorage.removeItem(TEMP_HAERTEWERTE_KEY);
            } catch (error) {
                console.error('Fehler beim Löschen der Daten aus dem LocalStorage:', error);
            }
            
            // Erfolgsbenachrichtigung
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
            
            Toast.fire({
                icon: 'success',
                title: 'Auftragsdaten wurden zurückgesetzt'
            });
        }
    });
};
</script>

<style>
.sticky {
    position: sticky;
    top: 1rem;
}

@media (max-width: 1023px) {
    .rechte-spalte {
        position: relative;
        margin-top: 1.5rem; /* Add approximately 5mm of space in responsive mode */
    }
}

@media (max-width: 767px) {
    .rechte-spalte {
        position: sticky;
        top: 0;
        z-index: 10;
        margin-bottom: 1rem;
        margin-top: 1.5rem; /* Keep the spacing in mobile view */
    }
    
    /* Prevent iOS keyboard from pushing the viewport */
    .rechte-spalte form {
        transform: translateZ(0);
    }
    
    /* Fix for iOS momentum scrolling issues */
    body, html {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Adjust input padding to accommodate the add button */
    input[type="number"] {
        padding-right: 3.5rem;
    }
    
    /* Make the measurement form more compact on mobile */
    .rechte-spalte .space-y-6 > div {
        margin-bottom: 0.75rem;
    }
    
    /* Improve tap targets for mobile */
    select, button, input {
        min-height: 2.75rem;
    }
    
    /* Optimize form layout when keyboard appears */
    .rechte-spalte {
        padding-bottom: 4rem;
    }
    
    /* Better spacing for SweetAlert on mobile */
    .swal2-popup {
        font-size: 0.85rem !important;
        padding: 0.5rem !important;
    }
}
</style>