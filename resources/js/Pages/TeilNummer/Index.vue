<template>
  <AppLayout>
    <div class="max-w-7xl mx-auto">
      <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-900">Teilenummer Verwaltung</h1>
        </div>

        <!-- Settings Card -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <h3 class="text-lg font-medium text-gray-900">Einstellungen</h3>
              <div class="flex items-center space-x-2">
                <label for="physicalExaminationCost" class="text-sm text-gray-600">
                  Kosten für physikalische Prüfung (€)
                </label>
                <input
                  type="number"
                  id="physicalExaminationCost"
                  v-model="physicalExaminationCost"
                  class="w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  step="0.01"
                  min="0"
                />
                <button
                  @click="updatePhysicalExaminationCost"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  :disabled="savingCost"
                >
                  {{ savingCost ? 'Speichern...' : 'Speichern' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Suchleiste und Import-Button -->
        <div class="flex flex-col md:flex-row justify-between mb-6 gap-4">
          <div class="relative w-full md:w-1/3">
            <div class="relative group">
              <input 
                v-model="search" 
                type="text" 
                placeholder="Teilenummer suchen..." 
                class="input input-bordered w-full pl-11 pr-10 bg-white rounded-full shadow-md transition-all duration-300 focus:shadow-lg focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none" 
                @input="debouncedSearch"
              />
              <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none text-gray-400">
                <i class="fas fa-search"></i>
              </div>
              <button 
                v-if="search" 
                @click="clearSearch" 
                class="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          
          <div class="flex gap-2">
            <button 
              @click="openSchrottpreisModal" 
              class="btn btn-accent"
            >
              <i class="fas fa-sync mr-2"></i>
              Schrottpreis aktualisieren
            </button>
            <button 
              @click="importFromRotekarten" 
              class="btn btn-primary"
              :disabled="importing"
            >
              <i class="fas fa-file-import mr-2"></i>
              {{ importing ? 'Importiere...' : 'Aus Rotekarten importieren' }}
            </button>
          </div>
        </div>

        <!-- Formular -->
        <div class="card bg-base-100 shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title mb-4">{{ form.id ? 'Teilenummer bearbeiten' : 'Neue Teilenummer anlegen' }}</h2>
            
            <div class="flex flex-wrap items-end gap-4">
              <div class="form-control flex-1 min-w-[200px]">
                <label class="label">
                  <span class="label-text">Teilenummer</span>
                </label>
                <input 
                  v-model="form.teil_nummer" 
                  type="text" 
                  placeholder="Teilenummer eingeben" 
                  class="input input-bordered w-full" 
                  :class="{ 'input-error': errors.teil_nummer }"
                />
                <label v-if="errors.teil_nummer" class="label">
                  <span class="label-text-alt text-error">{{ errors.teil_nummer }}</span>
                </label>
              </div>
              
              <div class="form-control w-24">
                <label class="label">
                  <span class="label-text">Preis (€)</span>
                </label>
                <input 
                  v-model.number="form.preis" 
                  type="number" 
                  step="0.01" 
                  min="0" 
                  placeholder="0.00" 
                  class="input input-bordered w-full" 
                  :class="{ 'input-error': errors.preis }"
                />
                <label v-if="errors.preis" class="label">
                  <span class="label-text-alt text-error">{{ errors.preis }}</span>
                </label>
              </div>
              
              <div class="form-control w-24">
                <label class="label">
                  <span class="label-text">Gewicht (kg)</span>
                </label>
                <input 
                  v-model.number="form.gewicht" 
                  type="number" 
                  step="0.001" 
                  min="0" 
                  placeholder="0.000" 
                  class="input input-bordered w-full" 
                  :class="{ 'input-error': errors.gewicht }"
                />
                <label v-if="errors.gewicht" class="label">
                  <span class="label-text-alt text-error">{{ errors.gewicht }}</span>
                </label>
              </div>
              
              <div class="form-control w-28">
                <label class="label">
                  <span class="label-text">Schrottpreis (€)</span>
                </label>
                <input 
                  v-model.number="form.schrott_preis" 
                  type="number" 
                  step="0.01" 
                  min="0" 
                  placeholder="0.00" 
                  class="input input-bordered w-full" 
                  :class="{ 'input-error': errors.schrott_preis }"
                />
                <label v-if="errors.schrott_preis" class="label">
                  <span class="label-text-alt text-error">{{ errors.schrott_preis }}</span>
                </label>
              </div>

              <div class="form-control flex-none">
                <button 
                  @click="saveTeilNummer" 
                  class="btn btn-primary h-12" 
                  :disabled="processing"
                >
                  {{ processing ? 'Speichern...' : (form.id ? 'Aktualisieren' : 'Speichern') }}
                </button>
              </div>
              
              <div v-if="form.id" class="form-control flex-none">
                <button 
                  @click="resetForm" 
                  class="btn btn-ghost h-12"
                >
                  Abbrechen
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Datentabelle -->
        <div class="overflow-x-auto bg-base-100 rounded-lg shadow">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th @click="sortBy('teil_nummer')" class="cursor-pointer">
                  Teilenummer
                  <i v-if="sortField === 'teil_nummer'" :class="[
                    'fas', 
                    sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down',
                    'ml-1'
                  ]"></i>
                </th>
                <th @click="sortBy('preis')" class="cursor-pointer">
                  Preis (€)
                  <i v-if="sortField === 'preis'" :class="[
                    'fas', 
                    sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down',
                    'ml-1'
                  ]"></i>
                </th>
                <th @click="sortBy('gewicht')" class="cursor-pointer">
                  Gewicht (kg)
                  <i v-if="sortField === 'gewicht'" :class="[
                    'fas', 
                    sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down', 
                    'ml-1'
                  ]"></i>
                </th>
                <th @click="sortBy('schrott_preis')" class="cursor-pointer">
                  Schrottpreis (€)
                  <i v-if="sortField === 'schrott_preis'" :class="[
                    'fas', 
                    sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down',
                    'ml-1'
                  ]"></i>
                </th>
                <th>Aktionen</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="teilnummern.data.length === 0">
                <td colspan="5" class="text-center py-4">
                  Keine Teilenummern gefunden
                </td>
              </tr>
              <tr v-for="item in teilnummern.data" :key="item.id">
                <td>{{ item.teil_nummer }}</td>
                <td>{{ item.preis }} €</td>
                <td>{{ item.gewicht }} kg</td>
                <td>{{ item.schrott_preis }} €</td>
                <td>
                  <div class="flex space-x-2">
                    <button @click="editTeilNummer(item)" class="btn btn-sm btn-square btn-ghost text-info">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button @click="confirmDelete(item)" class="btn btn-sm btn-square btn-ghost text-error">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- Paginierung -->
          <div class="flex justify-between items-center p-4">
            <div class="text-sm text-gray-500">
              Zeige {{ teilnummern.from }} bis {{ teilnummern.to }} von {{ teilnummern.total }} Einträgen
            </div>
            <div class="btn-group">
              <button 
                v-for="link in paginationLinks" 
                :key="link.label"
                @click="goToPage(link.url)" 
                class="btn btn-sm" 
                :class="{
                  'btn-active': link.active,
                  'btn-disabled': !link.url,
                }"
                v-html="link.translatedLabel"
              ></button>
            </div>
          </div>
        </div>

        <!-- Bestätigungsmodal für Löschen -->
        <div class="modal" :class="{ 'modal-open': showDeleteModal }">
          <div class="modal-box">
            <h3 class="font-bold text-lg">Teilenummer löschen</h3>
            <p class="py-4">
              Sind Sie sicher, dass Sie die Teilenummer <span class="font-semibold">{{ itemToDelete?.teil_nummer }}</span> löschen möchten?
            </p>
            <div class="modal-action">
              <button @click="cancelDelete" class="btn btn-ghost">Abbrechen</button>
              <button @click="deleteTeilNummer" class="btn btn-error" :disabled="processing">
                {{ processing ? 'Löschen...' : 'Löschen' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Toast Notification -->
        <div class="fixed bottom-4 right-4 z-50" v-if="toast.show">
          <div :class="['alert shadow-lg max-w-md', `alert-${toast.type}`]">
            <div class="flex items-start">
              <div class="mr-2 mt-0.5">
                <i v-if="toast.type === 'success'" class="fas fa-check-circle"></i>
                <i v-else-if="toast.type === 'error'" class="fas fa-exclamation-circle"></i>
                <i v-else-if="toast.type === 'info'" class="fas fa-info-circle"></i>
                <i v-else class="fas fa-bell"></i>
              </div>
              <div class="flex-1">
                <p class="font-medium">{{ toast.message }}</p>
                <p v-if="toast.details" class="text-sm mt-1">{{ toast.details }}</p>
              </div>
              <button @click="toast.show = false" class="btn btn-sm btn-ghost">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Schrottpreis-Modal -->
        <div class="modal" :class="{ 'modal-open': showSchrottpreisModal }">
          <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">Schrottpreis aktualisieren</h3>
            
            <div class="mb-6">
              <p class="mb-2">Wählen Sie, wie der aktuelle Schrottpreis ermittelt werden soll:</p>
              <div class="flex gap-4 mt-4">
                <button 
                  @click="loadSchrottpreis"
                  class="btn btn-primary flex-1"
                  :disabled="loadingSchrottpreis"
                >
                  <i class="fas fa-cloud-download-alt mr-2"></i>
                  {{ loadingSchrottpreis ? 'Wird geladen...' : 'Online laden' }}
                </button>
                <button 
                  @click="manualInputMode = true"
                  class="btn btn-secondary flex-1"
                >
                  <i class="fas fa-edit mr-2"></i>
                  Manuell eingeben
                </button>
              </div>
            </div>
            
            <div class="mb-4" v-if="manualInputMode">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">Schrottpreis manuell eingeben (€ pro kg)</span>
                </label>
                <input
                  v-model.number="currentSchrottpreis"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  class="input input-bordered"
                />
              </div>
            </div>
            
            <div v-if="loadedSchrottpreis !== null || manualInputMode" class="mt-6">
              <div class="mb-4">
                <h4 class="font-semibold mb-2">Schrottpreis anwenden auf:</h4>
                <div class="grid grid-cols-2 gap-4">
                  <div class="form-control">
                    <label class="label cursor-pointer">
                      <span class="label-text">Alle Teilenummern</span>
                      <input type="radio" v-model="applyTo" value="all" class="radio radio-primary" />
                    </label>
                  </div>
                  <div class="form-control">
                    <label class="label cursor-pointer">
                      <span class="label-text">Ausgewählte Teilenummer</span>
                      <input type="radio" v-model="applyTo" value="selected" class="radio radio-primary" />
                    </label>
                  </div>
                </div>
              </div>
              
              <div v-if="applyTo === 'selected'" class="form-control mb-4">
                <label class="label">
                  <span class="label-text">Teilenummer auswählen</span>
                </label>
                <select v-model="selectedTeilnummer" class="select select-bordered w-full">
                  <option :value="null">Bitte auswählen</option>
                  <option v-for="item in teilnummern.data" :key="item.id" :value="item.id">
                    {{ item.teil_nummer }}
                  </option>
                </select>
              </div>
              
              <div class="alert alert-info mb-4" v-if="loadedSchrottpreis !== null && !manualInputMode">
                <div>
                  <i class="fas fa-info-circle mr-2"></i>
                  <div>
                    <p>Geladener aktueller Schrottpreis: <span class="font-bold">{{ loadedSchrottpreis }} €/kg</span></p>
                    <p class="text-xs mt-1">Quelle: {{ schrottpreisSource }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="modal-action">
              <button @click="closeSchrottpreisModal" class="btn btn-ghost">Abbrechen</button>
              <button 
                @click="applySchrottpreis" 
                class="btn btn-primary" 
                :disabled="!canApplySchrottpreis || updating"
              >
                {{ updating ? 'Wird angewendet...' : 'Schrottpreis anwenden' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { router, useForm, usePage } from '@inertiajs/vue3';
import debounce from 'lodash/debounce';
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';

// Props und Daten aus dem usePage-Hook
const { flash } = usePage().props;

// Formulardaten
const form = useForm({
  id: null,
  teil_nummer: '',
  preis: 0,
  gewicht: 0,
  schrott_preis: 0,
});

// Übersetzte Paginierungslinks
const paginationLinks = computed(() => {
  if (!teilnummern.value || !teilnummern.value.links) return [];
  
  return teilnummern.value.links.map(link => {
    // Kopie des Link-Objekts erstellen
    const translatedLink = { ...link };
    
    // Label übersetzen
    if (link.label === 'pagination.previous') {
      translatedLink.translatedLabel = 'Zurück';
    } else if (link.label === 'pagination.next') {
      translatedLink.translatedLabel = 'Weiter';
    } else {
      translatedLink.translatedLabel = link.label;
    }
    
    return translatedLink;
  });
});

// Tabellendaten
const teilnummern = ref({
  data: [],
  links: [],
  from: 0,
  to: 0,
  total: 0,
});

// Zustände
const processing = ref(false);
const importing = ref(false);
const search = ref('');
const sortField = ref('teil_nummer');
const sortDirection = ref('asc');
const showDeleteModal = ref(false);
const itemToDelete = ref(null);
const errors = reactive({});

// Schrottpreis-Zustände
const showSchrottpreisModal = ref(false);
const loadingSchrottpreis = ref(false);
const updating = ref(false);
const manualInputMode = ref(false);
const loadedSchrottpreis = ref(null);
const currentSchrottpreis = ref(0);
const applyTo = ref('all');
const selectedTeilnummer = ref(null);
const schrottpreisSource = ref('');

// Computed property, um zu prüfen, ob der Schrottpreis angewendet werden kann
const canApplySchrottpreis = computed(() => {
  if (applyTo.value === 'all') {
    return (loadedSchrottpreis.value !== null || (manualInputMode.value && currentSchrottpreis.value > 0));
  } else {
    return (loadedSchrottpreis.value !== null || (manualInputMode.value && currentSchrottpreis.value > 0)) && 
           selectedTeilnummer.value !== null;
  }
});

// Toast Notification
const toast = ref({ 
  show: false, 
  message: '',
  type: 'success', // success, error, warning, info
  details: ''
});

const showToast = (message, type = 'success', details = '') => {
  toast.value = { show: true, message, type, details };
  setTimeout(() => {
    toast.value.show = false;
  }, 5000); // Längere Anzeigezeit für bessere Lesbarkeit
};

// Daten laden
const loadData = (page = 1) => {
  router.visit(
    route('teilnummer.index'), 
    {
      only: ['teilnummern'],
      preserveState: true,
      data: {
        page,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value,
      },
      onSuccess: (page) => {
        teilnummern.value = page.props.teilnummern;
      }
    }
  );
};

// Suche löschen
const clearSearch = () => {
  search.value = '';
  debouncedSearch();
};

// Debounced Suche
const debouncedSearch = debounce(() => {
  loadData();
}, 300);

// Sortierung
const sortBy = (field) => {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  loadData();
};

// Seitenwechsel in der Paginierung
const goToPage = (url) => {
  if (!url) return;
  
  const urlObj = new URL(url);
  const page = urlObj.searchParams.get('page');
  loadData(page);
};

// Neuen Datensatz speichern oder bestehenden aktualisieren
const saveTeilNummer = () => {
  processing.value = true;
  
  if (form.id) {
    // Update mit Axios
    axios.put(`/teilnummer/${form.id}`, {
      teil_nummer: form.teil_nummer,
      preis: form.preis,
      gewicht: form.gewicht,
      schrott_preis: form.schrott_preis
    })
    .then(() => {
      resetForm();
      // Direkter API-Aufruf für sofortige Aktualisierung
      axios.get(route('teilnummer.index', {
        page: 1,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
      }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        teilnummern.value = response.data.teilnummern;
        showToast('Teilenummer wurde erfolgreich aktualisiert.', 'success', `ID: ${form.id}`);
      });
      processing.value = false;
    })
    .catch((error) => {
      if (error.response && error.response.data && error.response.data.errors) {
        // Alle alten Fehler löschen
        Object.keys(errors).forEach(key => delete errors[key]);
        
        // Neue Fehler hinzufügen
        const responseErrors = error.response.data.errors;
        Object.keys(responseErrors).forEach(key => {
          errors[key] = responseErrors[key][0]; // Wir nehmen die erste Fehlermeldung
        });
      }
      processing.value = false;
      showToast('Fehler beim Aktualisieren der Teilenummer.', 'error');
    });
  } else {
    // Neu anlegen mit Axios statt Inertia Form
    axios.post(route('teilnummer.store'), {
      teil_nummer: form.teil_nummer,
      preis: form.preis,
      gewicht: form.gewicht,
      schrott_preis: form.schrott_preis
    })
    .then(() => {
      resetForm();
      // Direkter API-Aufruf für sofortige Aktualisierung
      axios.get(route('teilnummer.index', {
        page: 1,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
      }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        teilnummern.value = response.data.teilnummern;
        showToast('Neue Teilenummer wurde erfolgreich gespeichert.', 'success', `Teil-Nr: ${form.teil_nummer}`);
      });
      processing.value = false;
    })
    .catch((error) => {
      if (error.response && error.response.data && error.response.data.errors) {
        // Alle alten Fehler löschen
        Object.keys(errors).forEach(key => delete errors[key]);
        
        // Neue Fehler hinzufügen
        const responseErrors = error.response.data.errors;
        Object.keys(responseErrors).forEach(key => {
          errors[key] = responseErrors[key][0]; // Wir nehmen die erste Fehlermeldung
        });
      }
      processing.value = false;
      showToast('Fehler beim Speichern der neuen Teilenummer.', 'error');
    });
  }
};

// Teilenummer zum Bearbeiten laden
const editTeilNummer = (item) => {
  form.id = item.id;
  form.teil_nummer = item.teil_nummer;
  form.preis = item.preis;
  form.gewicht = item.gewicht;
  form.schrott_preis = item.schrott_preis;
  
  // Nach oben zum Formular scrollen
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// Formular zurücksetzen
const resetForm = () => {
  form.reset();
  form.clearErrors();
  Object.keys(errors).forEach(key => delete errors[key]);
};

// Bestätigungsdialog für Löschen anzeigen
const confirmDelete = (item) => {
  itemToDelete.value = item;
  showDeleteModal.value = true;
};

// Löschen abbrechen
const cancelDelete = () => {
  itemToDelete.value = null;
  showDeleteModal.value = false;
};

// Teilenummer löschen
const deleteTeilNummer = () => {
  if (!itemToDelete.value) return;
  
  processing.value = true;
  
  // Verwende Axios direkt, um die DELETE-Anfrage zu senden
  axios.delete(`/teilnummer/${itemToDelete.value.id}`)
    .then(() => {
      showDeleteModal.value = false;
      itemToDelete.value = null;
      
      // Direkter API-Aufruf für sofortige Aktualisierung
      axios.get(route('teilnummer.index', {
        page: 1,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
      }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        teilnummern.value = response.data.teilnummern;
        showToast('Teilenummer wurde erfolgreich gelöscht.', 'success', `Teil-Nr: ${itemToDelete.value.teil_nummer}`);
      });
      
      processing.value = false;
    })
    .catch(() => {
      processing.value = false;
      showToast('Fehler beim Löschen der Teilenummer.', 'error');
    });
};

// Import aus Rotekarten
const importFromRotekarten = () => {
  importing.value = true;
  
  // Direkter Axios-Request anstelle des Inertia-Routers
  axios.post(route('teilnummer.import'))
    .then((response) => {
      // Detaillierte Informationen aus der Antwort extrahieren
      const details = response.data.details || {};
      const importedCount = details.imported || 0;
      const skippedCount = details.skipped || 0;
      const missingCount = details.missing || 0;
      const importedTeilNummern = details.importedTeilNummern || [];
      
      // Direkter API-Aufruf für sofortige Aktualisierung
      axios.get(route('teilnummer.index', {
        page: 1,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
      }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        teilnummern.value = response.data.teilnummern;
        
        // Spezifischere Detailanzeige basierend auf den Import-Ergebnissen
        let detailsText = '';
        if (importedCount > 0) {
          detailsText = `${importedCount} importiert, ${skippedCount} übersprungen, ${missingCount} fehlend`;
          if (importedTeilNummern.length > 0) {
            const displayTeilNummern = importedTeilNummern.slice(0, 3);
            detailsText += `\nImportierte Teilenummern: ${displayTeilNummern.join(', ')}`;
            if (importedTeilNummern.length > 3) {
              detailsText += ` und ${importedTeilNummern.length - 3} weitere`;
            }
          }
          showToast('Teilenummern wurden erfolgreich importiert.', 'success', detailsText);
        } else if (skippedCount > 0 && importedCount === 0) {
          detailsText = `Alle gefundenen Teilenummern (${skippedCount}) existieren bereits in der Datenbank.`;
          showToast('Keine neuen Teilenummern zum Importieren gefunden.', 'info', detailsText);
        } else if (missingCount > 0 && importedCount === 0 && skippedCount === 0) {
          detailsText = `${missingCount} Rotekarten ohne gültige Teilenummern gefunden.`;
          showToast('Keine Teilenummern zum Importieren gefunden.', 'warning', detailsText);
        } else {
          showToast('Keine neuen Teilenummern zum Importieren gefunden.', 'info');
        }
      });
      importing.value = false;
    })
    .catch((error) => {
      importing.value = false;
      const errorMessage = error.response?.data?.message || 'Fehler beim Importieren der Teilenummern aus Rotekarten.';
      showToast(errorMessage, 'error');
    });
};

// Schrottpreis-Modal öffnen
const openSchrottpreisModal = () => {
  showSchrottpreisModal.value = true;
  manualInputMode.value = false;
  loadedSchrottpreis.value = null;
  currentSchrottpreis.value = 0;
  applyTo.value = 'all';
  selectedTeilnummer.value = null;
};

// Schrottpreis-Modal schließen
const closeSchrottpreisModal = () => {
  showSchrottpreisModal.value = false;
};

// Aktuellen Schrottpreis von einer API laden
const loadSchrottpreis = async () => {
  loadingSchrottpreis.value = true;
  
  try {
    const response = await axios.get(route('teilnummer.get-schrottpreis'));
    
    if (response.data.success) {
      loadedSchrottpreis.value = response.data.preis;
      currentSchrottpreis.value = response.data.preis;
      schrottpreisSource.value = response.data.source || 'Metallpreisdatenbank';
      manualInputMode.value = false;
    } else {
      showToast('Der Schrottpreis konnte nicht geladen werden.', 'error', response.data.message || 'Unbekannter Fehler');
      manualInputMode.value = true;
    }
  } catch (error) {
    console.error('Fehler beim Laden des Schrottpreises:', error);
    showToast(
      'Der Schrottpreis konnte nicht geladen werden.', 
      'error', 
      'Bitte geben Sie den Preis manuell ein.'
    );
    manualInputMode.value = true;
  } finally {
    loadingSchrottpreis.value = false;
  }
};

// Schrottpreis anwenden
const applySchrottpreis = async () => {
  updating.value = true;
  
  try {
    // Bestimmen, welcher Preis verwendet werden soll
    const preis = manualInputMode.value ? currentSchrottpreis.value : loadedSchrottpreis.value;
    
    // API-Endpunkt aufrufen
    const response = await axios.post(route('teilnummer.update-schrottpreis'), {
      preis,
      apply_to: applyTo.value,
      teil_nummer_id: applyTo.value === 'selected' ? selectedTeilnummer.value : null
    });
    
    if (response.data.success) {
      // Daten neu laden
      axios.get(route('teilnummer.index', {
        page: 1,
        search: search.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
      }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json'
        }
      })
      .then(response => {
        teilnummern.value = response.data.teilnummern;
        
        let detailMsg = '';
        if (applyTo.value === 'all') {
          detailMsg = `${response.data.updated || 0} Teilenummern aktualisiert`;
        } else {
          const selectedItem = teilnummern.value.data.find(item => item.id === selectedTeilnummer.value);
          detailMsg = `Teilenummer: ${selectedItem?.teil_nummer || ''}`;
        }
        
        showToast('Schrottpreis wurde erfolgreich aktualisiert.', 'success', detailMsg);
        closeSchrottpreisModal();
      });
    } else {
      showToast('Fehler beim Aktualisieren des Schrottpreises.', 'error', response.data.message || 'Unbekannter Fehler');
    }
  } catch (error) {
    console.error('Fehler beim Aktualisieren des Schrottpreises:', error);
    showToast('Fehler beim Aktualisieren des Schrottpreises.', 'error');
  } finally {
    updating.value = false;
  }
};

// Beim Mounten der Komponente Daten laden
onMounted(() => {
  // Verwenden der Inertia-Daten, die bereits in der Seite enthalten sind
  teilnummern.value = usePage().props.teilnummern;
});

const physicalExaminationCost = ref('600.00');
const savingCost = ref(false);

// Load the current physical examination cost
onMounted(async () => {
    try {
        const response = await axios.get(route('settings.get', { key: 'physical_examination_cost' }));
        if (response.data && response.data.value) {
            physicalExaminationCost.value = response.data.value;
        }
    } catch (error) {
        console.error('Error loading physical examination cost:', error);
        showToast('Fehler beim Laden der Kosten für physikalische Prüfung', 'error');
    }
});

// Update the physical examination cost
const updatePhysicalExaminationCost = async () => {
    if (!physicalExaminationCost.value || isNaN(parseFloat(physicalExaminationCost.value))) {
        showToast('Bitte geben Sie einen gültigen Wert ein', 'error');
        return;
    }

    savingCost.value = true;
    try {
        const response = await axios.post(route('settings.update'), {
            key: 'physical_examination_cost',
            value: physicalExaminationCost.value.toString()
        });
        
        if (response.data && response.data.message) {
            showToast('Kosten für physikalische Prüfung wurden aktualisiert', 'success');
        }
    } catch (error) {
        console.error('Error updating physical examination cost:', error);
        const errorMessage = error.response?.data?.error || 'Fehler beim Aktualisieren der Kosten';
        showToast(errorMessage, 'error');
    } finally {
        savingCost.value = false;
    }
};
</script> 