<script setup>
import { ref, onMounted } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, router, Link } from '@inertiajs/vue3';
import Swal from 'sweetalert2';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: false,
        default: null
    }
});

const message = ref('');
const messageType = ref('');

const showMessage = (text, type = 'success') => {
    message.value = text;
    messageType.value = type;
    setTimeout(() => {
        message.value = '';
        messageType.value = '';
    }, 3000);
};

const form = useForm({
    rotekarte_id: props.rotekarte?.id || null,
    gussnachbehandlung_daten: {
        verantwortlicher: '',
        datum: '',
        bemerkungen: '',
        system_info: {
            current: props.rotekarte?.gussnachbehandlung_daten?.system_info?.current || {
                username: '',
                hostname: '',
                timestamp: ''
            },
            history: props.rotekarte?.gussnachbehandlung_daten?.system_info?.history || []
        }
    }
});

const captureSystemInfo = async () => {
    try {
        const response = await fetch('/system-info', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.username || data.hostname) {
            form.gussnachbehandlung_daten.system_info.current = {
                username: data.username || 'Nicht verfügbar',
                hostname: data.hostname || 'Nicht verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        } else {
            form.gussnachbehandlung_daten.system_info.current = {
                username: 'Keine Daten verfügbar',
                hostname: 'Keine Daten verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        }
    } catch (error) {
        form.gussnachbehandlung_daten.system_info.current = {
            username: 'Systemfehler',
            hostname: 'Systemfehler',
            timestamp: new Date().toLocaleString('de-DE')
        };
    }
};

onMounted(() => {
    captureSystemInfo();
});

const validateForm = () => {
    const requiredFields = {
        'Verantwortlicher': form.gussnachbehandlung_daten.verantwortlicher,
        'Datum': form.gussnachbehandlung_daten.datum
    };

    const missingFields = Object.entries(requiredFields)
        .filter(([_, value]) => !value)
        .map(([key]) => key);

    if (missingFields.length > 0) {
        Swal.fire({
            icon: 'error',
            title: 'Fehlende Pflichtfelder',
            text: `Bitte füllen Sie folgende Felder aus: ${missingFields.join(', ')}`,
            showConfirmButton: true
        });
        return false;
    }
    return true;
};

const submit = () => {
    if (!validateForm()) {
        return;
    }

    const currentInfo = {
        username: form.gussnachbehandlung_daten.system_info.current.username,
        hostname: form.gussnachbehandlung_daten.system_info.current.hostname,
        timestamp: new Date().toLocaleString('de-DE')
    };

    if (!Array.isArray(form.gussnachbehandlung_daten.system_info.history)) {
        form.gussnachbehandlung_daten.system_info.history = [];
    }

    form.gussnachbehandlung_daten.system_info.history.push(currentInfo);

    form.post(route('gussnachbehandlung.store'), {
        onSuccess: () => {
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich gespeichert',
                text: 'Die Gussnachbehandlung wurde erfolgreich gespeichert.',
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                window.location.href = route('dashboard');
            });
        },
        onError: (errors) => {
            console.error('Fehler beim Speichern:', errors);
            let errorMessage = 'Bitte überprüfen Sie die folgenden Felder:\n\n';
            
            Object.entries(errors).forEach(([field, messages]) => {
                const fieldName = field.split('.').pop();
                errorMessage += `${fieldName}: ${messages.join(', ')}\n`;
            });

            Swal.fire({
                icon: 'error',
                title: 'Fehler beim Speichern',
                text: errorMessage,
                showConfirmButton: true
            });
        }
    });
};

const handleKeyDown = (event, nextFieldId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (nextFieldId === 'saveButton') {
            submit();
        } else {
            document.getElementById(nextFieldId)?.focus();
        }
    }
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div v-if="rotekarte.gussnachbehandlung_daten" class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="rounded-md bg-yellow-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Formular bereits ausgefüllt
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>
                                    ie Daten zur Gussnachbehandlung für diese Rotekarte wurden bereits erfasst. Ihr könnt die erfassten Informationen in der Übersicht einsehen.
                                </p>
                            </div>
                            <div class="mt-4">
                                <div class="-mx-2 -my-1.5 flex">
                                    <Link
                                        :href="route('rotekarte.show', rotekarte.id)"
                                        class="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                                    >
                                        Zur Übersicht
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <!-- Original form content -->
                <div class="max-w-7xl mx-auto">
                    <!-- Header Card -->
                    <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                        <div class="flex justify-between items-center">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">
                                    Gussnachbehandlung
                                    <span v-if="props.rotekarte?.id" class="text-lg font-medium text-gray-500 ml-2">
                                        (Rotekarte Nr. {{ props.rotekarte.id }})
                                    </span>
                                </h1>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <div v-if="message" :class="{
                        'mb-6 p-4 rounded-lg border': true,
                        'bg-green-50 text-green-800 border-green-400': messageType === 'success',
                        'bg-red-50 text-red-800 border-red-400': messageType === 'error'
                    }">
                        {{ message }}
                    </div>

                    <!-- Main Content -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column -->
                        <div class="lg:col-span-2">
                            <!-- Basis Informationen Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-6">Basis Informationen</h2>
                                <div class="grid grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Verantwortlicher</label>
                                        <input type="text" v-model="form.gussnachbehandlung_daten.verantwortlicher"
                                            @keydown="handleKeyDown($event, 'datum')"
                                            id="verantwortlicher"
                                            placeholder="Name eingeben"
                                            :class="[
                                                'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                                !form.gussnachbehandlung_daten.verantwortlicher ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.gussnachbehandlung_daten.verantwortlicher" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Hey, bitte gib deinen Namen ein 😊</p>
                                        </div>
                                        <p v-if="form.errors['gussnachbehandlung_daten.verantwortlicher']" class="mt-1 text-sm text-red-600">
                                            {{ form.errors['gussnachbehandlung_daten.verantwortlicher'] }}
                                        </p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Datum</label>
                                        <input type="date" v-model="form.gussnachbehandlung_daten.datum"
                                            @keydown="handleKeyDown($event, 'uhrzeit')"
                                            id="datum"
                                            :class="[
                                                'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                                !form.gussnachbehandlung_daten.datum ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.gussnachbehandlung_daten.datum" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Wähle bitte ein Datum aus 📅</p>
                                        </div>
                                        <p v-if="form.errors['gussnachbehandlung_daten.datum']" class="mt-1 text-sm text-red-600">
                                            {{ form.errors['gussnachbehandlung_daten.datum'] }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Bemerkungen Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Bemerkungen</h2>
                                <textarea v-model="form.gussnachbehandlung_daten.bemerkungen"
                                    @keydown="handleKeyDown($event, 'saveButton')"
                                    id="bemerkungen"
                                    rows="3"
                                    placeholder="Bemerkungen eingeben"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                <p v-if="form.errors['gussnachbehandlung_daten.bemerkungen']" class="mt-1 text-sm text-red-600">
                                    {{ form.errors['gussnachbehandlung_daten.bemerkungen'] }}
                                </p>

                                <!-- System Information -->
                                <div class="mt-6">
                                    <h3 class="text-sm font-medium text-gray-900">System Information</h3>
                                    <div class="mt-2 space-y-1">
                                        <p class="text-sm text-gray-600">
                                            <span class="font-medium">Benutzer:</span>
                                            {{ form.gussnachbehandlung_daten.system_info.current.username }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6 space-y-3">
                                <button type="submit"
                                    @click="submit"
                                    :disabled="form.processing"
                                    class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    {{ form.processing ? 'Wird gespeichert...' : 'Gussnachbehandlung speichern' }}
                                </button>
                                <Link :href="route('dashboard')"
                                    class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Abbrechen
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
