<script setup>
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch, computed, onMounted } from 'vue';
import debounce from 'lodash/debounce';
import { router } from '@inertiajs/vue3';
import HBWTableModal from '@/Components/HBWTableModal.vue';
import TeileModal from '@/Components/TeileModal.vue';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement } from 'chart.js';
import { Doughnut, Bar } from 'vue-chartjs';

ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);

const props = defineProps({
    rotekarten: {
        type: Object,
        required: true
    },
    filters: {
        type: Object,
        default: () => ({
            search: '',
            status: '',
            abteilung: '',
            date_from: '',
            date_to: ''
        })
    },
    totalCounts: {
        type: Object,
        required: true
    },
    qsPrueferStats: {
        type: Array,
        required: true
    }
});

// Dashboard Statistiken
const statistics = computed(() => {
    return {
        total: props.totalCounts.total,
        offen: props.totalCounts.offen,
        inBearbeitung: props.totalCounts.inBearbeitung,
        abgeschlossen: props.totalCounts.abgeschlossen,
        kleinguss: props.totalCounts.kleinguss,
        grossguss: props.totalCounts.grossguss
    };
});

// Chart Data für Status-Verteilung
const statusChartData = computed(() => ({
    labels: ['Offen', 'In Bearbeitung', 'Abgeschlossen'],
    datasets: [{
        data: [statistics.value.offen, statistics.value.inBearbeitung, statistics.value.abgeschlossen],
        backgroundColor: ['#FCD34D', '#60A5FA', '#34D399'],
        borderWidth: 0
    }]
}));

// Chart Data für Abteilungsverteilung
const abteilungChartData = computed(() => ({
    labels: ['Kleinguss', 'Großguss'],
    datasets: [{
        data: [statistics.value.kleinguss, statistics.value.grossguss],
        backgroundColor: ['#8B5CF6', '#EC4899'],
        borderWidth: 0
    }]
}));

// Chart Data für QS-Prüfer-Verteilung
const qsPrueferChartData = computed(() => ({
    labels: props.qsPrueferStats.map(stat => stat.name),
    datasets: [{
        data: props.qsPrueferStats.map(stat => stat.anzahl),
        backgroundColor: [
            '#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#3B82F6',
            '#6366F1', '#14B8A6', '#84CC16', '#EF4444', '#06B6D4'
        ],
        borderWidth: 0
    }]
}));

const qsPrueferChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                boxWidth: 12,
                padding: 15
            }
        },
        tooltip: {
            callbacks: {
                label: (context) => {
                    const value = context.raw;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${context.label}: ${value} (${percentage}%)`;
                }
            }
        },
        datalabels: {
            color: '#fff',
            font: {
                weight: 'bold',
                size: 11
            },
            formatter: (value, context) => {
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((value / total) * 100).toFixed(0);
                return `${percentage}%`;
            }
        }
    }
};

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom'
        }
    }
};

const search = ref(props.filters.search);
const selectedStatus = ref(props.filters.status);
const selectedAbteilung = ref(props.filters.abteilung);
const dateFrom = ref(props.filters.date_from);
const dateTo = ref(props.filters.date_to);

const statusOptions = [
    { value: '', label: 'Alle Status' },
    { value: 'Offen', label: 'Offen' },
    { value: 'In Bearbeitung', label: 'In Bearbeitung' },
    { value: 'Abgeschlossen', label: 'Abgeschlossen' },
    //{ value: 'Abgelehnt', label: 'Abgelehnt' }
];

const abteilungOptions = [
    { value: '', label: 'Alle Abteilungen' },
    { value: 'NG', label: 'Kleinguss' },
    { value: 'GG', label: 'Großguss' },
    { value: 'HF', label: 'Handformerei' }
];

const debouncedSearch = debounce(() => {
    router.get(route('dashboard'), {
        search: search.value,
        status: selectedStatus.value,
        abteilung: selectedAbteilung.value,
        date_from: dateFrom.value,
        date_to: dateTo.value
    }, {
        preserveState: true,
        preserveScroll: true,
        replace: true
    });
}, 300);

const clearSearch = () => {
    search.value = '';
    debouncedSearch();
};

watch([search, selectedStatus, selectedAbteilung, dateFrom, dateTo], () => {
    debouncedSearch();
});

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
};

const resetFilters = () => {
    search.value = '';
    selectedStatus.value = '';
    selectedAbteilung.value = '';
    dateFrom.value = '';
    dateTo.value = '';

    router.get(route('dashboard'), {}, { preserveState: true, preserveScroll: true });
};

const showHBWTable = ref(false);
const showTeileModal = ref(false);
const selectedTeile = ref([]);

const openTeileModal = (teile) => {
    selectedTeile.value = teile || [];
    showTeileModal.value = true;
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto">
            <!-- Header Card -->
            <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <div class="flex items-center gap-4 mb-4">
                            
                            <h1 class="text-2xl font-bold text-gray-900">Rotekarten Übersicht – Alles im Blick</h1>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Behalte hier alle Rotekarten im Blick und verwalte sie ganz einfach!</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button
                            @click="showHBWTable = true"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 3v18h18"></path>
                                <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                            </svg>
                            HBW-Tabelle
                        </button>
                        <Link
                            :href="route('spektrometer.index')"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Neue Rotekarte erstellen
                        </Link>
                    </div>
                </div>

                <!-- Statistik-Karten -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <!-- Gesamtanzahl -->
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-600">Gesamt Rotekarten</h2>
                                <p class="text-2xl font-semibold text-gray-900">{{ statistics.total }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Offene Karten -->
                    <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4">
                        <Link
                            :href="route('offene-karten-druck')"
                            class="flex items-center hover:bg-yellow-200 transition-colors p-2 rounded-lg"
                        >
                            <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-600">Offene Karten</h2>
                                <p class="text-2xl font-semibold text-gray-900">{{ statistics.offen }}</p>
                            </div>
                        </Link>
                    </div>

                    <!-- In Bearbeitung -->
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4">
                        <Link
                            :href="route('in-bearbeitung-karten-druck')"
                            class="flex items-center hover:bg-purple-200 transition-colors p-2 rounded-lg"
                        >
                            <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-600">In Bearbeitung</h2>
                                <p class="text-2xl font-semibold text-gray-900">{{ statistics.inBearbeitung }}</p>
                            </div>
                        </Link>
                    </div>

                    <!-- Abgeschlossen -->
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h2 class="text-sm font-medium text-gray-600">Abgeschlossen</h2>
                                <p class="text-2xl font-semibold text-gray-900">{{ statistics.abgeschlossen }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Status-Verteilung -->
                    <div class="bg-white rounded-lg shadow p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Status-Verteilung</h3>
                        <div class="h-64">
                            <Doughnut :data="statusChartData" :options="chartOptions" />
                        </div>
                    </div>

                    <!-- Abteilungs-Verteilung -->
                    <div class="bg-white rounded-lg shadow p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Abteilungs-Verteilung</h3>
                        <div class="h-64">
                            <Doughnut :data="abteilungChartData" :options="chartOptions" />
                        </div>
                    </div>

                    <!-- QS-Prüfer-Verteilung -->
                    <div class="bg-white rounded-lg shadow p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">QS-Prüfer-Verteilung</h3>
                        <div class="h-64">
                            <Doughnut 
                                :data="qsPrueferChartData" 
                                :options="qsPrueferChartOptions" 
                            />
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-500 text-center">
                                Gesamt QS-Prüfungen: {{ props.qsPrueferStats.reduce((sum, stat) => sum + stat.anzahl, 0) }}
                            </p>
                        </div>
                    </div>
                </div>

                <HBWTableModal
                    :show="showHBWTable"
                    @close="showHBWTable = false"
                />

                <!-- Search and Filter Section -->
                <div class="space-y-4">
                    <!-- Search Field -->
                    <div class="relative flex-1 mb-4">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="text"
                                v-model="search"
                                class="block w-full pl-10 pr-12 py-2.5 text-sm border border-gray-300 rounded-lg bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-150 ease-in-out shadow-sm hover:border-gray-400"
                                placeholder="Suche nach ID, Name, Chargennummer, Teilenummer..."
                            >
                            <button
                                v-if="search"
                                @click="clearSearch"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer text-gray-400 hover:text-gray-600"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Status Filter -->
                        <div>
                            <div class="flex justify-between items-center">
                                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                            </div>
                            <select
                                id="status"
                                v-model="selectedStatus"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                                <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <!-- Abteilung Filter -->
                        <div>
                            <label for="abteilung" class="block text-sm font-medium text-gray-700">Abteilung</label>
                            <select
                                id="abteilung"
                                v-model="selectedAbteilung"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                                <option v-for="option in abteilungOptions" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>

                        <!-- Date Range Filter -->
                        <div>
                            <label for="date-from" class="block text-sm font-medium text-gray-700">Datum von</label>
                            <input
                                type="date"
                                id="date-from"
                                v-model="dateFrom"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                        </div>

                        <div>
                            <label for="date-to" class="block text-sm font-medium text-gray-700">Datum bis</label>
                            <input
                                type="date"
                                id="date-to"
                                v-model="dateTo"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                        </div>
                    </div>

                    <!-- Reset Button -->
                    <div class="flex justify-end mt-4">
                        <button
                            @click="resetFilters"
                            class="text-sm text-gray-500 hover:text-gray-700"
                        >
                            Filter zurücksetzen
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="bg-white rounded-lg shadow-sm">
                <div v-if="rotekarten.data.length === 0" class="p-6">
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Keine Rotekarten</h3>
                        <p class="mt-1 text-sm text-gray-500">Leg einfach mit einer neuen Rotekarte los!</p>
                    </div>
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basis-Info</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-64">Aktionen</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="rotekarte in rotekarten.data" :key="rotekarte.id" :class="{
                                'bg-yellow-50': rotekarte.type === 'yellow',
                                'bg-red-50': rotekarte.type === 'red' && rotekarte.qs_daten?.status === 'Abgeschlossen',
                                'hover:bg-gray-50': !(rotekarte.type === 'yellow' || (rotekarte.type === 'red' && rotekarte.qs_daten?.status === 'Abgeschlossen'))
                            }">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    RT-{{ rotekarte.id }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="space-y-4">
                                        <!-- Spektrometer Data Section -->
                                        <div v-if="rotekarte.spektrometer_daten" class="space-y-2">
                                            <div class="grid grid-cols-2 gap-4">
                                                <!-- Name und Uhrzeit -->
                                                <div class="space-y-2">
                                                    <div>
                                                        <span class="text-xs text-gray-500">Element</span>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            {{ rotekarte.spektrometer_daten?.analysewerte?.[0]?.element }}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span class="text-xs text-gray-500">Uhrzeit</span>
                                                        <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.uhrzeit }}</p>
                                                    </div>
                                                </div>

                                                <!-- Ist-Wert und Datum -->
                                                <div class="space-y-2">
                                                    <div>
                                                        <span class="text-xs text-gray-500">Ist-Wert</span>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            {{ rotekarte.spektrometer_daten?.analysewerte?.[0]?.istWert }}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span class="text-xs text-gray-500">Datum</span>
                                                        <p class="text-sm font-medium text-gray-900">{{ formatDate(rotekarte.spektrometer_daten.datum) }}</p>
                                                    </div>
                                                </div>

                                                <!-- Chargennummer -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Chargennummer</span>
                                                    <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.chargennummer }}</p>
                                                </div>

                                                <!-- QS Abteilung -->
                                                <div>
                                                    <span class="text-xs text-gray-500">QS Abteilung</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ ['GG', 'HF'].includes(rotekarte.spektrometer_daten.abteilung) ? 'Großguss' : 'Kleinguss' }}
                                                    </p>
                                                </div>

                                                <!-- Eisenmarke -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Eisenmarke</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ rotekarte.spektrometer_daten.eisenmarke }}
                                                    </p>
                                                </div>

                                                <!-- SAP-Zahlnummer -->
                                                <div>
                                                    <span class="text-xs text-gray-500">SAP-Zahlnummer</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ rotekarte.formanlage_daten?.sap_zahlnummer }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- QS Data Section -->
                                        <div v-if="rotekarte.qs_daten" class="space-y-2 border-t pt-2">
                                            <div class="grid grid-cols-4 gap-4">
                                                <!-- Serie Status -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Abnahmebeauftragter</span>
                                                    <p class="text-sm font-medium mt-1" :class="{
                                                        'text-orange-600': rotekarte.qs_daten.serie_status === 'Entscheidung ausstehend',
                                                        'text-green-600': rotekarte.qs_daten.serie_status === 'Teile in die Serie einreihen',
                                                        'text-red-600': rotekarte.qs_daten.serie_status === 'Zur Verschrottung freigegeben'
                                                    }">{{ rotekarte.qs_daten.serie_status }}</p>
                                                </div>

                                                <!-- Status und Vorschlag Prüfungen -->
                                                <div class="col-span-1">
                                                    <span class="text-xs text-gray-500">QS-Status</span>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <span :class="{
                                                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                                                            'bg-yellow-100 text-yellow-800': rotekarte.qs_daten?.status === 'In Bearbeitung',
                                                            'bg-green-100 text-green-800': rotekarte.qs_daten?.status === 'Abgeschlossen',
                                                            'bg-red-100 text-red-800': rotekarte.qs_daten?.status === 'Abgelehnt'
                                                        }">
                                                            {{ rotekarte.qs_daten?.status }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Maßnahmen -->
                                                <div class="col-span-1">
                                                    <span class="text-xs text-gray-500">Maßnahmen</span>
                                                    <div class="flex items-center mt-1">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                            </svg>
                                                            {{ rotekarte.qs_daten?.vorschlag_pruefungen || 'Keine' }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Erstellt am -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Erstellt am</span>
                                                    <p class="text-sm font-medium text-gray-900 mt-1">
                                                        {{ new Date(rotekarte.created_at).toLocaleString('de-DE') }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-2">
                                        <Link
                                            :href="route('rotekarte.show', rotekarte.id)"
                                            class="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            Details
                                        </Link>

                                        <!-- Formanlage Link -->
                                        <Link
                                            v-if="!rotekarte.formanlage_daten"
                                            :href="route('formanlage.index', { rotekarte: rotekarte.id })"
                                            class="inline-flex items-center text-sm text-green-600 hover:text-green-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                            Zur Formanlage
                                        </Link>

                                        <!-- Gussnachbehandlung Link -->
                                        <Link
                                            v-if="rotekarte.formanlage_daten && !rotekarte.gussnachbehandlung_daten"
                                            :href="route('gussnachbehandlung.index', { rotekarte: rotekarte.id })"
                                            class="inline-flex items-center text-sm text-yellow-600 hover:text-yellow-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2M7 7h10" />
                                            </svg>
                                            Zur Gussnachbehandlung
                                        </Link>

                                        <!-- QS Links -->
                                        <template v-if="rotekarte.formanlage_daten">
                                            <Link
                                                v-if="!rotekarte.qs_daten"
                                                :href="route('qs.index', { rotekarte: rotekarte.id })"
                                                class="inline-flex items-center text-sm text-purple-600 hover:text-purple-900"
                                            >
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                </svg>
                                                Zum QS
                                            </Link>
                                            <Link
                                                v-else
                                                :href="route('qs.index', { rotekarte: rotekarte.id })"
                                                class="inline-flex items-center text-sm text-purple-600 hover:text-purple-900"
                                            >
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                QS Bearbeiten
                                            </Link>
                                        </template>
                                        <!-- Gelbe Karte Link -->
                                        <a v-if="rotekarte.type === 'yellow'"
                                            :href="`/storage/GelbeKarte/Gelbkarte-${rotekarte.id}.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            PDF Gelbe Karte
                                        </a>
                                        <!-- PDF Link -->
                                        <a
                                            v-if="rotekarte.formanlage_daten"
                                            :href="`/storage/rotekarten/rotekarte-${rotekarte.id}.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            PDF Rotekarte 1
                                        </a>
                                        <!-- Final PDF Rotekarte Link -->
                                        <a
                                            v-if="rotekarte.status === 'Abgeschlossen'"
                                            :href="`/storage/RotekarteFinal/Rotekarte-${rotekarte.id}-Final.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mt-2"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            PDF Rotekarte Final
                                        </a>

                                        <!-- Teile anzeigen Button -->
                                        <button
                                            v-if="rotekarte.qs_daten?.status === 'Abgeschlossen' ? rotekarte.qs_daten?.teile : rotekarte.formanlage_daten?.teile"
                                            @click="openTeileModal(rotekarte.qs_daten?.status === 'Abgeschlossen' ? rotekarte.qs_daten.teile : rotekarte.formanlage_daten.teile)"
                                            class="inline-flex items-center text-sm text-blue-600 hover:text-blue-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                                            </svg>
                                            {{ rotekarte.qs_daten?.status === 'Abgeschlossen' ? 'Teile anzeigen' : 'Teile anzeigen' }}
                                        </button>
                                                                                <!-- Prüfmethoden ermitteln Button -->
                                                                                <Link
                                            v-if="rotekarte.spektrometer_daten?.analysewerte?.length > 0"
                                            :href="route('rotekarte.show', rotekarte.id) + '?openAiModal=true'"
                                            class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-md text-white text-xs font-medium shadow-sm hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150 mt-2"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                            Prüfmethoden ermitteln
                                        </Link>

                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="px-6 py-4 bg-white border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <Link
                                    v-if="rotekarten.prev_page_url"
                                    :href="rotekarten.prev_page_url"
                                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Zurück
                                </Link>
                                <Link
                                    v-if="rotekarten.next_page_url"
                                    :href="rotekarten.next_page_url"
                                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    Weiter
                                </Link>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Zeige
                                        <span class="font-medium">{{ rotekarten.from }}</span>
                                        bis
                                        <span class="font-medium">{{ rotekarten.to }}</span>
                                        von
                                        <span class="font-medium">{{ rotekarten.total }}</span>
                                        Ergebnissen
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <Link
                                            v-if="rotekarten.prev_page_url"
                                            :href="rotekarten.prev_page_url"
                                            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            &laquo; Zurück
                                        </Link>
                                        <Link
                                            v-if="rotekarten.next_page_url"
                                            :href="rotekarten.next_page_url"
                                            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                        >
                                            Weiter &raquo;
                                        </Link>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TeileModal component -->
            <TeileModal
                :show="showTeileModal"
                :teile="selectedTeile"
                @close="showTeileModal = false"
            />
        </div>
    </AppLayout>
</template>
