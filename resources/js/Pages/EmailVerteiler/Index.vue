<script setup>
import { ref, computed } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';

const props = defineProps({
    verteiler: Array
});

const showModal = ref(false);
const editingEmail = ref(null);

const form = useForm({
    email: '',
    name: '',
    type: 'statistik',
    is_cc: false
});

const typeLabels = {
    'statistik': 'Statistik',
    'management': 'Management',
    'kg': 'Kleinguss (NG)',
    'hf': 'Handformerei',
    'qs_gg': 'QS Großguss',
    'formanlage': 'Formanlage',
    'spektrometer': 'Spektrometer',
    'gussnachbehandlung': 'Gussnachbehandlung'
};

const groupedVerteiler = computed(() => {
    const grouped = {};
    props.verteiler.forEach(email => {
        if (!grouped[email.type]) {
            grouped[email.type] = [];
        }
        grouped[email.type].push(email);
    });
    return grouped;
});

const openModal = (email = null) => {
    editingEmail.value = email;
    if (email) {
        form.email = email.email;
        form.name = email.name;
        form.type = email.type;
        form.is_cc = email.is_cc;
    } else {
        form.reset();
    }
    showModal.value = true;
};

const closeModal = () => {
    showModal.value = false;
    editingEmail.value = null;
    form.reset();
};

const submit = () => {
    if (editingEmail.value) {
        form.put(`/email-verteiler/${editingEmail.value.id}`, {
            onSuccess: () => closeModal()
        });
    } else {
        form.post('/email-verteiler', {
            onSuccess: () => closeModal()
        });
    }
};

const deleteEmail = (email) => {
    if (confirm(`Möchten Sie die E-Mail-Adresse "${email.email}" wirklich löschen?`)) {
        form.delete(`/email-verteiler/${email.id}`);
    }
};
</script>

<template>
    <AppLayout title="E-Mail-Verteiler">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">📧 E-Mail-Verteiler Verwaltung</h2>
                            <button 
                                @click="openModal()"
                                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200"
                            >
                                ➕ Neue E-Mail hinzufügen
                            </button>
                        </div>

                        <!-- Verteiler nach Typ gruppiert -->
                        <div class="space-y-8">
                            <div v-for="(emails, type) in groupedVerteiler" :key="type" class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    {{ typeLabels[type] || type }}
                                    <span class="text-sm font-normal text-gray-500">({{ emails.length }} E-Mails)</span>
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div 
                                        v-for="email in emails" 
                                        :key="email.id"
                                        class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
                                    >
                                        <div class="flex justify-between items-start mb-2">
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-900">{{ email.name }}</h4>
                                                <p class="text-sm text-gray-600">{{ email.email }}</p>
                                                <span v-if="email.is_cc" class="inline-block mt-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                                    CC
                                                </span>
                                            </div>
                                            <div class="flex gap-2">
                                                <button 
                                                    @click="openModal(email)"
                                                    class="text-blue-600 hover:text-blue-800 text-sm"
                                                    title="Bearbeiten"
                                                >
                                                    ✏️
                                                </button>
                                                <button 
                                                    @click="deleteEmail(email)"
                                                    class="text-red-600 hover:text-red-800 text-sm"
                                                    title="Löschen"
                                                >
                                                    🗑️
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal -->
        <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            {{ editingEmail ? 'E-Mail bearbeiten' : 'Neue E-Mail hinzufügen' }}
                        </h3>
                        <button 
                            @click="closeModal"
                            class="text-gray-400 hover:text-gray-600 text-xl"
                        >
                            ×
                        </button>
                    </div>
                    
                    <form @submit.prevent="submit" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                            <input 
                                type="text" 
                                v-model="form.name"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required
                            >
                            <div v-if="form.errors.name" class="text-red-600 text-sm mt-1">{{ form.errors.name }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">E-Mail-Adresse</label>
                            <input 
                                type="email" 
                                v-model="form.email"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required
                            >
                            <div v-if="form.errors.email" class="text-red-600 text-sm mt-1">{{ form.errors.email }}</div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Typ</label>
                            <select 
                                v-model="form.type"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required
                            >
                                <option v-for="(label, value) in typeLabels" :key="value" :value="value">
                                    {{ label }}
                                </option>
                            </select>
                            <div v-if="form.errors.type" class="text-red-600 text-sm mt-1">{{ form.errors.type }}</div>
                        </div>
                        
                        <div class="flex items-center">
                            <input 
                                type="checkbox" 
                                id="is_cc"
                                v-model="form.is_cc"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <label for="is_cc" class="ml-2 block text-sm text-gray-700">
                                Als CC (Kopie) versenden
                            </label>
                        </div>
                        
                        <div class="flex justify-end gap-3 mt-6">
                            <button 
                                type="button"
                                @click="closeModal"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors duration-200"
                            >
                                Abbrechen
                            </button>
                            <button 
                                type="submit"
                                :disabled="form.processing"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50"
                            >
                                {{ form.processing ? 'Speichern...' : (editingEmail ? 'Aktualisieren' : 'Hinzufügen') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
