<script setup>
import { ref, computed } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link, usePage } from '@inertiajs/vue3';

defineProps({
    roteKarten: {
        type: Array,
        default: () => []
    }
});

const page = usePage();
const currentPath = computed(() => page.url);

const navigationTabs = [
    { name: 'Spektrometer', route: 'spektrometer' },
    { name: 'Formanlage', route: 'formanlage' },
    { name: 'Gussnachbehandlung', route: 'gussnachbehandlung' },
    { name: 'QS Kleinguss & QS Großguss', route: 'qs' },
    { name: 'Einstellungen', route: 'einstellungen' }
];

const tabs = ref([
    { name: 'Alle', value: 'alle' },
    { name: 'Spektrometer', value: 'spektrometer' },
    { name: 'Formanlage', value: 'formanlage' },
    { name: 'Gussnachbehandlung', value: 'gussnachbehandlung' },
    { name: 'QS', value: 'qs' }
]);

const currentTab = ref('alle');

const getStatusColor = (status) => {
    switch(status) {
        case 'Offen':
            return 'bg-yellow-100 text-yellow-800';
        case 'In Bearbeitung':
            return 'bg-blue-100 text-blue-800';
        case 'Abgeschlossen':
            return 'bg-green-100 text-green-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const isCurrentRoute = (route) => {
    return currentPath.value === '/' + route;
};
</script>

<template>
    <AppLayout>
        <div class="bg-white rounded-lg shadow">
            <!-- Header mit Aktionsbuttons -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">Rote Karten Übersicht</h2>
                    <div class="space-x-4">
                        <Link :href="route('spektrometer')"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            Neue Rote Karte
                        </Link>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button v-for="tab in tabs"
                        :key="tab.value"
                        @click="currentTab = tab.value"
                        :class="[
                            currentTab === tab.value
                                ? 'border-indigo-500 text-indigo-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                            'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
                        ]">
                        {{ tab.name }}
                    </button>
                </nav>
            </div>

            <!-- Tabelle -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Typ
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Datum
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Verantwortlicher
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aktionen
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="karte in roteKarten" :key="karte.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ karte.typ }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ karte.datum }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ karte.verantwortlicher }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span :class="[
                                    getStatusColor(karte.status),
                                    'px-2 inline-flex text-xs leading-5 font-semibold rounded-full'
                                ]">
                                    {{ karte.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex space-x-3">
                                    <Link :href="route('rotekarte.edit', karte.id)"
                                        class="text-indigo-600 hover:text-indigo-900">
                                        Bearbeiten
                                    </Link>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </AppLayout>
</template>
