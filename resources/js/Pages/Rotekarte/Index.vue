<script setup>
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

defineProps({
    rotekarten: {
        type: Object,
        required: true
    }
});
</script>

<template>
    <AppLayout>
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h2 class="text-2xl font-bold mb-4">Rotekarten Übersicht – Alles im Blick</h2>

                    <!-- Rotekarten Tabelle -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        ID
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Erstellt am
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Details
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Aktionen
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="rotekarte in rotekarten.data" :key="rotekarte.id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ rotekarte.id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                            :class="{
                                                'bg-green-100 text-green-800': rotekarte.status === 'completed',
                                                'bg-yellow-100 text-yellow-800': rotekarte.status === 'in_progress',
                                                'bg-blue-100 text-blue-800': rotekarte.status === 'spectrometer_complete'
                                            }">
                                            {{ rotekarte.status }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ new Date(rotekarte.created_at).toLocaleString('de-DE') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div v-if="rotekarte.spektrometer_daten">
                                            <p>Name: {{ rotekarte.spektrometer_daten.name }}</p>
                                            <p>Chargennummer: {{ rotekarte.spektrometer_daten.chargennummer }}</p>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <Link :href="route('rotekarte.show', rotekarte.id)"
                                            class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            Anzeigen
                                        </Link>
                                        <Link :href="route('rotekarte.edit', rotekarte.id)"
                                            class="text-green-600 hover:text-green-900">
                                            Bearbeiten
                                        </Link>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4" v-if="rotekarten.links && rotekarten.links.length > 3">
                        <div class="flex justify-between">
                            <Link v-for="link in rotekarten.links"
                                :key="link.label"
                                :href="link.url"
                                v-html="link.label"
                                class="px-3 py-1 bg-white border rounded"
                                :class="{
                                    'bg-indigo-50 text-indigo-600': link.active,
                                    'text-gray-700': !link.active,
                                    'opacity-50 cursor-not-allowed': !link.url,
                                    'hover:bg-gray-50': link.url
                                }"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
