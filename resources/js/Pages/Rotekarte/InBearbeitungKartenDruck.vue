<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import moment from 'moment';

// Props für die in Bearbeitung befindlichen Rotekarten vom Server
const props = defineProps({
    inBearbeitungKarten: {
        type: Array,
        required: true
    }
});

// Automatischer Druck, wenn gewünscht
const autoPrint = ref(false);

// Formatiere das Datum in deutsches Format
const formatDate = (date) => {
    return moment(date).format('DD.MM.YYYY');
};

// Formatiere die Abteilung in einen lesbaren Namen
const formatDepartment = (dept) => {
    if (dept === 'NG') {
        return 'Nassgussanlage';
    } else if (dept === 'GG') {
        return 'Großgussanlage';
    } else if (dept === 'HF') {
        return 'Handformerei';
    }
    return dept;
};

// Drucke die aktuelle Seite
const printPage = () => {
    window.print();
};

// PDF herunterladen
const downloadPdf = async () => {
    try {
        // Hole die PDF vom Server
        const response = await fetch('/in-bearbeitung-karten-pdf');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // PDF als Blob erhalten
        const pdfBlob = await response.blob();
        
        // Aktuelle Datum für Dateinamen
        const today = new Date();
        const dateStr = today.toISOString().split('T')[0]; // YYYY-MM-DD format
        const pdfFileName = `In_Bearbeitung_Rotekarten_${dateStr}.pdf`;
        
        // PDF herunterladen
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(pdfBlob);
        downloadLink.download = pdfFileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    } catch (error) {
        console.error('Fehler beim Herunterladen der PDF:', error);
        alert('Beim Herunterladen der PDF ist ein Fehler aufgetreten.');
    }
};

// Count cards by department
const nassgussAnlageCount = props.inBearbeitungKarten.filter(karte => 
    karte.spektrometer_daten && karte.spektrometer_daten.abteilung === 'NG'
).length;

const grossgussAnlageCount = props.inBearbeitungKarten.filter(karte => 
    karte.spektrometer_daten && karte.spektrometer_daten.abteilung === 'GG'
).length;

const handformereiCount = props.inBearbeitungKarten.filter(karte => 
    karte.spektrometer_daten && karte.spektrometer_daten.abteilung === 'HF'
).length;

onMounted(() => {
    // Prüfe, ob ein Parameter für automatischen Druck vorhanden ist
    const urlParams = new URLSearchParams(window.location.search);
    const autoPrint = urlParams.get('print');
    
    if (autoPrint === 'true') {
        // Kurze Verzögerung für das Laden der Seite
        setTimeout(() => {
            printPage();
        }, 500);
    }
});
</script>

<template>
    <Head title="In Bearbeitung Rotekarten - Druckansicht" />
    
    <AppLayout>
        <div class="max-w-8xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Header mit Aktionsbuttons -->
            <div class="flex justify-between items-center mb-8 print:hidden">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 tracking-tight">In Bearbeitung Rotekarten</h1>
                    <p class="mt-2 text-base text-gray-600">Übersicht aller in Bearbeitung befindlichen Fertigungsaufträge</p>
                </div>
                <div class="flex space-x-4">
                    <button @click="printPage" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        Drucken
                    </button>
                    <button @click="downloadPdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        PDF herunterladen
                    </button>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 print:hidden">
                <!-- Status-Übersicht -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                    <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
                        <h2 class="text-lg font-semibold text-gray-800">Übersicht</h2>
                    </div>
                    <div class="px-6 py-5 flex items-center">
                        <div class="flex-1 text-center">
                            <div class="text-3xl font-bold text-purple-700">{{ props.inBearbeitungKarten.length }}</div>
                            <div class="text-sm text-gray-600 mt-1">In Bearbeitung Karten</div>
                        </div>
                        <div class="h-10 w-px bg-gray-200 mx-4"></div>
                        <div class="flex-1 text-center">
                            <div class="text-sm text-gray-600">Stand</div>
                            <div class="mt-1 font-medium">{{ new Date().toLocaleDateString('de-DE') }}</div>
                        </div>
                        <div class="h-10 w-px bg-gray-200 mx-4"></div>
                        <div class="flex-1 text-center">
                            <div class="text-sm text-gray-600">Zeit</div>
                            <div class="mt-1 font-medium">{{ new Date().toLocaleTimeString('de-DE') }}</div>
                        </div>
                    </div>
                </div>

                <!-- Abteilungs-Verteilung -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                    <div class="px-6 py-5 border-b border-gray-200 bg-gray-50">
                        <h2 class="text-lg font-semibold text-gray-800">Abteilungs-Verteilung</h2>
                    </div>
                    <div class="px-6 py-5 flex items-center">
                        <div class="flex-1 text-center">
                            <div class="text-3xl font-bold text-blue-600">
                                {{ nassgussAnlageCount }}
                            </div>
                            <div class="text-sm text-gray-600 mt-1">Nassgussanlage</div>
                        </div>
                        <div class="h-10 w-px bg-gray-200 mx-4"></div>
                        <div class="flex-1 text-center">
                            <div class="text-3xl font-bold text-green-600">
                                {{ grossgussAnlageCount }}
                            </div>
                            <div class="text-sm text-gray-600 mt-1">Großgussanlage</div>
                        </div>
                        <div class="h-10 w-px bg-gray-200 mx-4"></div>
                        <div class="flex-1 text-center">
                            <div class="text-3xl font-bold text-purple-600">
                                {{ handformereiCount }}
                            </div>
                            <div class="text-sm text-gray-600 mt-1">Handformerei</div>
                        </div>
                    </div>
                </div>
            </div>
                
            <!-- Printable content -->
            <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200 print:shadow-none print:border-0">
                <div class="px-6 py-5 border-b border-gray-200 bg-gray-50 print:border-b-2 print:border-black print:bg-white">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-xl font-bold text-gray-800 print:text-2xl">In Bearbeitung Rotekarten: {{ props.inBearbeitungKarten.length }}</h2>
                            <p class="text-sm text-gray-500 print:text-gray-700 mt-1">Stand: {{ new Date().toLocaleDateString('de-DE') }} {{ new Date().toLocaleTimeString('de-DE') }}</p>
                        </div>
                        <div class="print:hidden">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                In Bearbeitung: {{ props.inBearbeitungKarten.length }}
                            </span>
                        </div>
                        <!-- Logo für den Druck -->
                        <div class="hidden print:block print:absolute print:top-10 print:right-10">
                            <img src="/images/logo.png" alt="Logo" class="h-16 w-16" />
                        </div>
                    </div>
                </div>
                
                <!-- Rotekarten Liste -->
                <div class="divide-y divide-gray-200 print:divide-gray-400">
                    <div v-for="(karte, index) in props.inBearbeitungKarten" :key="karte.id" class="p-6 hover:bg-gray-50 print:hover:bg-white print:page-break-inside-avoid border-l-4 border-transparent"
                         :class="{
                            'border-l-blue-500': karte.spektrometer_daten?.abteilung === 'NG',
                            'border-l-green-500': karte.spektrometer_daten?.abteilung === 'GG',
                            'border-l-purple-500': karte.spektrometer_daten?.abteilung === 'HF'
                         }">
                        <div class="flex flex-col sm:flex-row justify-between mb-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 flex items-center justify-center rounded-full"
                                         :class="{
                                             'bg-blue-100 text-blue-800': karte.spektrometer_daten?.abteilung === 'NG',
                                             'bg-green-100 text-green-800': karte.spektrometer_daten?.abteilung === 'GG',
                                             'bg-purple-100 text-purple-800': karte.spektrometer_daten?.abteilung === 'HF'
                                         }">
                                        <span class="text-xl font-bold">{{ karte.id }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-bold text-gray-900 flex items-center">
                                        Rotekarte #{{ karte.id }}
                                        <span class="ml-3 px-2.5 py-0.5 rounded-full text-xs font-semibold"
                                              :class="{
                                                  'bg-blue-100 text-blue-800': karte.spektrometer_daten?.abteilung === 'NG',
                                                  'bg-green-100 text-green-800': karte.spektrometer_daten?.abteilung === 'GG',
                                                  'bg-purple-100 text-purple-800': karte.spektrometer_daten?.abteilung === 'HF'
                                              }">
                                            {{ formatDepartment(karte.spektrometer_daten?.abteilung) }}
                                        </span>
                                    </h3>
                                    <p class="text-sm text-gray-500">Erstellt am: {{ formatDate(karte.created_at) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Spektrometer Daten -->
                        <div class="mt-5 border border-gray-100 rounded-lg pt-4 px-4 pb-2 bg-gray-50 print:border print:border-gray-300 print:bg-white">
                            <h4 class="text-base font-bold text-gray-800 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                Spektrometer Daten
                            </h4>
                            <div class="mt-3 grid grid-cols-1 sm:grid-cols-5 gap-4">
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Name</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.spektrometer_daten?.name || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Chargennummer</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.spektrometer_daten?.chargennummer || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Eisenmarke</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.spektrometer_daten?.eisenmarke || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Datum/Zeit</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">
                                        {{ karte.spektrometer_daten?.datum || '-' }} 
                                        {{ karte.spektrometer_daten?.uhrzeit || '' }}
                                    </p>
                                </div>
                                <div v-if="karte.spektrometer_daten?.fehlercode" class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Fehlercode</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.spektrometer_daten?.fehlercode }}</p>
                                </div>
                            </div>
                            
                            <!-- Spektrometer Analysewerte -->
                            <div v-if="karte.spektrometer_daten?.analysewerte && karte.spektrometer_daten.analysewerte.length > 0" class="mt-4">
                                <h5 class="text-sm font-bold text-gray-700 flex items-center mb-3">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                    Analysewerte
                                </h5>
                                <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Element</th>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ist-Wert</th>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Soll-Wert</th>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toleranz</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="(analyse, aIndex) in karte.spektrometer_daten.analysewerte" :key="aIndex"
                                                :class="{
                                                    'bg-red-50': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) > 0.05,
                                                    'bg-yellow-50': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) > 0.03 && Math.abs(parseFloat(analyse.toleranz)) <= 0.05,
                                                    'bg-green-50': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) <= 0.03
                                                }">
                                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ analyse.element }}
                                                </td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ analyse.istWert }}
                                                </td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                                                    {{ analyse.sollWert }}
                                                </td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium"
                                                    :class="{
                                                        'text-red-600': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) > 0.05,
                                                        'text-yellow-600': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) > 0.03 && Math.abs(parseFloat(analyse.toleranz)) <= 0.05,
                                                        'text-green-600': analyse.toleranz && Math.abs(parseFloat(analyse.toleranz)) <= 0.03
                                                    }">
                                                    {{ analyse.toleranz || '-' }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Formanlage Daten -->
                        <div v-if="karte.formanlage_daten" class="mt-5 border border-gray-100 rounded-lg pt-4 px-4 pb-2 bg-gray-50 print:border print:border-gray-300 print:bg-white">
                            <h4 class="text-base font-bold text-gray-800 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                </svg>
                                Formanlage Daten
                            </h4>
                            <div class="mt-3 grid grid-cols-1 sm:grid-cols-5 gap-4">
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Verantwortlicher</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.formanlage_daten.verantwortlicher || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Gießdatum</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.formanlage_daten.giessdatum || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Gießzeit</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.formanlage_daten.giesszeit || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">SAP Zählnummer</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.formanlage_daten.sap_zahlnummer || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl Kästen</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.formanlage_daten.anzahl_kasten || '-' }}</p>
                                </div>
                            </div>

                            <!-- Teile -->
                            <div v-if="karte.formanlage_daten.teile && karte.formanlage_daten.teile.length > 0" class="mt-4">
                                <h5 class="text-sm font-bold text-gray-700 flex items-center mb-3">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                    Teile
                                </h5>
                                <div class="overflow-x-auto bg-white border border-gray-200 rounded-lg">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teilenummer</th>
                                                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="(teil, tIndex) in karte.formanlage_daten.teile" :key="tIndex">
                                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ teil.teilenummer }}
                                                </td>
                                                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ teil.anzahl }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- QS-Daten (spezifisch für In Bearbeitung Karten) -->
                        <div v-if="karte.qs_daten" class="mt-5 border border-gray-100 rounded-lg pt-4 px-4 pb-2 bg-purple-50 print:border print:border-gray-300 print:bg-white">
                            <h4 class="text-base font-bold text-gray-800 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                QS-Prüfung
                            </h4>
                            <div class="mt-3 grid grid-cols-1 sm:grid-cols-4 gap-4">
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</span>
                                    <p class="text-sm font-medium text-purple-600 mt-1">{{ karte.qs_daten.status || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">QS-Typ</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ karte.qs_daten.qs_type || '-' }}</p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Prüfer</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">
                                        {{ karte.qs_daten.pruefer?.[0]?.name || '-' }}
                                    </p>
                                </div>
                                <div class="bg-white p-3 rounded border border-gray-200">
                                    <span class="text-xs font-medium text-gray-500 uppercase tracking-wider">Datum</span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">
                                        {{ karte.qs_daten.pruefer?.[0]?.datum ? formatDate(karte.qs_daten.pruefer[0].datum) : '-' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
@media print {
    @page {
        size: A4;
        margin: 1cm;
    }
    
    body {
        font-size: 12pt;
    }
    
    .print\:border-t {
        border-top-width: 1px !important;
    }
    
    .print\:border-b-2 {
        border-bottom-width: 2px !important;
    }
    
    .print\:border-black {
        border-color: black !important;
    }
    
    .print\:border-gray-300 {
        border-color: #d1d5db !important;
    }
    
    .print\:text-gray-700 {
        color: #374151 !important;
    }
    
    .print\:break-inside-avoid {
        break-inside: avoid;
    }
    
    .print\:hover\:bg-white:hover {
        background-color: white !important;
    }
    
    .print\:page-break-inside-avoid {
        page-break-inside: avoid;
    }
    
    .print\:absolute {
        position: absolute !important;
    }
    
    .print\:top-10 {
        top: 2.5rem !important;
    }
    
    .print\:right-10 {
        right: 2.5rem !important;
    }
    
    .print\:hidden {
        display: none !important;
    }
    
    .print\:block {
        display: block !important;
    }
    
    .hidden.print\:block {
        display: block !important;
    }
    
    .print\:text-2xl {
        font-size: 1.5rem !important;
    }
    
    .print\:shadow-none {
        box-shadow: none !important;
    }
    
    .print\:border-0 {
        border-width: 0 !important;
    }
    
    .print\:bg-white {
        background-color: white !important;
    }
    
    .print\:border {
        border-width: 1px !important;
    }
}
</style> 