<script setup>
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: true
    }
});

const form = useForm({
    status: props.rotekarte.status,
    spektrometer_daten: props.rotekarte.spektrometer_daten || {},
});

const submit = () => {
    form.put(route('rotekarte.update', props.rotekarte.id));
};
</script>

<template>
    <AppLayout>
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h2 class="text-2xl font-bold mb-6">Rotekarte Nr. {{ rotekarte.id }} bearbeiten</h2>

                    <form @submit.prevent="submit" class="space-y-6">
                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select v-model="form.status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="spectrometer_complete">Spektrometer abgeschlossen</option>
                                <option value="in_progress">In Bearbeitung</option>
                                <option value="completed">Abgeschlossen</option>
                            </select>
                            <p v-if="form.errors.status" class="mt-1 text-sm text-red-600">{{ form.errors.status }}</p>
                        </div>

                        <!-- Spektrometer Daten -->
                        <div v-if="form.spektrometer_daten" class="space-y-4">
                            <h3 class="text-lg font-medium">Spektrometer Daten</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Name</label>
                                    <input type="text" v-model="form.spektrometer_daten.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Chargennummer</label>
                                    <input type="text" v-model="form.spektrometer_daten.chargennummer" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Abteilung</label>
                                    <input type="text" v-model="form.spektrometer_daten.abteilung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Eisenmarke</label>
                                    <input type="text" v-model="form.spektrometer_daten.eisenmarke" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>
                            </div>

                            <!-- Analysewerte -->
                            <div v-if="form.spektrometer_daten.analysewerte" class="mt-4">
                                <h4 class="text-lg font-medium mb-2">Analysewerte</h4>
                                <div class="space-y-2">
                                    <div v-for="(analyse, index) in form.spektrometer_daten.analysewerte" :key="index" class="grid grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Element</label>
                                            <input type="text" v-model="analyse.element" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Ist-Wert</label>
                                            <input type="text" v-model="analyse.istWert" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Soll-Wert</label>
                                            <input type="text" v-model="analyse.sollWert" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <Link :href="route('dashboard')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Abbrechen
                            </Link>
                            <button type="submit"
                                :disabled="form.processing"
                                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50">
                                {{ form.processing ? 'Wird gespeichert...' : 'Speichern' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
