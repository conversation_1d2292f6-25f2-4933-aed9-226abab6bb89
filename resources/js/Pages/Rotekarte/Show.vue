<script setup>
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';
import { marked } from 'marked';

const showSpektrometerModal = ref(false);
const showFormanlagModal = ref(false);
const showGussModal = ref(false);
const showQsModal = ref(false);
const showAiQueryModal = ref(false);
const aiResponse = ref('');
const isAiLoading = ref(false);
const qsDocuments = ref([]);

// Create a computed property to render the markdown response
const aiResponseHtml = computed(() => {
    return aiResponse.value ? marked(aiResponse.value) : '';
});

const props = defineProps({
    rotekarte: {
        type: Object,
        required: true
    }
});

onMounted(async () => {
    if (props.rotekarte?.id) {
        try {
            const response = await axios.get(route('qs.documents.index', { rotekarte_id: props.rotekarte.id }));
            qsDocuments.value = response.data;
        } catch (error) {
            console.error('Fehler beim Laden der QS-Dokumente:', error);
        }
    }
    
    // Check URL parameters to see if we should open the AI modal
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('openAiModal') === 'true' && props.rotekarte?.spektrometer_daten?.analysewerte?.length > 0) {
        showAiQueryModal.value = true;
        
        // Don't automatically query, just show the modal and let the user click the button
        // This prevents the automatic GET request that was causing the 500 error
        // queryAiAssistant(); 
    }
});

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

const prepareAiPrompt = () => {
    if (!props.rotekarte?.spektrometer_daten?.analysewerte) return '';
    
    const eisenmarke = props.rotekarte.spektrometer_daten.eisenmarke || 'nicht angegeben';
    
    // Extract analysis data for prompt
    const analyseWerte = props.rotekarte.spektrometer_daten.analysewerte
        .map(analyse => {
            return `${analyse.element}: Ist-Wert ${analyse.istWert}, Soll-Wert ${analyse.sollWert}`;
        })
        .join('\n');
    
    return `Eisenmarke: ${eisenmarke}

Analysewerte:
${analyseWerte}`;
};

const queryAiAssistant = async () => {
    isAiLoading.value = true;
    aiResponse.value = '';
    
    try {
        const prompt = prepareAiPrompt();
        const requestData = { prompt };
        
        // Verwende die Rotekarten-ID statt der Spektrometer-ID
        const rotekarteId = props.rotekarte?.id;
        if (rotekarteId && Number.isInteger(Number(rotekarteId))) {
            requestData.analyseId = Number(rotekarteId);
        }
        
        // Entfernen der manuellen CSRF-Token-Suche und direkt den axios-Aufruf verwenden
        const response = await axios.post('/api/ai-assistant/query', requestData);
        
        // Prüfen, ob eine Fehlermeldung zurückgegeben wurde
        if (response.data.error) {
            throw new Error(response.data.error);
        }
        
        aiResponse.value = response.data.response;
    } catch (error) {
        console.error('Fehler bei der AI-Anfrage:', error);
        
        // Wenn es ein API-Fehler ist (503 oder api_error flag)
        if (error.response && (error.response.status === 503 || error.response.data.api_error)) {
            aiResponse.value = '⚠️ **KI-Dienst nicht verfügbar**\n\nDie KI-API ist derzeit nicht erreichbar. Bitte versuchen Sie es später erneut.';
        } else if (error.response && error.response.data.format_error) {
            // Format-Fehler
            aiResponse.value = '⚠️ **Datenformat-Fehler**\n\n' + error.response.data.error;
        } else {
            // Allgemeiner Fehler
            aiResponse.value = '⚠️ **Fehler bei der Verarbeitung**\n\nEs ist ein Fehler bei der Verarbeitung Ihrer Anfrage aufgetreten. Bitte versuchen Sie es später erneut.';
        }
    } finally {
        isAiLoading.value = false;
    }
};

const isWithinTolerance = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return false;

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(min) && !isNaN(max) && istWert >= min && istWert <= max;
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(max) && istWert <= max;
    }

    return false;
};

const getToleranceDeviation = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return '';

    const istWert = parseFloat(analyse.istWert.replace(',', '.'));

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        if (istWert < min) {
            const absoluteDeviation = (istWert - min).toFixed(3);
            const percentDeviation = ((istWert - min) / min * 100).toFixed(2);
            return `${absoluteDeviation.replace('.', ',')} (${percentDeviation.replace('.', ',')}%)`;
        }
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    return '';
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto">
            <!-- Header Card -->
            <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="flex items-center space-x-4">
                            <h1 class="text-2xl font-bold text-gray-900">Rotekarte Nr.  {{ rotekarte.id }}</h1>
                            <span :class="{
                                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium': true,
                                'bg-blue-100 text-blue-800': rotekarte.status === 'spectrometer_complete',
                                'bg-green-100 text-green-800': rotekarte.status === 'formanlage_complete'
                            }">
                                {{ {
                                    'spectrometer_complete': 'Spektrometer abgeschlossen',
                                    'formanlage_complete': 'Formanlage abgeschlossen'
                                }[rotekarte.status] }}
                            </span>
                            <span v-if="rotekarte.spektrometer_daten?.abteilung" :class="{
                                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium': true,
                                'bg-blue-100 text-blue-800': rotekarte.spektrometer_daten.abteilung === 'NG',
                                'bg-green-100 text-green-800': rotekarte.spektrometer_daten.abteilung === 'GG',
                                'bg-yellow-100 text-yellow-800': rotekarte.spektrometer_daten.abteilung === 'HF'
                            }">
                                {{ {
                                    'NG': 'QS Kleinguss',
                                    'GG': 'QS Großguss',
                                    'HF': 'QS Großguss HF'
                                }[rotekarte.spektrometer_daten.abteilung] }}
                            </span>
                            <span v-if="rotekarte.spektrometer_daten?.fehlercode" :class="{
                                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium': true,
                                'bg-red-100 text-red-800': true
                            }">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {{ rotekarte.spektrometer_daten.fehlercode }}
                            </span>
                            <span v-if="rotekarte.qs_daten?.vorschlag_pruefungen" :class="{
                                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium': true,
                                'bg-purple-100 text-purple-800': true
                            }">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                {{ rotekarte.qs_daten.vorschlag_pruefungen }}
                            </span>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Erstellt am {{ new Date(rotekarte.created_at).toLocaleString('de-DE') }}</p>
                    </div>
                    <Link
                        :href="route('dashboard')"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Zurück zur Übersicht
                    </Link>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Spektrometer Daten Card -->
                <div v-if="rotekarte.spektrometer_daten" class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center space-x-2">
                            <h2 class="text-lg font-semibold text-gray-900">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                    <span>Spektrometer Daten</span>
                                </div>
                            </h2>
                        </div>
                        <div class="flex space-x-2">
                            <!-- <button 
                                v-if="rotekarte.spektrometer_daten.analysewerte?.length > 0"
                                @click="showAiQueryModal = true" 
                                class="inline-flex items-center px-3 py-1.5 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                            >
                                <svg class="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                KI-Fragen
                            </button> -->
                            <button @click="showSpektrometerModal = !showSpektrometerModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-xs text-gray-500">Name</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.name }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Chargennummer</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.chargennummer }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Eisenmarke</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.eisenmarke }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Datum & Uhrzeit</span>
                            <p class="text-sm font-medium text-gray-900">
                                {{ formatDate(rotekarte.spektrometer_daten.datum) }} 
                                <span class="bg-blue-100 px-2 py-0.5 rounded text-blue-800 font-medium">
                                    {{ rotekarte.spektrometer_daten.uhrzeit }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Proben -->
                    <div v-if="rotekarte.spektrometer_daten.proben" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Proben</h3>
                        <div class="bg-gray-50 rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Probe</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gid-Nummer</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Probennummer</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr v-for="(probe, index) in rotekarte.spektrometer_daten.proben" :key="index" class="hover:bg-gray-100">
                                        <td class="px-4 py-3 text-sm text-gray-900">Probe {{ index + 1 }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ probe.gidNummer }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ probe.probenummer }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Analysewerte -->
                    <div v-if="rotekarte.spektrometer_daten.analysewerte" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Analysewerte</h3>
                        <div class="bg-gray-50 rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Element</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ist-Wert</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Soll-Wert</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toleranz</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr v-for="analyse in rotekarte.spektrometer_daten.analysewerte" :key="analyse.element" class="hover:bg-gray-100">
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900">{{ analyse.element }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ analyse.istWert }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ analyse.sollWert }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">
                                            <span :class="[
                                                'px-2 py-1 text-xs font-medium rounded inline-block',
                                                isWithinTolerance(analyse) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                            ]">
                                                <template v-if="!isWithinTolerance(analyse)">
                                                    {{ getToleranceDeviation(analyse) }}
                                                </template>
                                                <template v-else>
                                                    OK
                                                </template>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <button 
                        v-if="rotekarte.spektrometer_daten.analysewerte?.length > 0"
                        @click="showAiQueryModal = true" 
                        class="inline-flex items-center px-3 py-1.5 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150 mt-4"
                        >
                            <svg class="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Prüfmethoden ermitteln
                    </button>
                </div>

                <!-- Formanlage Daten Card -->
                <div v-if="rotekarte.formanlage_daten" class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center space-x-2">
                            <h2 class="text-lg font-semibold text-gray-900">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span>Formanlage Daten</span>
                                </div>
                            </h2>
                        </div>
                        <button @click="showFormanlagModal = !showFormanlagModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-xs text-gray-500">Verantwortlicher</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.formanlage_daten.verantwortlicher }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Gießdatum & Zeit</span>
                            <p class="text-sm font-medium text-gray-900">
                                {{ formatDate(rotekarte.formanlage_daten.giessdatum) }} {{ rotekarte.formanlage_daten.giesszeit }}
                            </p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Anzahl Kästen</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.formanlage_daten.anzahl_kasten }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">SAP Zahlnummer</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.formanlage_daten.sap_zahlnummer }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Formen gekennzeichnet?</span>
                            <div class="flex items-center mt-1">
                                <span v-if="rotekarte.formanlage_daten.formen_gekennzeichnet" class="text-green-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </span>
                                <span v-else class="text-red-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Formen an der Anlage ausgeschleust?</span>
                            <div class="flex items-center mt-1">
                                <span v-if="rotekarte.formanlage_daten.formen_an_anlage" class="text-green-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </span>
                                <span v-else class="text-red-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Ausschussseparation durchgeführt?</span>
                            <div class="flex items-center mt-1">
                                <span v-if="rotekarte.formanlage_daten.ausschussseparation" class="text-green-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </span>
                                <span v-else class="text-red-600">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Teile -->
                    <div v-if="rotekarte.formanlage_daten.teile?.length" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Teile</h3>
                        <div class="bg-gray-50 rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teilenummer</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr v-for="(teil, index) in rotekarte.formanlage_daten.teile" :key="index" class="hover:bg-gray-100">
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ teil.teilenummer }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ teil.anzahl }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Bemerkungen -->
                    <div v-if="rotekarte.formanlage_daten.bemerkungen" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Bemerkungen</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ rotekarte.formanlage_daten.bemerkungen }}</p>
                        </div>
                    </div>
                </div>

                <!-- Gussnachbehandlung Daten Card -->
                <div v-if="rotekarte.gussnachbehandlung_daten" class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center space-x-2">
                            <h2 class="text-lg font-semibold text-gray-900">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                    </svg>
                                    <span>Gussnachbehandlung Daten</span>
                                </div>
                            </h2>
                        </div>
                        <button @click="showGussModal = !showGussModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-xs text-gray-500">Verantwortlicher</span>
                            <p class="text-sm font-medium text-gray-900">{{ rotekarte.gussnachbehandlung_daten.verantwortlicher }}</p>
                        </div>
                        <div>
                            <span class="text-xs text-gray-500">Datum</span>
                            <p class="text-sm font-medium text-gray-900">{{ formatDate(rotekarte.gussnachbehandlung_daten.datum) }}</p>
                        </div>
                    </div>

                    <!-- Bemerkungen -->
                    <div v-if="rotekarte.gussnachbehandlung_daten.bemerkungen" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Bemerkungen</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ rotekarte.gussnachbehandlung_daten.bemerkungen }}</p>
                        </div>
                    </div>
                </div>

                <!-- QS Daten Section -->
                <div v-if="rotekarte.formanlage_daten && rotekarte.qs_daten" class="bg-white rounded-lg shadow-lg p-6 space-y-6 border-2 border-blue-200">
                    <div class="flex items-center space-x-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h2 class="text-xl font-bold text-blue-900">QS Daten</h2>
                    </div>

                    <!-- Vorschlag Prüfungen -->
                    <div class="bg-blue-50 border-2 border-blue-300 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">Vorschlag Prüfungen</h3>
                        <div class="bg-white border border-blue-200 rounded px-4 py-2 text-blue-800 font-medium w-full">
                            {{ rotekarte.qs_daten?.vorschlag_pruefungen || '-' }}
                        </div>
                    </div>

                    <!-- Teile -->
                    <div v-if="rotekarte.qs_daten?.teile && rotekarte.qs_daten.teile.length > 0" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-3">Teile</h3>
                        <div class="bg-gray-50 rounded-lg overflow-hidden">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teilenummer</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Geprüfte Teile</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IO/NIO</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr v-for="(teil, index) in rotekarte.qs_daten.teile" :key="index" class="hover:bg-gray-100">
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ teil.teilenummer }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ teil.anzahl }}</td>
                                        <td class="px-4 py-3 text-sm text-gray-900">{{ teil.gepruefteTeile }}</td>
                                        <td class="px-4 py-3">
                                            <span :class="{
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                                                'bg-green-100 text-green-800': teil.io_nio === 'IO',
                                                'bg-red-100 text-red-800': teil.io_nio === 'NIO'
                                            }">
                                                {{ teil.io_nio }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Bemerkungen -->
                    <div v-if="rotekarte.qs_daten?.bemerkungen && rotekarte.qs_daten.bemerkungen.length > 0" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Bemerkungen</h3>
                        <div class="space-y-4">
                            <div v-for="(remark, index) in rotekarte.qs_daten.bemerkungen" :key="index"
                                class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-700">{{ remark.text }}</p>
                                <div class="mt-1 text-xs text-gray-500">
                                    <span class="font-medium">{{ remark.pruefer }}</span>
                                    <span class="mx-1">•</span>
                                    <span>{{ remark.timestamp }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QS Dokumente -->
                    <div v-if="qsDocuments.length > 0" class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">QS Dokumente</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <a v-for="document in qsDocuments" 
                               :key="document.id"
                               :href="document.url"
                               target="_blank"
                               class="flex items-center space-x-3 p-3 bg-white border rounded-lg hover:bg-gray-50 transition-colors duration-150">
                                <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 2v5a2 2 0 002 2h5" />
                                </svg>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ document.filename }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        Dokument
                                    </p>
                                </div>
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Status und Serien Status -->
                    <div v-if="rotekarte.qs_daten?.status || rotekarte.qs_daten?.serie_status" class="mt-6 pt-6 border-t border-gray-200">
                        <div class="flex items-center space-x-4">
                            <div v-if="rotekarte.qs_daten?.status">
                                <span class="text-sm font-medium text-gray-500">Status</span>
                                <span :class="{
                                    'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                                    'bg-yellow-100 text-yellow-800': rotekarte.qs_daten.status === 'In Bearbeitung',
                                    'bg-green-100 text-green-800': rotekarte.qs_daten.status === 'Abgeschlossen',
                                    'bg-red-100 text-red-800': rotekarte.qs_daten.status === 'Abgelehnt'
                                }">
                                    {{ rotekarte.qs_daten.status }}
                                </span>
                            </div>
                            <div v-if="rotekarte.qs_daten?.serie_status">
                                <span class="text-sm font-medium text-gray-500">Abnahmebeauftragter</span>
                                <span :class="{
                                    'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                                    'text-orange-600 bg-orange-100': rotekarte.qs_daten.serie_status === 'Entscheidung ausstehend',
                                    'text-green-600 bg-green-100': rotekarte.qs_daten.serie_status === 'Teile in die Serie einreihen',
                                    'text-red-600 bg-red-100': rotekarte.qs_daten.serie_status === 'Zur Verschrottung freigegeben'
                                }">
                                    {{ rotekarte.qs_daten.serie_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Info Modals -->
        <div v-if="showSpektrometerModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">System Information - Spektrometer</h3>
                    <button @click="showSpektrometerModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Aktuelle Information</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <span class="text-xs text-gray-500">Benutzer</span>
                                <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten?.system_info?.current?.username }}</p>
                            </div>
                            <div>
                                <span class="text-xs text-gray-500">Datum & Uhrzeit</span>
                                <p class="text-sm font-medium text-gray-900">
                                    {{ formatDate(rotekarte.spektrometer_daten.datum) }} 
                                    <span class="bg-blue-100 px-2 py-0.5 rounded text-blue-800 font-medium">
                                        {{ rotekarte.spektrometer_daten.uhrzeit }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div v-if="rotekarte.spektrometer_daten?.system_info?.history?.length" class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Historie</h4>
                        <div class="space-y-2">
                            <div v-for="(entry, index) in rotekarte.spektrometer_daten.system_info.history" :key="index"
                                class="bg-white p-2 rounded-md border border-gray-200">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <span class="text-xs text-gray-500">Benutzer</span>
                                        <p class="text-sm text-gray-900">{{ entry.username }}</p>
                                    </div>
                                    <div>
                                        <span class="text-xs text-gray-500">Zeitpunkt</span>
                                        <p class="text-sm text-gray-900">{{ entry.timestamp }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showFormanlagModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">System Information - Formanlage</h3>
                    <button @click="showFormanlagModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Aktuelle Information</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <span class="text-xs text-gray-500">Benutzer</span>
                                <p class="text-sm font-medium text-gray-900">{{ rotekarte.formanlage_daten?.system_info?.current?.username }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-if="rotekarte.formanlage_daten?.system_info?.history?.length" class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Historie</h4>
                        <div class="space-y-2">
                            <div v-for="(entry, index) in rotekarte.formanlage_daten.system_info.history" :key="index"
                                class="bg-white p-2 rounded-md border border-gray-200">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <span class="text-xs text-gray-500">Benutzer</span>
                                        <p class="text-sm text-gray-900">{{ entry.username }}</p>
                                    </div>
                                    <div>
                                        <span class="text-xs text-gray-500">Zeitpunkt</span>
                                        <p class="text-sm text-gray-900">{{ entry.timestamp }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showGussModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">System Information - Gussnachbehandlung</h3>
                    <button @click="showGussModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Aktuelle Information</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <span class="text-xs text-gray-500">Benutzer</span>
                                <p class="text-sm font-medium text-gray-900">{{ rotekarte.gussnachbehandlung_daten?.system_info?.current?.username }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-if="rotekarte.gussnachbehandlung_daten?.system_info?.history?.length" class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Historie</h4>
                        <div class="space-y-2">
                            <div v-for="(entry, index) in rotekarte.gussnachbehandlung_daten.system_info.history" :key="index"
                                class="bg-white p-2 rounded-md border border-gray-200">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <span class="text-xs text-gray-500">Benutzer</span>
                                        <p class="text-sm text-gray-900">{{ entry.username }}</p>
                                    </div>
                                    <div>
                                        <span class="text-xs text-gray-500">Zeitpunkt</span>
                                        <p class="text-sm text-gray-900">{{ entry.timestamp }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showQsModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">System Information - QS</h3>
                    <button @click="showQsModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Aktuelle Information</h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <span class="text-xs text-gray-500">Benutzer</span>
                                <p class="text-sm font-medium text-gray-900">{{ rotekarte.qs_daten?.system_info?.current?.username }}</p>
                            </div>
                        </div>
                    </div>
                    <div v-if="rotekarte.qs_daten?.system_info?.history?.length" class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Historie</h4>
                        <div class="space-y-2">
                            <div v-for="(entry, index) in rotekarte.qs_daten.system_info.history" :key="index"
                                class="bg-white p-2 rounded-md border border-gray-200">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <span class="text-xs text-gray-500">Benutzer</span>
                                        <p class="text-sm text-gray-900">{{ entry.username }}</p>
                                    </div>
                                    <div>
                                        <span class="text-xs text-gray-500">Zeitpunkt</span>
                                        <p class="text-sm text-gray-900">{{ entry.timestamp }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Assistant Modal -->
        <div v-if="showAiQueryModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] flex flex-col overflow-hidden">
                <!-- Modal Header -->
                <div class="border-b border-gray-200 p-4 flex justify-between items-center bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
                    <div class="flex items-center space-x-2">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <h3 class="text-lg font-bold">Metallurgischer Prüfassistent</h3>
                    </div>
                    <button @click="showAiQueryModal = false" class="text-white hover:text-gray-200 focus:outline-none">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <!-- Modal Body -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Analysedaten</h4>
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <span class="text-xs text-gray-500">Eisenmarke</span>
                                    <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.eisenmarke }}</p>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Chargennummer</span>
                                    <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.chargennummer }}</p>
                                </div>
                            </div>
                            
                            <div v-if="rotekarte.spektrometer_daten.analysewerte?.length > 0">
                                <span class="text-xs text-gray-500 block mb-2">Analysewerte</span>
                                <table class="min-w-full divide-y divide-gray-200 border rounded-lg overflow-hidden">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Element</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ist-Wert</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Soll-Wert</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toleranz</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-for="analyse in rotekarte.spektrometer_daten.analysewerte" :key="analyse.element" 
                                            :class="[!isWithinTolerance(analyse) ? 'bg-red-50' : '']">
                                            <td class="px-3 py-2 text-sm font-medium text-gray-900">{{ analyse.element }}</td>
                                            <td class="px-3 py-2 text-sm text-gray-900">{{ analyse.istWert }}</td>
                                            <td class="px-3 py-2 text-sm text-gray-900">{{ analyse.sollWert }}</td>
                                            <td class="px-3 py-2 text-sm">
                                                <span :class="[
                                                    'px-2 py-1 text-xs font-medium rounded inline-block',
                                                    isWithinTolerance(analyse) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                ]">
                                                    <template v-if="!isWithinTolerance(analyse)">
                                                        {{ getToleranceDeviation(analyse) }}
                                                    </template>
                                                    <template v-else>
                                                        OK
                                                    </template>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <button 
                            @click="queryAiAssistant" 
                            class="w-full flex justify-center items-center py-2.5 px-4 bg-indigo-600 text-white rounded-lg shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150 disabled:opacity-50"
                            :disabled="isAiLoading"
                        >
                            <span v-if="!isAiLoading">Notwendige Prüfungen durch KI ermitteln</span>
                            <span v-else class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Analysiere Daten...
                            </span>
                        </button>
                        <div v-if="!aiResponse && showAiQueryModal" class="mt-2 text-center text-sm text-gray-600">
                            Klicken Sie auf den Button oben, um die KI-Analyse zu starten.
                        </div>
                    </div>
                    
                    <div v-if="aiResponse" class="bg-indigo-50 border border-indigo-200 rounded-lg p-5 mb-4">
                        <h4 class="text-sm font-medium text-indigo-700 mb-2 flex items-center">
                            <svg class="w-5 h-5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Empfohlene Prüfmethoden
                        </h4>
                        <div class="prose prose-sm max-w-none text-indigo-900" v-html="aiResponseHtml"></div>
                    </div>
                    
                    <div class="text-xs text-gray-500 mt-3">
                        <p>Die empfohlenen Prüfmethoden dienen als Entscheidungshilfe. Die endgültige Beurteilung zur Freigabe oder Verschrottung sollte durch einen qualifizierten Mitarbeiter erfolgen.</p>
                    </div>
                </div>
                
                <!-- Modal Footer -->
                <div class="border-t border-gray-200 p-4 bg-gray-50">
                    <div class="flex justify-end">
                        <button 
                            @click="showAiQueryModal = false" 
                            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Schließen
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
