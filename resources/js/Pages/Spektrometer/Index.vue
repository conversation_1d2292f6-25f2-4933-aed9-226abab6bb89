<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import Swal from 'sweetalert2';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: false,
        default: null
    }
});

const form = useForm({
    rotekarte_id: props.rotekarte?.id || null,
    spektrometer_daten: {
        name: '',
        datum: '',
        uhrzeit: '',
        abteilung: '',
        chargennummer: '',
        eisenmarke: '',
        fehlercode: '',
        proben: [],
        analysewerte: [],
        bemerkungen: '',
        system_info: {
            current: props.rotekarte?.spektrometer_daten?.system_info?.current || {
                username: '',
                hostname: '',
                timestamp: ''
            },
            history: props.rotekarte?.spektrometer_daten?.system_info?.history || []
        }
    }
});

const probenForm = ref({
    probenummer: '',
    gidNummer: ''
});

const analyseForm = ref({
    element: '',
    istWert: '',
    sollWert: ''
});

const sollwerte = ref(null);
const sollwerteError = ref(null);
const showElementModal = ref(false);
const availableElements = ref([]);
const showEisenmarkeModal = ref(false);
const availableEisenmarken = ref([]);
const showFehlercodeModal = ref(false);
const availableFehlercodes = ref([]);
const selectedFehlercode = ref(null);

const fetchSollwerte = async (eisenmarke) => {
    try {
        sollwerteError.value = null;
        const response = await axios.get(route('spektrometer.sollwerte'), {
            params: {
                eisenmarke,
                abteilung: form.spektrometer_daten.abteilung
            }
        });
        sollwerte.value = response.data;
    } catch (error) {
        sollwerteError.value = error.response?.data?.message || 'Fehler beim Laden der Sollwerte';
        sollwerte.value = null;
    }
};

watch(() => form.spektrometer_daten.eisenmarke, (newValue) => {
    if (newValue) {
        fetchSollwerte(newValue);
    } else {
        sollwerte.value = null;
        sollwerteError.value = null;
    }
});

const getElementName = (symbol) => {
    const elementNames = {
        'C': 'Kohlenstoff',
        'P': 'Phosphor',
        'S': 'Schwefel',
        'Cr': 'Chrom',
        'Cu': 'Kupfer',
        'Mg': 'Magnesium',
        'Mn': 'Mangan',
        'Mo': 'Molybdän',
        'Ni': 'Nickel',
        'Si': 'Silizium',
        'Sn': 'Zinn',
        'SC': 'Sättigungsgrad',
        'Liq': 'Liquidus',
        'UK_r': 'Unterkühlung',
        'UKR': 'Unterkühlung',
        'AT': 'Abstichtemperatur',
        'ÜT': 'Überhitzungstemperatur',
        'WT': 'Warmhaltetemperatur'
    };
    return elementNames[symbol] || symbol;
};

const getElementKey = (element) => {
    // Special Soll-Werte Abrufen Arif
    const specialCases = {
        'SC': 'Sättigungsgrad_Sc_percent',
        'LIQ': sollwerte.value?.Liq_r_percent ? 'Liq_r_percent' : 'Liq_percent',
        'UKR': 'UKr_percent',
        'UK_R': 'UK_r_percent',
        'AT': 'Abstichtemperatur_percent',
        'ÜT': 'Überhitzungstemperatur_percent',
        'WT': 'Warmhaltetemperatur_percent'
    };

    // Check if it's a special case
    const upperElement = element.toUpperCase();
    if (specialCases[upperElement]) {
        return specialCases[upperElement];
    }

    // Default case for regular elements (with _percent suffix)
    return `${element.charAt(0).toUpperCase() + element.slice(1).toLowerCase()}_percent`;
};

watch(() => analyseForm.value.element, (newValue) => {
    if (newValue && sollwerte.value) {
        const elementKey = getElementKey(newValue);
        analyseForm.value.sollWert = sollwerte.value[elementKey] || '';
        console.log('Element Key:', elementKey, 'Value:', sollwerte.value[elementKey]); // Debug logging
    } else {
        analyseForm.value.sollWert = '';
    }
});

const addProbe = () => {
    if (probenForm.value.probenummer && probenForm.value.gidNummer) {
        form.spektrometer_daten.proben.push({
            probenummer: probenForm.value.probenummer,
            gidNummer: probenForm.value.gidNummer
        });
        probenForm.value.probenummer = '';
        probenForm.value.gidNummer = '';
    }
};

const addAnalyse = () => {
    // Normaler Fall: Alle Werte vorhanden
    if (analyseForm.value.element && analyseForm.value.istWert && analyseForm.value.sollWert) {
        form.spektrometer_daten.analysewerte.push({
            element: analyseForm.value.element,
            istWert: analyseForm.value.istWert,
            sollWert: analyseForm.value.sollWert
        });
        analyseForm.value.element = '';
        analyseForm.value.istWert = '';
        analyseForm.value.sollWert = '';
    }
    // Spezialfall: "Nicht analysierbar" wurde ausgewählt
    else if (analyseForm.value.element === 'Nicht analysierbar') {
        form.spektrometer_daten.analysewerte.push({
            element: 'Nicht analysierbar',
            istWert: '-',
            sollWert: '-'
        });
        analyseForm.value.element = '';
        analyseForm.value.istWert = '';
        analyseForm.value.sollWert = '';
    }
};

const addNichtAnalysierbar = () => {
    form.spektrometer_daten.analysewerte.push({
        element: 'Nicht analysierbar',
        istWert: '-',
        sollWert: '-'
    });
};

const captureSystemInfo = async () => {
    try {
        const response = await fetch('/system-info', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.username || data.hostname) {
            form.spektrometer_daten.system_info.current = {
                username: data.username || 'Nicht verfügbar',
                hostname: data.hostname || 'Nicht verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        } else {
            form.spektrometer_daten.system_info.current = {
                username: 'Keine Daten verfügbar',
                hostname: 'Keine Daten verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        }
    } catch (error) {
        form.spektrometer_daten.system_info.current = {
            username: 'Systemfehler',
            hostname: 'Systemfehler',
            timestamp: new Date().toLocaleString('de-DE')
        };
    }
};

const fetchFehlercodes = async () => {
    try {
        const response = await axios.get(route('spektrometer.fehlercodes'));
        availableFehlercodes.value = response.data;
    } catch (error) {
        console.error('Fehler beim Laden der Fehlercodes:', error);
    }
};

const selectFehlercode = (fehlercode) => {
    form.spektrometer_daten.fehlercode = fehlercode.fehlercode;
    selectedFehlercode.value = fehlercode;
    showFehlercodeModal.value = false;
};

const autoSuggestFehlercode = async () => {
    console.log('autoSuggestFehlercode aufgerufen');
    console.log('Eisenmarke:', form.spektrometer_daten.eisenmarke);
    console.log('Analysewerte:', form.spektrometer_daten.analysewerte);

    if (!form.spektrometer_daten.eisenmarke || !form.spektrometer_daten.analysewerte || form.spektrometer_daten.analysewerte.length === 0) {
        console.warn('Eisenmarke oder Analysewerte fehlen');
        
        Swal.fire({
            title: 'Fehler',
            text: 'Bitte wählen Sie eine Eisenmarke und geben Sie mindestens einen Analysewert ein.',
            icon: 'warning',
            showConfirmButton: true,
            confirmButtonText: 'Verstanden',
            timer: 3000,
            timerProgressBar: true
        });
        return;
    }

    // Lade-Anzeige - zwei verschiedene Ansätze je nach Bildschirmgröße
    let loadingToast;
    
    // Auf größeren Bildschirmen: zentrierter Dialog
    if (window.innerWidth >= 768) {
        loadingToast = Swal.fire({
            title: 'Automatische Zuordnung',
            text: 'Fehlercode wird gesucht...',
            icon: 'info',
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    } else {
        // Auf kleineren Bildschirmen: Toast-Notification ohne automatisches Schließen
        loadingToast = Swal.fire({
            title: 'Fehlercode wird gesucht...',
            icon: 'info',
            position: 'center',
            showConfirmButton: false,
            didOpen: (toast) => {
                Swal.showLoading();
            }
        });
    }

    try {
        console.log('API-Request senden an:', route('spektrometer.suggest-fehlercode'));
        
        const response = await axios.post(route('spektrometer.suggest-fehlercode'), {
            eisenmarke: form.spektrometer_daten.eisenmarke,
            analysewerte: form.spektrometer_daten.analysewerte
        });

        // Detaillierte Logging der API-Antwort
        console.log('Detaillierte API-Antwort:', {
            success: response.data.success,
            message: response.data.message,
            fehlercode: response.data.fehlercode,
            details: response.data.details
        });

        loadingToast.close();

        if (response.data.success) {
            form.spektrometer_daten.fehlercode = response.data.fehlercode;
            selectedFehlercode.value = response.data.details;
            
            // Display success notification with confirmation button and auto-dismiss
            Swal.fire({
                title: 'Automatische Zuordnung',
                text: response.data.message,
                icon: 'success',
                showConfirmButton: true,
                confirmButtonText: 'Verstanden',
                timer: 9000,
                timerProgressBar: true
            });
        } else {
            // Fehlermeldung anzeigen mit Bestätigungsbutton und Auto-Dismiss
            Swal.fire({
                title: 'Automatische Zuordnung',
                text: response.data.message || 'Kein passender Fehlercode gefunden',
                icon: 'warning',
                showConfirmButton: true,
                confirmButtonText: 'Verstanden',
                showCancelButton: true,
                cancelButtonText: 'Manuelle Zuordnung',
                cancelButtonColor: '#3085d6',
                confirmButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    // Manueller Zuordnungs-Button wurde geklickt
                    showFehlercodeModal.value = true;
                }
            });
        }
    } catch (error) {
        loadingToast.close();
        
        console.error('Fehler bei der automatischen Zuordnung:', error);
        console.error('Fehler-Details:', error.response?.data || error.message);
        
        // Detaillierte Fehlermeldung anzeigen mit Bestätigungsbutton und manueller Zuordnung Option
        Swal.fire({
            title: 'Fehler',
            text: `Bei der automatischen Zuordnung ist ein Fehler aufgetreten: ${error.response?.data?.message || error.message}`,
            icon: 'error',
            showConfirmButton: true,
            confirmButtonText: 'Verstanden',
            showCancelButton: true,
            cancelButtonText: 'Manuelle Zuordnung',
            cancelButtonColor: '#3085d6',
            confirmButtonColor: '#6b7280'
        }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                // Manueller Zuordnungs-Button wurde geklickt
                showFehlercodeModal.value = true;
            }
        });
    }
};

// Watch for changes in eisenmarke and analysewerte to trigger auto-suggestion
watch([() => form.spektrometer_daten.eisenmarke, () => form.spektrometer_daten.analysewerte], 
    async ([newEisenmarke, newAnalysewerte], [oldEisenmarke, oldAnalysewerte]) => {
        if (newEisenmarke && newAnalysewerte && newAnalysewerte.length > 0) {
            // Check if either the eisenmarke changed or if new analysewerte were added
            const oldLength = oldAnalysewerte ? oldAnalysewerte.length : 0;
            const newLength = newAnalysewerte ? newAnalysewerte.length : 0;
            
            if (newEisenmarke !== oldEisenmarke || newLength > oldLength) {
                await autoSuggestFehlercode();
            }
        }
    }
);

onMounted(() => {
    captureSystemInfo();
    if (form.spektrometer_daten.eisenmarke) {
        fetchSollwerte(form.spektrometer_daten.eisenmarke);
    }
    fetchFehlercodes();
});

const submit = async () => {
    try {
        // Validate required fields before submission
        const requiredFields = {
            'Name': form.spektrometer_daten.name,
            'Datum': form.spektrometer_daten.datum,
            'Uhrzeit': form.spektrometer_daten.uhrzeit,
            'Abteilung': form.spektrometer_daten.abteilung,
            'Chargennummer': form.spektrometer_daten.chargennummer,
            'Eisenmarke': form.spektrometer_daten.eisenmarke,
            'Fehlercode': form.spektrometer_daten.fehlercode
        };

        // Check required fields
        const missingFields = Object.entries(requiredFields)
            .filter(([_, value]) => !value)
            .map(([key]) => key);

        if (missingFields.length > 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Pflichtfelder',
                text: `Bitte füllen Sie folgende Felder aus: ${missingFields.join(', ')}`,
                showConfirmButton: true
            });
            return;
        }

        // Check if proben array is empty
        if (form.spektrometer_daten.proben.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Proben',
                text: 'Bitte fügen Sie mindestens eine Probe hinzu.',
                showConfirmButton: true
            });
            return;
        }

        // Check if analysewerte array is empty
        if (form.spektrometer_daten.analysewerte.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Analysewerte',
                text: 'Bitte fügen Sie mindestens einen Analysewert hinzu.',
                showConfirmButton: true
            });
            return;
        }

        // Update system info
        const currentInfo = {
            username: form.spektrometer_daten.system_info.current.username,
            hostname: form.spektrometer_daten.system_info.current.hostname,
            timestamp: new Date().toLocaleString('de-DE')
        };

        if (!Array.isArray(form.spektrometer_daten.system_info.history)) {
            form.spektrometer_daten.system_info.history = [];
        }

        form.spektrometer_daten.system_info.history.push(currentInfo);

        // Submit the form
        await form.post(route('spektrometer.store'), {
            preserveScroll: true,
            onSuccess: async (response) => {
                try {
                    // Create Rotekarte
                    const rotekarteResponse = await axios.post('/rotekarte/create', {
                        spektrometer_id: response.data.id,
                        data: form.spektrometer_daten
                    });

                    // Send email notifications
                    await axios.post('/email/send-spektrometer-notification', {
                        spektrometer_id: response.data.id,
                        rotekarte_id: rotekarteResponse.data.id
                    });

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                } catch (error) {
                    console.error('Fehler bei der Nachverarbeitung:', error);
                    
                    // Show appropriate error message based on the error type
                    if (error.response?.status === 502) {
                        /* Swal.fire({
                            icon: 'error',
                            title: 'Server-Fehler',
                            text: 'Es gab ein Problem mit der Serververbindung. Bitte versuchen Sie es später erneut.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                    } else if (error.response?.status >= 400) {
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                        /* Swal.fire({
                            icon: 'warning',
                            title: 'Teilweise erfolgreich',
                            text: 'Die Spektrometer-Daten wurden gespeichert, aber es gab Probleme bei der Rotekarte-Erstellung oder E-Mail-Versendung.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });

                    } else {
                        /* Swal.fire({
                            icon: 'error',
                            title: 'Fehler',
                            text: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                    }
                }
            },
            onError: (errors) => {
                console.error('Fehler beim Speichern:', errors);
                let errorMessage = 'Bitte überprüfen Sie die folgenden Felder:\n\n';
                
                Object.entries(errors).forEach(([field, messages]) => {
                    const fieldName = field.split('.').pop(); // Get the last part of the field name
                    errorMessage += `${fieldName}: ${messages.join(', ')}\n`;
                });

                Swal.fire({
                    icon: 'error',
                    title: 'Fehler beim Speichern',
                    text: errorMessage,
                    showConfirmButton: true
                });
            }
        });
    } catch (error) {
        console.error('Allgemeiner Fehler:', error);
        Swal.fire({
            icon: 'error',
            title: 'Ein Fehler ist aufgetreten',
            text: 'Bitte überprüfen Sie Ihre Eingaben und versuchen Sie es später erneut.',
            showConfirmButton: true
        });
    }
};

const handleKeyDown = (event, nextFieldId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (nextFieldId === 'addAnalyseButton' || nextFieldId === 'addProbeButton') {
            document.getElementById(nextFieldId)?.click();
        } else {
            document.getElementById(nextFieldId)?.focus();
        }
    }
};

const updateSollWert = () => {
    if (sollwerte.value && analyseForm.value.element) {
        const elementKey = getElementKey(analyseForm.value.element);
        analyseForm.value.sollWert = sollwerte.value[elementKey] || '';
        console.log('Update Element Key:', elementKey, 'Value:', sollwerte.value[elementKey]); // Debug logging
    } else {
        analyseForm.value.sollWert = '';
    }
};

const isWithinTolerance = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return false;

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(min) && !isNaN(max) && istWert >= min && istWert <= max;
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(max) && istWert <= max;
    }

    return false;
};

const calculateToleranceText = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return 'Keine Werte';
    return isWithinTolerance(analyse) ? 'Innerhalb der Toleranz' : 'Außerhalb der Toleranz';
};

const getToleranceRange = (sollWert) => {
    if (!sollWert) return '';

    // Handle format "X - Y"
    if (sollWert.includes('-')) {
        return sollWert;
    }

    // Handle format "≤ X"
    if (sollWert.includes('≤')) {
        return `0 - ${sollWert.replace('≤', '').trim()}`;
    }

    return '';
};

const getToleranceDeviation = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return '';

    const istWert = parseFloat(analyse.istWert.replace(',', '.'));

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        if (istWert < min) {
            const absoluteDeviation = (istWert - min).toFixed(3);
            const percentDeviation = ((istWert - min) / min * 100).toFixed(2);
            return `${absoluteDeviation.replace('.', ',')} (${percentDeviation.replace('.', ',')}%)`;
        }
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    return '';
};

const updateAvailableElements = () => {
    if (sollwerte.value) {
        // Define the desired order of elements
        const elementOrder = [
            'C', 'P', 'S', 'Cr', 'Cu', 'Mg', 'Mn', 'Mo', 'Ni', 'Si', 'Sn', 'Liq', 'UK_r',
            'Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'
        ];

        // Create the elements array with proper formatting
        availableElements.value = elementOrder
            .map(elementKey => {
                let value = null;

                // Handle special cases for temperature values
                if (['Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'].includes(elementKey)) {
                    const key = `${elementKey}_percent`;
                    value = sollwerte.value[key];
                } else {
                    // Handle different key formats for other elements
                    const possibleKeys = [
                        `${elementKey}_percent`,
                        `${elementKey.toLowerCase()}_percent`,
                        `${elementKey}_r_percent`,
                        `${elementKey.toLowerCase()}_r_percent`,
                        `Liq_r_percent`,
                        `Liq_percent`,
                        `UK_r_percent`
                    ];

                    for (const key of possibleKeys) {
                        if (sollwerte.value[key] !== undefined && sollwerte.value[key] !== null && sollwerte.value[key] !== '') {
                            value = sollwerte.value[key];
                            break;
                        }
                    }
                }

                if (value !== undefined && value !== "null") {
                    return {
                        key: elementKey,
                        value: value
                    };
                }
                return null;
            })
            .filter(element => element !== null);
    }
};

watch(() => sollwerte.value, (newValue) => {
    if (newValue) {
        updateAvailableElements();
    }
});

const selectElement = (element) => {
    analyseForm.value.element = element.key;
    showElementModal.value = false;
};

const updateAvailableEisenmarken = async () => {
    try {
        const response = await axios.get(route('spektrometer.sollwerte'), {
            params: {
                abteilung: form.spektrometer_daten.abteilung
            }
        });

        console.log('Rohdaten von API:', response.data);
        console.log('Ausgewählte Abteilung:', form.spektrometer_daten.abteilung);

        if (form.spektrometer_daten.abteilung === 'HF') {
            console.log('HF Abteilung ausgewählt');
            availableEisenmarken.value = response.data
                .filter(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    console.log('Verarbeite Item:', item);
                    console.log('Geparste Daten:', parsedData);
                    // Filter out all two-digit Eisenmarken
                    const number = parseInt(parsedData.EM_HF?.match(/\d+/)?.[0] || '0');
                    return parsedData && parsedData.EM_HF && (number < 10 || number >= 100);
                })
                .map(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    return {
                        value: parsedData.EM_HF,
                        details: parsedData
                    };
                })
                .sort((a, b) => parseInt(a.value) - parseInt(b.value));
        } else {
            availableEisenmarken.value = response.data
                .filter(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    // Filter out all two-digit Eisenmarken
                    const number = parseInt(parsedData.EM?.match(/\d+/)?.[0] || '0');
                    return parsedData && parsedData.EM && !parsedData.EM_HF && (number < 10 || number >= 100);
                })
                .map(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    return {
                        value: parsedData.EM,
                        details: parsedData
                    };
                })
                .sort((a, b) => parseInt(a.value) - parseInt(b.value));
        }

        console.log('Gefilterte Eisenmarken:', availableEisenmarken.value);
    } catch (error) {
        console.error('Fehler beim Laden der Eisenmarken:', error);
        availableEisenmarken.value = [];
    }
};

watch(() => form.spektrometer_daten.abteilung, (newValue) => {
    if (newValue) {
        updateAvailableEisenmarken();
    }
});

const selectEisenmarke = (eisenmarke) => {
    form.spektrometer_daten.eisenmarke = eisenmarke.value.toString();
    showEisenmarkeModal.value = false;
};
</script>

<template>
    <AppLayout>
        <form @submit.prevent="submit">
            <div class="max-w-7xl mx-auto">
                <!-- Header Card -->
                <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">
                                Spektrometer
                            </h1>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Left Column (2/3) -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Basis Informationen Card -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-6">Basis Informationen</h2>
                            <div class="grid grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input type="text" v-model="form.spektrometer_daten.name"
                                        @keydown="handleKeyDown($event, 'datum')"
                                        id="name"
                                        placeholder="Wer bist du?"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !form.spektrometer_daten.name ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!form.spektrometer_daten.name" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Hey, bitte gib deinen Namen ein 😊</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Datum</label>
                                    <input type="date" v-model="form.spektrometer_daten.datum"
                                        @keydown="handleKeyDown($event, 'uhrzeit')"
                                        id="datum"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !form.spektrometer_daten.datum ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!form.spektrometer_daten.datum" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Wähle bitte ein Datum aus 📅</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Uhrzeit</label>
                                    <input type="time" v-model="form.spektrometer_daten.uhrzeit"
                                        @keydown="handleKeyDown($event, 'chargennummer')"
                                        id="uhrzeit"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !form.spektrometer_daten.uhrzeit ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!form.spektrometer_daten.uhrzeit" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Wähle bitte eine Uhrzeit aus ⏰</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Chargennummer</label>
                                    <input type="text" v-model="form.spektrometer_daten.chargennummer"
                                        @keydown="handleKeyDown($event, 'abteilung')"
                                        id="chargennummer"
                                        placeholder="123456"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !form.spektrometer_daten.chargennummer ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!form.spektrometer_daten.chargennummer" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Trag bitte die Chargennummer ein 📦</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Abteilung</label>
                                    <select v-model="form.spektrometer_daten.abteilung"
                                        @keydown="handleKeyDown($event, 'eisenmarke')"
                                        id="abteilung"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !form.spektrometer_daten.abteilung ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                        <option value="">Bitte wählen</option>
                                        <option value="NG">NG</option>
                                        <option value="GG">GG</option>
                                        <option value="HF">HF</option>
                                    </select>
                                    <div v-if="!form.spektrometer_daten.abteilung" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Wähle bitte eine Abteilung aus 🏢</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Eisenmarke</label>
                                    <div class="relative">
                                        <input type="text" v-model="form.spektrometer_daten.eisenmarke"
                                            @keydown="handleKeyDown($event, 'element')"
                                            id="eisenmarke"
                                            placeholder="Klicke auf das Symbol, um auszuwählen."
                                            :class="[
                                                'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                                !form.spektrometer_daten.eisenmarke ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <button
                                            v-if="form.spektrometer_daten.abteilung"
                                            @click="showEisenmarkeModal = true"
                                            type="button"
                                            class="absolute right-2 top-1/2 -translate-y-1/2 inline-flex items-center justify-center rounded-full w-5 h-5 bg-indigo-100 text-indigo-600 hover:bg-indigo-200 hover:text-indigo-700 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                            title="Verfügbare Eisenmarken anzeigen">
                                            <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div v-if="sollwerteError" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">{{ sollwerteError }}</p>
                                    </div>
                                </div>

                                <!-- Analyse Form -->
                                <div class="col-span-2">
                                    <div class="grid grid-cols-4 gap-2">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Element</label>
                                            <div class="relative">
                                                <input type="text" v-model="analyseForm.element"
                                                    @keydown="handleKeyDown($event, 'istWert')"
                                                    @input="updateSollWert"
                                                    id="element"
                                                    placeholder="Klicke auf Symbol"
                                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                                
                                                <button 
                                                    v-if="sollwerte"
                                                    @click="showElementModal = true"
                                                    type="button"
                                                    class="absolute right-2 top-1/2 -translate-y-1/2 inline-flex items-center justify-center rounded-full w-5 h-5 bg-indigo-100 text-indigo-600 hover:bg-indigo-200 hover:text-indigo-700 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                    title="Verfügbare Elemente anzeigen">
                                                    <svg class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </button>
                                            </div>
                                            <!-- Hinweis-Text bei Nicht analysierbar -->
                                            <div v-if="analyseForm.element === 'Nicht analysierbar'" class="mt-1 text-xs text-red-600 flex items-center">
                                                <svg class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                </svg>
                                                Als nicht analysierbar markiert
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Ist-Wert</label>
                                            <input type="text" v-model="analyseForm.istWert"
                                                @keydown="handleKeyDown($event, 'sollWert')"
                                                id="istWert"
                                                placeholder="Eingeben"
                                                class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Soll-Wert</label>
                                            <input type="text" v-model="analyseForm.sollWert"
                                                @keydown="handleKeyDown($event, 'addAnalyseButton')"
                                                id="sollWert"
                                                placeholder="Autoload"
                                                :readonly="!!sollwerte"
                                                class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 disabled:bg-gray-100">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Toleranz</label>
                                            <div class="h-[38px] flex items-center">
                                                <div v-if="analyseForm.istWert && analyseForm.sollWert"
                                                    :class="[
                                                        'px-3 py-1.5 rounded-md flex items-center gap-1.5',
                                                        isWithinTolerance(analyseForm) ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                                                    ]">
                                                    <span class="font-medium">
                                                        {{ isWithinTolerance(analyseForm) ? 'Innerhalb' : 'Außerhalb' }}
                                                    </span>
                                                    <span class="text-gray-600">
                                                        ({{ getToleranceRange(analyseForm.sollWert) }})
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <div v-if="!analyseForm.element || (!analyseForm.istWert || !analyseForm.sollWert) && analyseForm.element !== 'Nicht analysierbar'" class="mb-2 text-sm text-pink-600">
                                            Bitte füllen Sie alle Felder aus
                                        </div>
                                        <div class="flex gap-2">
                                            <button type="button"
                                                id="addAnalyseButton"
                                                @click="addAnalyse"
                                                :disabled="!analyseForm.element || ((!analyseForm.istWert || !analyseForm.sollWert) && analyseForm.element !== 'Nicht analysierbar')"
                                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                                Analyse hinzufügen
                                            </button>
                                            
                                            <button type="button"
                                                @click="() => { 
                                                        if (analyseForm.element === 'Nicht analysierbar') {
                                                            // Zurücksetzen, wenn bereits 'Nicht analysierbar' ist
                                                            analyseForm.element = '';
                                                            analyseForm.istWert = '';
                                                            analyseForm.sollWert = '';
                                                            if (sollwerte) updateSollWert();
                                                        } else {
                                                            // Auf 'Nicht analysierbar' setzen
                                                            analyseForm.element = 'Nicht analysierbar';
                                                            analyseForm.istWert = '-';
                                                            analyseForm.sollWert = '-';
                                                        }
                                                    }"
                                                :class="[
                                                        'inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                        analyseForm.element === 'Nicht analysierbar' 
                                                            ? 'border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500' 
                                                            : 'border-gray-300 text-red-600 bg-white hover:bg-red-50 focus:ring-red-500'
                                                    ]"
                                                :title="analyseForm.element === 'Nicht analysierbar' ? 'Nicht analysierbar zurücksetzen' : 'Nicht analysierbar markieren'">
                                                <svg class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                {{ analyseForm.element === 'Nicht analysierbar' ? 'Zurücksetzen' : 'Nicht analysierbar' }}
                                            </button>
                                            
                                            <button type="button"
                                                v-if="false && analyseForm.element === 'Nicht analysierbar'"
                                                @click="addNichtAnalysierbar"
                                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                </svg>
                                                Nicht analysierbar hinzufügen
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Analysewerte Card -->
                                <div class="bg-white rounded-lg shadow-sm p-6 col-span-2">
                                    <div class="flex justify-between items-center mb-6">
                                        <h2 class="text-lg font-semibold text-gray-900">Analysewerte</h2>
                                    </div>

                                    <!-- Analysewerte List -->
                                    <div class="overflow-hidden rounded-lg border border-gray-200 w-full">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                                        ELEMENT
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                                        IST-WERT
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                                        SOLL-WERT
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                                        TOLERANZ
                                                    </th>
                                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                                                        AKTIONEN
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white">
                                                <tr v-if="form.spektrometer_daten.analysewerte.length === 0">
                                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500 whitespace-nowrap">
                                                        Noch keine Analysewerte vorhanden
                                                    </td>
                                                </tr>
                                                <tr v-for="(analyse, index) in form.spektrometer_daten.analysewerte" :key="index" class="border-t border-gray-200">
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ analyse.element }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ analyse.istWert }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ analyse.sollWert }}</td>
                                                    <td class="px-6 py-4 text-sm text-gray-500">
                                                        <span :class="[
                                                            'px-2 py-1 text-xs font-medium rounded inline-block',
                                                            isWithinTolerance(analyse) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                        ]">
                                                            <template v-if="!isWithinTolerance(analyse)">
                                                                {{ getToleranceDeviation(analyse) }}
                                                            </template>
                                                            <template v-else>
                                                                OK
                                                            </template>
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <button @click="form.spektrometer_daten.analysewerte.splice(index, 1)"
                                                            class="text-gray-400 hover:text-red-600">
                                                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Analysewerte Card -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <!-- ... existing Analysewerte content ... -->
                        </div>
                    </div>

                    <!-- Right Column (1/3) -->
                    <div class="space-y-6">
                        <!-- Fehlercode -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Fehlercode</h2>
                            <div class="grid grid-cols-1 gap-4">
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    <input
                                        type="text"
                                        v-model="form.spektrometer_daten.fehlercode"
                                        class="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        placeholder="Fehlercode"
                                        readonly
                                    />
                                    <button
                                        type="button"
                                        @click="showFehlercodeModal = true"
                                        class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        title="Verfügbare Fehlercodes anzeigen"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    
                                    <button
                                        v-if="form.spektrometer_daten.fehlercode"
                                        type="button"
                                        @click="form.spektrometer_daten.fehlercode = ''; selectedFehlercode = null"
                                        class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        title="Fehlercode löschen"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    
                                    <button
                                        type="button"
                                        @click="autoSuggestFehlercode"
                                        class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                        title="Fehlercode automatisch zuordnen"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div v-if="selectedFehlercode" class="mt-2 flex flex-col gap-2">
                                <div v-if="selectedFehlercode.teilesperren" class="inline-flex items-center px-2.5 py-1.5 rounded-md text-sm font-medium bg-red-100 text-red-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    Achtung: Dieser Fehlercode führt zu einer Teilesperrung!
                                </div>
                                
                                <div v-if="selectedFehlercode.massnahmen" class="inline-flex items-center px-2.5 py-1.5 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2h2a1 1 0 000-2H9z" clip-rule="evenodd" />
                                    </svg>
                                    Maßnahmen: {{ selectedFehlercode.massnahmen }}
                                </div>
                            </div>
                        </div>

                        <!-- Proben Card -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-lg font-semibold text-gray-900">Proben</h2>
                                <button type="button"
                                    @click="addProbe"
                                    :disabled="!probenForm.probenummer || !probenForm.gidNummer"
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Probe hinzufügen
                                </button>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Probennummer</label>
                                    <input type="text" v-model="probenForm.probenummer"
                                        @keydown="handleKeyDown($event, 'gidNummer')"
                                        id="probenummer"
                                        placeholder="123456"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                    <div v-if="!probenForm.probenummer" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Trag bitte eine Probennummer ein 🔢</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">GID Nummer</label>
                                    <input type="text" v-model="probenForm.gidNummer"
                                        @keydown="handleKeyDown($event, 'addProbeButton')"
                                        id="gidNummer"
                                        placeholder="123456"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                                    <div v-if="!probenForm.gidNummer" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-600">Trag bitte eine GID Nummer ein 🏷️</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Proben Table -->
                            <div class="mt-6 overflow-hidden rounded-lg border border-gray-200">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Probe-Nr.</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">GID-Nr.</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Aktionen</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-for="(probe, index) in form.spektrometer_daten.proben" :key="index">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ probe.probenummer }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ probe.gidNummer }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <button @click="form.spektrometer_daten.proben.splice(index, 1)"
                                                    class="text-gray-400 hover:text-red-600">
                                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- System Information -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">System Information</h2>
                            <div class="grid grid-cols-1 gap-4 mb-4">
                                <div>
                                    <span class="text-xs text-gray-500">Benutzer</span>
                                    <p class="text-sm font-medium text-gray-900">{{ form.spektrometer_daten.system_info.current.username }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Bemerkungen -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Bemerkungen</h2>
                            <textarea v-model="form.spektrometer_daten.bemerkungen"
                                rows="4"
                                placeholder="Bitte trage hier alle relevanten Anmerkungen zu den Teilen mit Rotekarte im laufenden Prozess ein."
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-3">
                            <button type="submit"
                                :disabled="form.processing"
                                class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                {{ form.processing ? 'Wird gespeichert...' : 'Speichern' }}
                            </button>
                            <Link :href="route('dashboard')"
                                class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Abbrechen
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Element Modal -->
        <div v-if="showElementModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-lg w-full max-h-[80vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">Verfügbare Elemente für EM {{ form.spektrometer_daten.eisenmarke }}</h3>
                    <button @click="showElementModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 gap-2">
                    <button
                        v-for="element in availableElements"
                        :key="element.key"
                        @click="selectElement(element)"
                        class="text-left p-3 hover:bg-gray-50 rounded-md border border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="font-medium">{{ element.key }}</span>
                                <span v-if="!['Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'].includes(element.key)"
                                      class="text-gray-600 text-sm ml-2">({{ getElementName(element.key) }})</span>
                            </div>
                            <span class="text-gray-500">{{ element.value }}</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Eisenmarke Modal -->
        <div v-if="showEisenmarkeModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-lg w-full max-h-[80vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium">Verfügbare {{ form.spektrometer_daten.abteilung }}-Eisenmarken</h3>
                    <button @click="showEisenmarkeModal = false" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 gap-2">
                    <button
                        v-for="eisenmarke in availableEisenmarken"
                        :key="eisenmarke.value"
                        @click="selectEisenmarke(eisenmarke)"
                        class="text-left p-3 hover:bg-gray-50 rounded-md border border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">EM {{ eisenmarke.value }}</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Fehlercode Modal -->
        <TransitionRoot appear :show="showFehlercodeModal" as="template">
            <Dialog as="div" @close="showFehlercodeModal = false" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95"
                        >
                            <DialogPanel class="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                                    Fehlercode auswählen
                                </DialogTitle>
                                <div class="mt-2">
                                    <div class="overflow-hidden rounded-lg border border-gray-200">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Fehlercode</th>                                            
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Eisenmarke</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Element</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Absolutwerte</th>
                                                    <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">Auswählen</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <tr v-for="code in availableFehlercodes" :key="code.id" class="hover:bg-gray-50">
                                                    <td class="px-4 py-2 text-sm text-gray-900">{{ code.fehlercode }}</td>                                                    
                                                    <td class="px-4 py-2 text-sm text-gray-500">{{ code.eisenmarke }}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-500">{{ code.element }}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-500">{{ code.absolutwerte }}</td>
                                                    <td class="px-4 py-2 text-sm text-center">
                                                        <button
                                                            @click="selectFehlercode(code)"
                                                            class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                        >
                                                            Auswählen
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <button
                                        type="button"
                                        class="inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                                        @click="showFehlercodeModal = false"
                                    >
                                        Schließen
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </AppLayout>
</template>
