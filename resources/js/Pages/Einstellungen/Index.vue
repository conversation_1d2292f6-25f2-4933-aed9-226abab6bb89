<script setup>
import { ref, computed } from 'vue';
import { Link, router, useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { usePage } from '@inertiajs/vue3';
import EmailVerteiler from './Tabs/EmailVerteiler.vue';
import Swal from 'sweetalert2';
import Swal from 'sweetalert2';

const props = defineProps({
    qsEmails: Array,
    hfEmails: Array,
    ggEmails: Array,
    qsKgEmails: Array,
    qsGgEmails: Array,
    ggNachbehandlungEmails: Array,
    kgNachbehandlungEmails: Array,
    abnahmebeauftragterEmails: Array,
    abnahmebeauftragterGgEmails: Array,
    statistikEmails: Array,
    managementEmails: Array,
    excelData: Object,
    lastSync: String,
    sollvorgaben: Array,
    kgEmails: Array,
});

const showMessage = (text, type = 'success') => {
    if (type === 'success') {
        Swal.fire({
            icon: 'success',
            title: 'Erfolgreich',
            text: text,
            showConfirmButton: true,
            confirmButtonText: 'Ok'
        });
    } else {
        Swal.fire({
            icon: 'error',
            title: 'Fehler',
            text: text,
            showConfirmButton: true,
            confirmButtonText: 'Ok'
        });
    }
};

const kgForm = useForm({
    type: 'kg',
    emails: props.kgEmails || []
});

const hfForm = useForm({
    type: 'hf',
    emails: props.hfEmails || []
});

const ggForm = useForm({
    type: 'gg',
    emails: props.ggEmails || []
});

const qsKgForm = useForm({
    type: 'qs_kg',
    emails: props.qsKgEmails || []
});

const qsGgForm = useForm({
    type: 'qs_gg',
    emails: props.qsGgEmails || []
});

const ggNachbehandlungForm = useForm({
    type: 'gg_nachbehandlung',
    emails: props.ggNachbehandlungEmails || []
});

const kgNachbehandlungForm = useForm({
    type: 'kg_nachbehandlung',
    emails: props.kgNachbehandlungEmails || []
});

const abnahmebeauftragterForm = useForm({
    type: 'abnahmebeauftragter',
    emails: props.abnahmebeauftragterEmails || []
});

const abnahmebeauftragterGgForm = useForm({
    type: 'abnahmebeauftragter_gg',
    emails: props.abnahmebeauftragterGgEmails || []
});

const statistikForm = useForm({
    type: 'statistik',
    emails: props.statistikEmails || []
});

const managementForm = useForm({
    type: 'management',
    emails: props.managementEmails || []
});

const newEmail = ref('');
const newEmailType = ref('kg');
const newEmailCc = ref(false);

const emailTypes = [
    { value: 'kg', label: 'Kleinguss (KG) Formanlage' },
    { value: 'kg_nachbehandlung', label: 'KG-Nachbehandlung' },
    { value: 'qs_kg', label: 'QS-Kleinguss' },
    { value: 'gg', label: 'Grossguss (GG) Formanlage' },
    { value: 'hf', label: 'Handformerei (HF)' },
    { value: 'gg_nachbehandlung', label: 'GG-Nachbehandlung' },
    { value: 'qs_gg', label: 'QS-Grossguss' },
    { value: 'abnahmebeauftragter', label: 'Abnahmebeauftragter Kleinguss' },
    { value: 'abnahmebeauftragter_gg', label: 'Abnahmebeauftragter Großguss' },
    { value: 'statistik', label: 'Statistik Empfänger' },
    { value: 'management', label: 'Management' }
];

const handleKeyDown = (event, nextElementId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        const nextElement = document.getElementById(nextElementId);
        if (nextElement) {
            nextElement.focus();
        }
    }
};

const addEmail = (type) => {
    let form;
    switch (type) {
        case 'qs_kg':
            form = qsKgForm;
            break;
        case 'qs_gg':
            form = qsGgForm;
            break;
        case 'hf':
            form = hfForm;
            break;
        case 'gg':
            form = ggForm;
            break;
        case 'kg':
            form = kgForm;
            break;
        case 'gg_nachbehandlung':
            form = ggNachbehandlungForm;
            break;
        case 'kg_nachbehandlung':
            form = kgNachbehandlungForm;
            break;
        case 'abnahmebeauftragter':
            form = abnahmebeauftragterForm;
            break;
        case 'abnahmebeauftragter_gg':
            form = abnahmebeauftragterGgForm;
            break;
        case 'statistik':
            form = statistikForm;
            break;
        case 'management':
            form = managementForm;
            break;
        default:
            return;
    }

    if (!newEmail.value) return;

    form.emails.push({
        email: newEmail.value,
        is_cc: newEmailCc.value,
    });

    form.post(route('einstellungen.store'), {
        preserveScroll: true,
        onSuccess: () => {
            newEmail.value = '';
            newEmailCc.value = false;
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich',
                text: 'E-Mail-Adresse wurde erfolgreich hinzugefügt!',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        },
        onError: () => {
            Swal.fire({
                icon: 'error',
                title: 'Fehler',
                text: 'Fehler beim Hinzufügen der E-Mail-Adresse',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        }
    });
};

const showConfirmDialog = ref(false);
const pendingDeleteInfo = ref(null);

const removeEmail = (type, index, emailId) => {
    pendingDeleteInfo.value = { type, index, emailId };
    showConfirmDialog.value = true;
};

const confirmDelete = () => {
    if (!pendingDeleteInfo.value) return;

    const { type, index, emailId } = pendingDeleteInfo.value;

    let form;
    switch (type) {
        case 'qs_kg':
            form = qsKgForm;
            break;
        case 'qs_gg':
            form = qsGgForm;
            break;
        case 'hf':
            form = hfForm;
            break;
        case 'gg':
            form = ggForm;
            break;
        case 'kg':
            form = kgForm;
            break;
        case 'gg_nachbehandlung':
            form = ggNachbehandlungForm;
            break;
        case 'kg_nachbehandlung':
            form = kgNachbehandlungForm;
            break;
        case 'abnahmebeauftragter':
            form = abnahmebeauftragterForm;
            break;
        case 'abnahmebeauftragter_gg':
            form = abnahmebeauftragterGgForm;
            break;
        case 'statistik':
            form = statistikForm;
            break;
        case 'management':
            form = managementForm;
            break;
        default:
            return;
    }

    router.post(`/einstellungen/email/${emailId}`, {
        _method: 'DELETE'
    }, {
        preserveScroll: true,
        onSuccess: () => {
            form.emails.splice(index, 1);
            showConfirmDialog.value = false;
            pendingDeleteInfo.value = null;
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich',
                text: 'E-Mail wurde erfolgreich entfernt!',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        },
        onError: () => {
            showConfirmDialog.value = false;
            pendingDeleteInfo.value = null;
            Swal.fire({
                icon: 'error',
                title: 'Fehler',
                text: 'Fehler beim Entfernen der E-Mail',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        }
    });
};

const toggleCc = (type, index) => {
    let form;
    switch (type) {
        case 'qs_kg':
            form = qsKgForm;
            break;
        case 'qs_gg':
            form = qsGgForm;
            break;
        case 'hf':
            form = hfForm;
            break;
        case 'gg':
            form = ggForm;
            break;
        case 'kg':
            form = kgForm;
            break;
        case 'gg_nachbehandlung':
            form = ggNachbehandlungForm;
            break;
        case 'kg_nachbehandlung':
            form = kgNachbehandlungForm;
            break;
        case 'abnahmebeauftragter':
            form = abnahmebeauftragterForm;
            break;
        case 'abnahmebeauftragter_gg':
            form = abnahmebeauftragterGgForm;
            break;
        case 'statistik':
            form = statistikForm;
            break;
        case 'management':
            form = managementForm;
            break;
        default:
            return;
    }

    const previousValue = form.emails[index].is_cc;
    form.emails[index].is_cc = !previousValue;

    form.post('/einstellungen', {
        preserveScroll: true,
        onSuccess: () => {
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich',
                text: 'CC Status wurde erfolgreich aktualisiert!',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        },
        onError: () => {
            form.emails[index].is_cc = previousValue; // Restore previous value if there was an error
            Swal.fire({
                icon: 'error',
                title: 'Fehler',
                text: 'Fehler beim Aktualisieren des CC Status',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        }
    });
};

// Add Excel functionality
const excelForm = useForm({
    excel_file: null
});

const deleteForm = useForm({});

const submitExcel = () => {
    excelForm.post(route('einstellungen.excel.import'), {
        preserveScroll: true,
        onSuccess: () => {
            excelForm.reset();
        }
    });
};

const deleteExcelData = () => {
    if (confirm('Sind Sie sicher, dass Sie alle Excel-Daten löschen möchten?')) {
        deleteForm.post(route('einstellungen.excel.destroy'), {
            preserveScroll: true
        });
    }
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <!-- Header -->
                <div class="bg-indigo-600 p-8 text-white">
                    <div class="flex items-center">
                        <svg class="h-8 w-8 text-indigo-100 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <div>
                            <h2 class="text-2xl font-bold">E-Mail-Verteiler</h2>
                            <p class="mt-2 text-indigo-100">Hier kannst du die E-Mail-Verteiler für die verschiedenen Abteilungen verwalten.</p>
                        </div>
                    </div>
                </div>



                <!-- Email Distribution List Content -->
                <div class="p-6">
                    <!-- New Email Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Neue E-Mail hinzufügen</h2>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">E-Mail-Adresse</label>
                                <input
                                    type="email"
                                    id="emailInput"
                                    v-model="newEmail"
                                    placeholder="<EMAIL>"
                                    @keydown="handleKeyDown($event, 'emailType')"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            </div>
                            <div class="mt-4">
                                <label for="emailType" class="block text-sm font-medium text-gray-700">Verteiler</label>
                                <select
                                    id="emailType"
                                    v-model="newEmailType"
                                    @keydown="handleKeyDown($event, 'emailCc')"
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                >
                                    <option v-for="type in emailTypes" :key="type.value" :value="type.value">
                                        {{ type.label }}
                                    </option>
                                </select>
                            </div>
                            <div class="flex items-center justify-between space-x-4">
                                <label class="inline-flex items-center">
                                    <input
                                        type="checkbox"
                                        id="emailCc"
                                        v-model="newEmailCc"
                                        @keydown="handleKeyDown($event, 'addEmailButton')"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <span class="ml-2 text-sm text-gray-900">Als CC</span>
                                </label>
                                <button
                                    id="addEmailButton"
                                    @click="addEmail(newEmailType)"
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Hinzufügen
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Email Lists Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Kleinguss (KG) -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Kleinguss (KG) Formanlage</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in kgForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('kg', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('kg', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="kgForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- KG-Nachbehandlung -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">KG-Nachbehandlung</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in kgNachbehandlungForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('kg_nachbehandlung', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('kg_nachbehandlung', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="kgNachbehandlungForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- QS-Kleinguss -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">QS-Kleinguss</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in qsKgForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('qs_kg', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('qs_kg', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="qsKgForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- Grossguss -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Grossguss (GG) Formanlage</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in ggForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('gg', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('gg', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="ggForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- Handformerei -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Handformerei (HF)</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in hfForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('hf', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('hf', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="hfForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- GG-Nachbehandlung -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">GG-Nachbehandlung</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in ggNachbehandlungForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('gg_nachbehandlung', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('gg_nachbehandlung', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="ggNachbehandlungForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- QS-Grossguss -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">QS-Grossguss</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in qsGgForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('qs_gg', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('qs_gg', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="qsGgForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                        <!-- Abnahmebeauftragter -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Abnahmebeauftragter Kleinguss</h2>
                            <div class="space-y-2">
                                <div v-for="(email, index) in abnahmebeauftragterForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('abnahmebeauftragter', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('abnahmebeauftragter', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="abnahmebeauftragterForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>

                        <!-- Abnahmebeauftragter GG Emails -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Abnahmebeauftragter Großguss</h3>
                            <!-- Email List -->
                            <div class="space-y-2">
                                <div v-for="(email, index) in abnahmebeauftragterGgForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('abnahmebeauftragter_gg', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('abnahmebeauftragter_gg', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="abnahmebeauftragterGgForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>

                        <!-- Statistik Empfänger -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Statistik Empfänger</h3>
                            <!-- Email List -->
                            <div class="space-y-2">
                                <div v-for="(email, index) in statistikForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('statistik', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('statistik', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="statistikForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>

                        <!-- Management -->
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Management</h3>
                            <!-- Email List -->
                            <div class="space-y-2">
                                <div v-for="(email, index) in managementForm.emails" :key="index"
                                    class="flex items-center justify-between p-2 rounded-md hover:bg-gray-50">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-gray-900">{{ email.email }}</span>
                                        <span v-if="email.is_cc"
                                            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                            CC
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button @click="toggleCc('management', index)"
                                            class="text-gray-400 hover:text-indigo-600">
                                            <span class="sr-only">Toggle CC</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </button>
                                        <button @click="removeEmail('management', index, email.id)"
                                            class="text-gray-400 hover:text-red-600">
                                            <span class="sr-only">Remove</span>
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="managementForm.emails.length === 0"
                                    class="text-sm text-gray-500 text-center py-4">
                                    Keine E-Mail-Adressen vorhanden
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>

    <!-- Confirmation Dialog -->
    <div v-if="showConfirmDialog" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showConfirmDialog = false"></div>

            <!-- Dialog panel -->
            <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            E-Mail-Adresse löschen
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Möchten Sie diese E-Mail-Adresse wirklich löschen?
                            </p>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button
                        type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                        @click="confirmDelete(); showConfirmDialog = false;"
                    >
                        Löschen
                    </button>
                    <button
                        type="button"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                        @click="showConfirmDialog = false; pendingDeleteInfo = null;"
                    >
                        Abbrechen
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

