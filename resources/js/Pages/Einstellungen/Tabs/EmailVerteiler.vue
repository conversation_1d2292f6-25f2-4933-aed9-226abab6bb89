<template>
    <div class="max-w-7xl mx-auto">
        <!-- Header Card -->
        <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        E-Mail-Verteiler
                    </h1>
                    <p class="mt-1 text-sm text-gray-500">
                        Verwalten Sie hier die E-Mail-Verteilerlisten für verschiedene Abteilungen
                    </p>
                </div>
            </div>
        </div>

        <!-- Content from parent component's E-Mail-Verteiler tab -->
        <slot></slot>
    </div>
</template>

<script setup>
defineProps({
    emails: {
        type: Array,
        default: () => []
    }
});
</script>
