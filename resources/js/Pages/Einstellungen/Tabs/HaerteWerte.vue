<script setup>
import { ref, computed } from 'vue';
import { useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
    werte: {
        type: Array,
        required: true
    }
});

const form = useForm({
    werte: props.werte
});

const searchTerm = ref('');
const selectedMaterial = ref('all');
const message = ref('');
const messageType = ref('');
const showMessage = ref(false);

const filteredWerte = computed(() => {
    return form.werte.filter(wert =>
        (selectedMaterial.value === 'all' || wert.werkstoff === selectedMaterial.value) &&
        (wert.werkstoff.toLowerCase().includes(searchTerm.value.toLowerCase()))
    );
});

const uniqueMaterials = computed(() => {
    return [...new Set(form.werte.map(wert => wert.werkstoff))];
});

const displayMessage = (text, type = 'success') => {
    message.value = text;
    messageType.value = type;
    showMessage.value = true;
    setTimeout(() => {
        showMessage.value = false;
    }, 3000);
};

const updateWerte = () => {
    form.put(route('haertewerte.update'), {
        preserveScroll: true,
        onSuccess: () => {
            displayMessage('Härtewerte wurden erfolgreich gespeichert');
        },
        onError: () => {
            displayMessage('Fehler beim Speichern der Härtewerte', 'error');
        }
    });
};

const addNewRow = () => {
    form.werte.push({
        werkstoff: '',
        wanddicke: '',
        toleranz_min: null,
        toleranz_max: null
    });
};

const removeRow = (index) => {
    form.werte.splice(index, 1);
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto py-6">
            <!-- Success/Error Message -->
            <div
                v-show="showMessage"
                class="fixed top-4 right-4 z-50 max-w-sm w-full"
                @click="showMessage = false"
            >
                <div
                    class="bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300"
                    :class="{
                        'border-l-4 border-green-400': messageType === 'success',
                        'border-l-4 border-red-400': messageType === 'error'
                    }"
                >
                    <div class="p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg
                                    v-if="messageType === 'success'"
                                    class="h-6 w-6 text-green-400"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                                <svg
                                    v-else
                                    class="h-6 w-6 text-red-400"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                            </div>
                            <div class="ml-3 w-0 flex-1">
                                <p
                                    class="text-sm font-medium"
                                    :class="{
                                        'text-green-900': messageType === 'success',
                                        'text-red-900': messageType === 'error'
                                    }"
                                >
                                    {{ message }}
                                </p>
                            </div>
                            <div class="ml-4 flex-shrink-0 flex">
                                <button
                                    class="inline-flex text-gray-400 hover:text-gray-500"
                                    @click="showMessage = false"
                                >
                                    <span class="sr-only">Schließen</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path
                                            fill-rule="evenodd"
                                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-blue-800 p-4 sm:p-6">
                    <h2 class="text-xl font-semibold text-white">
                        Härtewerte Verwaltung
                    </h2>
                    <p class="mt-1 text-sm text-blue-100">
                        Hier kannst du die Härtewerte für alle Eisenmarken verwalten.
                    </p>
                </div>

                <!-- Search and Filter -->
                <div class="p-4 border-b">
                    <div class="flex flex-col sm:flex-row gap-3">
                        <div class="relative flex-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                            </svg>
                            <input
                                type="text"
                                placeholder="Material suchen..."
                                class="w-full pl-9 pr-4 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                v-model="searchTerm"
                            >
                        </div>
                        <select
                            class="px-3 py-2 text-sm border rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            v-model="selectedMaterial"
                        >
                            <option value="all">EM</option>
                            <option v-for="material in uniqueMaterials" :key="material" :value="material">
                                {{ material }}
                            </option>
                        </select>
                    </div>
                </div>

                <!-- Table -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wanddicke [mm]</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Min. HBW</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Max. HBW</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Aktionen</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr v-for="(wert, index) in filteredWerte" :key="index" class="hover:bg-gray-50">
                                <td class="px-4 py-2">
                                    <input
                                        type="text"
                                        v-model="wert.werkstoff"
                                        class="w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </td>
                                <td class="px-4 py-2">
                                    <input
                                        type="text"
                                        v-model="wert.wanddicke"
                                        class="w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </td>
                                <td class="px-4 py-2">
                                    <input
                                        type="number"
                                        v-model="wert.toleranz_min"
                                        class="w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </td>
                                <td class="px-4 py-2">
                                    <input
                                        type="number"
                                        v-model="wert.toleranz_max"
                                        class="w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </td>
                                <td class="px-4 py-2 text-center">
                                    <button
                                        @click="removeRow(index)"
                                        class="text-red-600 hover:text-red-800"
                                        title="Zeile löschen"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 6h18"></path>
                                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                        </svg>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Actions -->
                <div class="p-4 border-t bg-gray-50 flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <Link
                            :href="route('dashboard')"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                            Dashboard
                        </Link>
                        <button
                            @click="addNewRow"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Neue Zeile
                        </button>
                    </div>

                    <button
                        @click="updateWerte"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        :disabled="form.processing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                            <polyline points="17 21 17 13 7 13 7 21"></polyline>
                            <polyline points="7 3 7 8 15 8"></polyline>
                        </svg>
                        Speichern
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
