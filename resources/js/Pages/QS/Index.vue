<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm, Link } from '@inertiajs/vue3';
import HBWTableModal from '@/Components/HBWTableModal.vue';
import QSDocumentUpload from '@/Components/QSDocumentUpload.vue';
import Swal from 'sweetalert2';
import axios from 'axios';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: false,
        default: null
    }
});

const message = ref('');
const messageType = ref('');

// Formatiere das Datum
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

// Bestimme den QS-Typ basierend auf der Abteilung
const determineQsType = () => {
    if (!props.rotekarte?.spektrometer_daten?.abteilung) {
        return 'kleinguss';
    }

    return ['GG', 'HF'].includes(props.rotekarte.spektrometer_daten.abteilung) ? 'grossguss' : 'kleinguss';
};

const form = useForm({
    rotekarte_id: props.rotekarte?.id,
    qs_daten: {
        qs_type: props.rotekarte?.spektrometer_daten?.abteilung === 'GG' ? 'grossguss' : 'kleinguss',
        pruefer: [],
        vorschlag_pruefungen: props.rotekarte?.spektrometer_daten?.fehlercode ? '' : '',
        teile: props.rotekarte?.formanlage_daten?.teile?.map(teil => ({
            teilenummer: teil.teilenummer,
            anzahl: teil.anzahl,
            gepruefteTeile: '',
            io_nio: null
        })) || [],
        io_nio: 'IO',
        bemerkungen: [],
        status: 'Offen',
        serie_status: 'Entscheidung ausstehend',
        haertewerte: props.rotekarte?.qs_daten?.haertewerte || [],
        system_info: {
            current: {
                username: '',
                hostname: '',
                timestamp: ''
            },
            history: []
        },
        ...props.rotekarte?.qs_daten || {}
    }
});

const newRemark = ref('');
const showNewRemarkForm = ref(false);

const addNewRemark = () => {
    if (newRemark.value.trim()) {
        form.qs_daten.bemerkungen.push({
            text: newRemark.value,
            pruefer: form.qs_daten.system_info.current.username,
            timestamp: new Date().toLocaleString('de-DE'),
            locked: false
        });
        newRemark.value = '';
        showNewRemarkForm.value = false;
    }
};

const lockAllRemarks = () => {
    form.qs_daten.bemerkungen.forEach(remark => {
        remark.locked = true;
    });
};

// Capture system information
const captureSystemInfo = async () => {
    try {
        const response = await fetch('/system-info', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('System Info Response:', data);

        if (data.username || data.hostname) {
            form.qs_daten.system_info.current = {
                username: data.username || 'Nicht verfügbar',
                hostname: data.hostname || 'Nicht verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        } else {
            console.warn('No system info data received:', data);
            form.qs_daten.system_info.current = {
                username: 'Keine Daten verfügbar',
                hostname: 'Keine Daten verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        }
    } catch (error) {
        console.error('Error fetching system info:', error);
        form.qs_daten.system_info.current = {
            username: 'Systemfehler',
            hostname: 'Systemfehler',
            timestamp: new Date().toLocaleString('de-DE')
        };
    }
};

const fetchPruefvorschlag = async () => {
    if (props.rotekarte?.spektrometer_daten?.fehlercode) {
        try {
            const response = await axios.get(route('pruefkatalog.fehlercodes'));
            const fehlercode = response.data.find(
                code => code.fehlercode === props.rotekarte.spektrometer_daten.fehlercode
            );
            if (fehlercode) {
                form.qs_daten.vorschlag_pruefungen = fehlercode.massnahmen;
            }
        } catch (error) {
            console.error('Fehler beim Laden des Prüfvorschlags:', error);
        }
    }
};

// Call on component mount
onMounted(() => {
    captureSystemInfo();
    fetchPruefvorschlag();
    
    // Initialize haertewerte if needed
    if (!form.qs_daten.haertewerte) {
        form.qs_daten.haertewerte = [];
    }
    
    // Set initial values from lastHaerteValues if available
    if (form.qs_daten.haertewerte.length > 0) {
        const lastEntry = form.qs_daten.haertewerte[form.qs_daten.haertewerte.length - 1];
        lastHaerteValues.value = {
            eisenmarke: lastEntry.eisenmarke,
            nestnummer: lastEntry.nestnummer,
            soll_von: lastEntry.soll_von,
            soll_bis: lastEntry.soll_bis
        };
        haerteForm.value = {
            ...lastHaerteValues.value,
            ist_wert: ''
        };
    } else if (props.rotekarte?.spektrometer_daten?.eisenmarke) {
        // If no entries but we have an eisenmarke from rotekarte, use it
        haerteForm.value.eisenmarke = props.rotekarte.spektrometer_daten.eisenmarke;
        lastHaerteValues.value.eisenmarke = props.rotekarte.spektrometer_daten.eisenmarke;
    }
});

console.log('Gesetzter QS Typ:', form.qs_daten.qs_type);

const teilForm = ref({
    teilenummer: '',
    anzahl: '',
    gepruefteTeile: ''
});

const addTeil = () => {
    form.qs_daten.teile.push({
        teilenummer: teilForm.value.teilenummer,
        anzahl: teilForm.value.anzahl,
        gepruefteTeile: teilForm.value.gepruefteTeile,
        io_nio: null
    });
    teilForm.value.teilenummer = '';
    teilForm.value.anzahl = '';
    teilForm.value.gepruefteTeile = '';
};

const prueferForm = ref({
    name: '',
    datum: ''
});

const addPruefer = () => {
    form.qs_daten.pruefer.push({
        name: prueferForm.value.name,
        datum: prueferForm.value.datum
    });
    prueferForm.value.name = '';
    prueferForm.value.datum = '';
};

const submit = async () => {
    // Lock all remarks before submission
    lockAllRemarks();

    // Automatically save haertewerte if there are any
    if (form.qs_daten.haertewerte && form.qs_daten.haertewerte.length > 0) {
        try {
            console.log('Automatically saving Härtewerte before form submission');
            await axios.post(route('qs.save-haertewerte'), {
                rotekarte_id: props.rotekarte.id,
                haertewerte: form.qs_daten.haertewerte
            });
            console.log('Härtewerte saved successfully');
        } catch (error) {
            console.error('Error saving Härtewerte before form submission:', error);
            // Continue with form submission even if haertewerte save fails
        }
    }

    // Add current system info to history
    const currentInfo = {
        username: form.qs_daten.system_info.current.username,
        hostname: form.qs_daten.system_info.current.hostname,
        timestamp: new Date().toLocaleString('de-DE')
    };

    // Ensure history array exists
    if (!Array.isArray(form.qs_daten.system_info.history)) {
        form.qs_daten.system_info.history = [];
    }

    // Add to history
    form.qs_daten.system_info.history.push(currentInfo);

    // Validate required fields
    if (!form.qs_daten.pruefer.length) {
        Swal.fire({
            icon: 'error',
            title: 'Fehlende Angaben',
            text: 'Bitte mindestens einen Prüfer hinzufügen.'
        });
        return;
    }

    // Validate that teile array is not empty
    if (!form.qs_daten.teile.length) {
        Swal.fire({
            icon: 'error',
            title: 'Fehlende Angaben',
            text: 'Bitte mindestens ein Teil hinzufügen.'
        });
        return;
    }

    // Validate that all parts have geprüfte Teile and IO/NIO status
    const invalidTeile = form.qs_daten.teile.filter(teil =>
        !teil.gepruefteTeile || teil.gepruefteTeile === '0' || teil.io_nio === null
    );

    if (invalidTeile.length > 0) {
        Swal.fire({
            icon: 'error',
            title: 'Unvollständige Angaben',
            text: 'Bitte für alle Teile die Anzahl der geprüften Teile eingeben und IO/NIO Status auswählen.'
        });
        return;
    }

    // Log form data before submission
    console.log('Submitting form data:', {
        rotekarte_id: form.rotekarte_id,
        qs_daten: form.qs_daten
    });

    // Submit the form
    form.post(route('qs.store'), {
        preserveScroll: true,
        onSuccess: () => {
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich gespeichert',
                text: 'Die QS-Daten wurden erfolgreich gespeichert.',
                showConfirmButton: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = route('dashboard');
                }
            });
        },
        onError: (errors) => {
            console.error('Fehler beim Speichern:', errors);
            let errorMessage = 'Folgende Fehler sind aufgetreten:\n';
            Object.entries(errors).forEach(([field, messages]) => {
                errorMessage += `${messages.join(', ')}\n`;
            });
            
            Swal.fire({
                icon: 'error',
                title: 'Fehler beim Speichern',
                text: errorMessage,
                showConfirmButton: true
            });
        }
    });
};

// Füge Validierungsfehler-Anzeige hinzu
const showError = (field) => {
    return form.errors[field] ? form.errors[field] : '';
};

const handleKeyDown = (event, nextFieldId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (nextFieldId === 'saveButton') {
            form.post(route('qs.store'));
        } else {
            document.getElementById(nextFieldId)?.focus();
        }
    }
};

const showHBWTable = ref(false);

// Verschrottungskosten
const scrapCalculation = ref(null);
const calculatingScrap = ref(false);

const calculateScrapCosts = async () => {
    if (!form.qs_daten.teile || form.qs_daten.teile.length === 0) {
        showToast('Keine Teile vorhanden für die Berechnung.', 'warning');
        return;
    }

    calculatingScrap.value = true;
    try {
        const response = await axios.post(route('qs.calculate-scrap-costs'), {
            teile: form.qs_daten.teile
        });
        
        if (response.data.success) {
            scrapCalculation.value = response.data;
            console.log('Scrap calculation result:', response.data); // Debug-Ausgabe
        } else {
            showToast('Fehler bei der Verschrottungsberechnung.', 'error');
        }
    } catch (error) {
        console.error('Fehler bei der Verschrottungsberechnung:', error);
        showToast('Fehler bei der Verschrottungsberechnung.', 'error');
    } finally {
        calculatingScrap.value = false;
    }
};

const formatCurrency = (value) => {
    if (typeof value !== 'number') return '0,00 €';
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
};

const formatWeight = (value) => {
    if (typeof value !== 'number') return '0,00 kg';
    return new Intl.NumberFormat('de-DE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value) + ' kg';
};

const showToast = (message, type = 'info') => {
    // Implementation...
};

// Add new refs and methods for Härtewerte
const haerteForm = ref({
    eisenmarke: props.rotekarte?.spektrometer_daten?.eisenmarke || '',
    soll_von: '',
    soll_bis: '',
    ist_wert: '',
    nestnummer: ''
});

const lastHaerteValues = ref({
    eisenmarke: props.rotekarte?.spektrometer_daten?.eisenmarke || '',
    nestnummer: props.rotekarte?.spektrometer_daten?.nestnummer || '',
    soll_von: '',
    soll_bis: ''
});

const addHaertewert = () => {
    if (!form.qs_daten.haertewerte) {
        form.qs_daten.haertewerte = [];
    }

    // Validate required fields
    if (!haerteForm.value.eisenmarke || !haerteForm.value.soll_von || 
        !haerteForm.value.soll_bis || !haerteForm.value.ist_wert || 
        !haerteForm.value.nestnummer) {
        return;
    }

    // Save current values for next entry
    lastHaerteValues.value = {
        eisenmarke: haerteForm.value.eisenmarke,
        nestnummer: haerteForm.value.nestnummer,
        soll_von: haerteForm.value.soll_von,
        soll_bis: haerteForm.value.soll_bis
    };

    // Add new entry
    form.qs_daten.haertewerte.push({
        eisenmarke: haerteForm.value.eisenmarke,
        nestnummer: haerteForm.value.nestnummer,
        soll_von: parseFloat(haerteForm.value.soll_von),
        soll_bis: parseFloat(haerteForm.value.soll_bis),
        ist_wert: parseFloat(haerteForm.value.ist_wert)
    });

    // Save the current active element to refocus if needed
    const activeElement = document.activeElement;

    // Reset only the ist_wert, keep other values
    haerteForm.value.ist_wert = '';
    
    // Focus back on the IST-Wert input field for the next entry
    nextTick(() => {
        // Try to find the input field by placeholder
        const istWertInput = document.querySelector('input[placeholder="Ist-Wert eingeben"]');
        if (istWertInput) {
            istWertInput.focus();
        } else if (activeElement) {
            // If we can't find the input, at least focus back on the previously active element
            activeElement.focus();
        }
    });
};

const calculateAverageHaertewert = computed(() => {
    if (!form.qs_daten.haertewerte || form.qs_daten.haertewerte.length === 0) {
        return '0';
    }

    const total = form.qs_daten.haertewerte.reduce((sum, wert) => sum + parseFloat(wert.ist_wert), 0);
    return (total / form.qs_daten.haertewerte.length).toFixed(2);
});

const isOutOfRange = (wert) => {
    const istWert = parseFloat(wert.ist_wert);
    const sollVon = parseFloat(wert.soll_von);
    const sollBis = parseFloat(wert.soll_bis);
    
    return istWert < sollVon || istWert > sollBis;
};

const downloadHaertewertePDF = () => {
    try {
        // Neues PDF im A4 Format erstellen
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // Heidelberg Blau Farbdefinitionen (Corporate Design)
        const colors = {
            primary: [0, 48, 98],         // Heidelberg Dunkelblau
            secondary: [0, 90, 170],      // Heidelberg Mittelblau
            tertiary: [100, 160, 200],    // Heidelberg Hellblau
            success: [39, 174, 96],       // Grün
            danger: [192, 57, 43],        // Rot
            warning: [255, 152, 0],       // Orange
            info: [0, 80, 140],           // Heidelberg Blau Variante
            dark: [33, 33, 33],           // Dunkelgrau
            light: [245, 245, 245],       // Hellgrau
            text: [68, 68, 68],           // Textfarbe
            lightText: [120, 120, 120]    // Helle Textfarbe
        };
        
        // Header mit Farbverlauf im Heidelberg Blau
        doc.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        doc.rect(0, 0, 210, 30, 'F');
        
        // Weißes Rechteck für Logo-Hintergrund
        doc.setFillColor(255, 255, 255);
        doc.roundedRect(5, 2.5, 35, 25, 3, 3, 'F');
        
        // Logo in den Header einfügen (links oben)
        // Verwende das lokal gespeicherte Heidelberger Logo
        const logoPath = '/images/HD_Logo_175.png';
        
        // Füge das Logo direkt in den Header ein
        doc.addImage(logoPath, 'PNG', 10, 5, 25, 20);
        
        // Title ohne Datum
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(22);
        doc.setFont('helvetica', 'bold');
        doc.text('Härtewertprüfung Protokoll', 105, 15, { align: 'center' });
        
        // Aktuelles Datum für das PDF
        const today = new Date();
        const dateStr = today.toLocaleDateString('de-DE');
        
        // Infobox für Auftragsdaten
        doc.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        doc.setFillColor(240, 245, 255); // Sehr helles Heidelberg Blau
        doc.roundedRect(10, 35, 90, 40, 3, 3, 'FD');
        
        // Auftragsdaten
        doc.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Auftragsdaten', 15, 43);
        
        doc.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text(`Rotekarte:`, 15, 51);
        doc.text(`Abteilung:`, 15, 57);
        doc.text(`Eisenmarke:`, 15, 63);
        
        doc.setFont('helvetica', 'bold');
        doc.text(`RT-${props.rotekarte.id}`, 50, 51);
        doc.text(`${props.rotekarte?.spektrometer_daten?.abteilung || '-'}`, 50, 57);
        doc.text(`${props.rotekarte?.spektrometer_daten?.eisenmarke || '-'}`, 50, 63);
        
        // Infobox für Sollwerte
        doc.setDrawColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        doc.setFillColor(240, 245, 255); // Sehr helles Heidelberg Blau
        doc.roundedRect(110, 35, 90, 40, 3, 3, 'FD');
        
        // Prüfdaten
        doc.setTextColor(colors.primary[0], colors.primary[1], colors.primary[2]);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Prüfdaten', 115, 43);
        
        doc.setTextColor(colors.text[0], colors.text[1], colors.text[2]);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        
        // Bestimme Zeilenhöhen für mehr Inhalte
        const rowHeight = 5;
        let row = 1;
        
        // Soll-Werte aus ersten Härtewert entnehmen (falls vorhanden)
        const ersterHaertewert = form.qs_daten.haertewerte[0] || {};
        
        doc.text(`Sollwert von:`, 115, 43 + rowHeight * row);
        row++;
        doc.text(`Sollwert bis:`, 115, 43 + rowHeight * row);
        row++;
        doc.text(`Prüfverfahren:`, 115, 43 + rowHeight * row);
        row++;
        doc.text(`Datum:`, 115, 43 + rowHeight * row);
        row++;
        doc.text(`Geprüft von:`, 115, 43 + rowHeight * row);
        
        // Werte rechts einfügen
        doc.setFont('helvetica', 'bold');
        row = 1;
        
        doc.text(`${ersterHaertewert.soll_von || '-'}`, 150, 43 + rowHeight * row);
        row++;
        doc.text(`${ersterHaertewert.soll_bis || '-'}`, 150, 43 + rowHeight * row);
        row++;
        doc.text(`Brinellhärte`, 150, 43 + rowHeight * row);
        row++;
        doc.text(`${dateStr}`, 150, 43 + rowHeight * row);
        row++;
        // Prüfer aus dem QS-Formular verwenden, falls vorhanden
        const pruefer = form.qs_daten.pruefer.length > 0 ? form.qs_daten.pruefer[0].name : '-';
        doc.text(`${pruefer}`, 150, 43 + rowHeight * row);
        
        // Durchschnittswert mit visuellem Indikator
        const durchschnitt = calculateAverageHaertewert.value;
        const sollVon = parseFloat(ersterHaertewert.soll_von || 0);
        const sollBis = parseFloat(ersterHaertewert.soll_bis || 0);
        const durchschnittNum = parseFloat(durchschnitt);
        const istDurchschnittIO = !isNaN(durchschnittNum) && !isNaN(sollVon) && !isNaN(sollBis) && 
                               durchschnittNum >= sollVon && durchschnittNum <= sollBis;
        
        // Box für Durchschnittswert
        doc.setDrawColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        doc.setFillColor(
            ...(istDurchschnittIO 
                ? [colors.success[0] * 0.9, colors.success[1] * 0.9, colors.success[2] * 0.9]
                : [colors.danger[0] * 0.9, colors.danger[1] * 0.9, colors.danger[2] * 0.9])
        );
        doc.roundedRect(110, 80, 90, 16, 3, 3, 'FD');
        
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(11);
        doc.setFont('helvetica', 'bold');
        doc.text('Durchschnitt Ø HBW:', 115, 89);
        doc.setFontSize(12);
        doc.text(`${durchschnitt}`, 175, 89, { align: 'right' });
        
        // IO/NIO Zusammenfassung
        const ioCount = form.qs_daten.haertewerte.filter(hw => !isOutOfRange(hw)).length;
        const nioCount = form.qs_daten.haertewerte.length - ioCount;
        const ioPercent = Math.round((ioCount / form.qs_daten.haertewerte.length) * 100);
        
        // Box für IO/NIO Zusammenfassung
        doc.setDrawColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        doc.setFillColor(245, 245, 245);
        doc.roundedRect(10, 80, 90, 16, 3, 3, 'FD');
        
        doc.setTextColor(colors.dark[0], colors.dark[1], colors.dark[2]);
        doc.setFontSize(11);
        doc.setFont('helvetica', 'bold');
        doc.text('Prüfergebnis:', 15, 89);
        
        // IO-Werte
        doc.setTextColor(colors.success[0], colors.success[1], colors.success[2]);
        doc.text(`IO: ${ioCount} (${ioPercent}%)`, 55, 89);
        
        // NIO-Werte
        doc.setTextColor(colors.danger[0], colors.danger[1], colors.danger[2]);
        doc.text(`NIO: ${nioCount}`, 85, 89);
        
        // Tabelle mit Härtewerten generieren
        const tableData = form.qs_daten.haertewerte.map((hw, index) => {
            const isIo = !isOutOfRange(hw);
            return [
                index + 1, // Zeilennummer
                hw.nestnummer || '-',
                `${hw.soll_von} - ${hw.soll_bis}`,
                hw.ist_wert,
                {
                    content: isIo ? 'IO' : 'NIO',
                    styles: {
                        halign: 'center',
                        fillColor: isIo ? [230, 246, 236] : [252, 232, 232],
                        textColor: isIo ? [39, 174, 96] : [192, 57, 43],
                        fontStyle: 'bold'
                    }
                }
            ];
        });
        
        autoTable(doc, {
            startY: 105,
            head: [['Nr.', 'Nestnummer', 'Soll-Wert', 'IST-Wert', 'Status']],
            body: tableData,
            headStyles: {
                fillColor: colors.primary,
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                halign: 'center',
                valign: 'middle'
            },
            styles: {
                fontSize: 10,
                cellPadding: 5,
                lineColor: [220, 220, 220],
                lineWidth: 0.1,
                halign: 'center', 
                valign: 'middle',
                overflow: 'linebreak'
            },
            alternateRowStyles: {
                fillColor: [248, 248, 248]
            },
            // Optimierte Spaltenbreiten für volle Seitenbreite
            columnStyles: {
                0: { cellWidth: '8%', halign: 'center' },      // Nr.
                1: { cellWidth: '22%', halign: 'center' },     // Nestnummer
                2: { cellWidth: '35%', halign: 'center' },     // Soll-Wert
                3: { cellWidth: '20%', halign: 'center' },     // IST-Wert
                4: { cellWidth: '15%', halign: 'center' }      // Status
            },
            margin: { left: 10, right: 10 },
            tableWidth: '100%',
            pageBreak: 'auto',
            theme: 'grid',
            didParseCell: function(data) {
                // Tabellentitel hervorheben
                if (data.row.index === 0) {
                    data.cell.styles.fontStyle = 'bold';
                }
            }
        });
        
        // Fußzeile
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFillColor(colors.primary[0], colors.primary[1], colors.primary[2]);
            doc.rect(0, 280, 210, 17, 'F');
            
            doc.setFontSize(9);
            doc.setTextColor(255, 255, 255);
            doc.setFont('helvetica', 'normal');
            doc.text(`Seite ${i} von ${pageCount}`, 195, 287, { align: 'right' });
        }
        
        // Dateiname generieren und PDF speichern
        doc.save(`Haertewerte_RT-${props.rotekarte.id}_${dateStr.replace(/\./g, '')}.pdf`);
        
        // Erfolgsbenachrichtigung
        Swal.fire({
            icon: 'success',
            title: 'PDF wurde erfolgreich erstellt',
            showConfirmButton: false,
            timer: 2000
        });
    } catch (error) {
        console.error('Fehler beim PDF-Export:', error);
        Swal.fire({
            icon: 'error',
            title: 'Fehler',
            text: 'Beim Erstellen des PDFs ist ein Fehler aufgetreten.',
            showConfirmButton: true
        });
    }
};

// Add this after the last const declaration in the script section
const saveHaertewerte = async () => {
    try {
        const response = await axios.post(route('qs.save-haertewerte'), {
            rotekarte_id: props.rotekarte.id,
            haertewerte: form.qs_daten.haertewerte
        });

        if (response.data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich gespeichert',
                text: 'Die Härtewerte wurden erfolgreich in der Datenbank gespeichert.',
                showConfirmButton: true,
                timer: 2000
            });
        } else {
            throw new Error('Fehler beim Speichern');
        }
    } catch (error) {
        console.error('Fehler beim Speichern der Härtewerte:', error);
        Swal.fire({
            icon: 'error',
            title: 'Fehler',
            text: 'Die Härtewerte konnten nicht gespeichert werden. Bitte versuchen Sie es erneut.',
            showConfirmButton: true
        });
    }
};

const deleteHaertewert = async (index) => {
    try {
        console.log('Versuche Härtewert zu löschen mit Index:', index);
        console.log('Aktuelle Härtewerte:', JSON.stringify(form.qs_daten.haertewerte));
        
        // Create a copy of the array without the item to delete
        const updatedHaertewerte = [...form.qs_daten.haertewerte];
        updatedHaertewerte.splice(index, 1);
        
        console.log('Neue Härtewerte nach Löschen:', JSON.stringify(updatedHaertewerte));
        
        // Save to the database
        const response = await axios.post(route('qs.save-haertewerte'), {
            rotekarte_id: props.rotekarte.id,
            haertewerte: updatedHaertewerte
        });

        console.log('API-Antwort:', response.data);

        if (response.data.success) {
            // Only update the form data after successful save
            form.qs_daten.haertewerte = updatedHaertewerte;
            console.log('Härtewerte aktualisiert im Form-Objekt');
            
            // Wenn es das letzte Element war und wir jetzt auf der Dashboard-Seite landen,
            // dann wird keine Meldung angezeigt. Daher ein kürzeres Timeout.
            const timer = updatedHaertewerte.length === 0 ? 500 : 2000;
            
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich gelöscht',
                text: 'Der Härtewert wurde erfolgreich gelöscht.',
                showConfirmButton: true,
                timer: timer
            });
            
            // Wenn es das letzte Element war, fügen wir ein kurzes Timeout hinzu,
            // damit die Erfolgsmeldung angezeigt werden kann, bevor etwas passiert
            if (updatedHaertewerte.length === 0) {
                // Keine weitere Aktion nötig, wir verbleiben auf der Seite
                // und bestätigen nur, dass das letzte Element gelöscht wurde
            }
        } else {
            throw new Error('Fehler beim Löschen');
        }
    } catch (error) {
        console.error('Fehler beim Löschen des Härtewerts:', error);
        Swal.fire({
            icon: 'error',
            title: 'Fehler',
            text: 'Der Härtewert konnte nicht gelöscht werden. Bitte versuchen Sie es erneut.',
            showConfirmButton: true
        });
    }
};

const deleteTeil = (index) => {
    // For Teile, we don't need to do a server-side operation before removing
    form.qs_daten.teile.splice(index, 1);
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto">
            <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                            QS Bearbeitung 
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-lg font-bold bg-indigo-100 text-indigo-800">
                                RT-{{ rotekarte.id }}
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                :class="{
                                    'bg-blue-100 text-blue-800': rotekarte?.spektrometer_daten?.abteilung === 'NG',
                                    'bg-green-100 text-green-800': rotekarte?.spektrometer_daten?.abteilung === 'GG',
                                    'bg-purple-100 text-purple-800': rotekarte?.spektrometer_daten?.abteilung === 'HF'
                                }">
                                {{ {
                                    'NG': 'Nassgussanlage',
                                    'GG': 'Großgussanlage',
                                    'HF': 'Handformerei'
                                }[rotekarte?.spektrometer_daten?.abteilung] || rotekarte?.spektrometer_daten?.abteilung }}
                            </span>
                            <span v-if="rotekarte?.spektrometer_daten?.eisenmarke" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                Eisenmarke {{ rotekarte.spektrometer_daten.eisenmarke }}
                            </span>
                            <button
                                @click="showHBWTable = true"
                                class="ml-auto inline-flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                                HBW-Tabelle
                            </button>
                        </h1>

                        <HBWTableModal
                            :show="showHBWTable"
                            @close="showHBWTable = false"
                        />
                    </div>
                </div>
            </div>

            <!-- Modern Validation Message -->
            <div v-if="message"
                class="fixed top-4 right-4 max-w-sm w-full bg-white rounded-lg shadow-lg pointer-events-auto border-l-4 transform transition-all duration-300"
                :class="{
                    'border-pink-400 animate-bounce': messageType === 'error',
                    'border-green-400': messageType === 'success'
                }">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg v-if="messageType === 'error'" class="h-6 w-6 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <svg v-else class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1">
                            <p class="text-sm font-medium" :class="{ 'text-pink-900': messageType === 'error', 'text-green-900': messageType === 'success' }">
                                {{ message }}
                            </p>
                            <div class="mt-2">
                                <div v-if="Object.keys(form.errors).length > 0" class="text-sm text-pink-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li v-for="(error, field) in form.errors" :key="field">
                                            {{ error }}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="message = ''" class="inline-flex text-gray-400 hover:text-gray-500">
                                <span class="sr-only">Schließen</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message for Missing Rotekarte -->
            <div v-if="!rotekarte" class="bg-white rounded-lg shadow-sm p-6">
                <div class="bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Keine Rotekarte ausgewählt</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>Bitte wählen Sie zuerst eine Rotekarte aus dem Dashboard aus.</p>
                            </div>
                            <div class="mt-4">
                                <Link
                                    :href="route('dashboard')"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                    Zurück zum Dashboard
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <form v-else @submit.prevent="submit" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- QS Prüfer Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">QS Prüfer</h2>
                                <div v-if="(prueferForm.name || prueferForm.datum) && !form.qs_daten.pruefer.length" class="mt-1.5 flex items-center space-x-1">
                                    <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p class="text-sm text-pink-500">Klick auf "Prüfer hinzufügen" um den Prüfer zu speichern ➕</p>
                                </div>
                            </div>
                            <button type="button"
                                @click="addPruefer"
                                :disabled="!prueferForm.name || !prueferForm.datum"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Prüfer hinzufügen
                            </button>
                        </div>

                        <p v-if="form.errors['qs_daten.pruefer']" class="mt-1 text-sm text-red-600">
                            {{ form.errors['qs_daten.pruefer'] }}
                        </p>

                        <!-- New Prüfer Form -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input type="text" v-model="prueferForm.name"
                                        placeholder="Name eingeben"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !prueferForm.name && !form.qs_daten.pruefer.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!prueferForm.name && !form.qs_daten.pruefer.length" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-500">Hey, bitte gib deinen Namen ein 😊</p>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Datum</label>
                                    <input type="date" v-model="prueferForm.datum"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !prueferForm.datum && !form.qs_daten.pruefer.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!prueferForm.datum && !form.qs_daten.pruefer.length" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-500">Wähle bitte ein Datum aus 📅</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Prüfer List -->
                        <div class="divide-y divide-gray-200">
                            <div v-for="(pruefer, index) in form.qs_daten.pruefer" :key="index"
                                class="py-3 flex items-center">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                            <span class="text-gray-600 font-medium">{{ pruefer.name.charAt(0) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ pruefer.name }}</p>
                                        <p class="text-sm text-gray-500">{{ formatDate(pruefer.datum) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bemerkungen Section -->
                        <div class="mt-6 border-t border-gray-200 pt-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Bemerkungen</h3>
                                <button 
                                    v-if="!showNewRemarkForm"
                                    @click="showNewRemarkForm = true"
                                    type="button"
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Neue Bemerkung
                                </button>
                            </div>

                            <!-- Existing Remarks -->
                            <div class="space-y-4 mb-4">
                                <div v-for="(remark, index) in form.qs_daten.bemerkungen" :key="index"
                                    class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-grow">
                                            <p class="text-sm text-gray-900">{{ remark.text }}</p>
                                            <div class="mt-1 text-xs text-gray-500">
                                                <span class="font-medium">{{ remark.pruefer }}</span>
                                                <span class="mx-1">•</span>
                                                <span>{{ remark.timestamp }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- New Remark Form -->
                            <div v-if="showNewRemarkForm" class="mt-4">
                                <textarea
                                    v-model="newRemark"
                                    rows="3"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="Neue Bemerkung eingeben..."
                                ></textarea>
                                <div class="mt-3 flex justify-end space-x-3">
                                    <button
                                        @click="showNewRemarkForm = false"
                                        type="button"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Abbrechen
                                    </button>
                                    <button
                                        @click="addNewRemark"
                                        type="button"
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Bemerkung hinzufügen
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Teile Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">Teile</h2>
                                <div v-if="(teilForm.teilenummer || teilForm.anzahl || teilForm.gepruefteTeile) && !form.qs_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                    <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p class="text-sm text-pink-500">Klick auf "Teil hinzufügen" um das Teil zu speichern ➕</p>
                                </div>
                            </div>
                            <button type="button"
                                @click="addTeil"
                                :disabled="!teilForm.teilenummer || !teilForm.anzahl || !teilForm.gepruefteTeile"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Teil hinzufügen
                            </button>
                        </div>

                        <p v-if="form.errors['qs_daten.teile']" class="mt-1 text-sm text-red-600">
                            {{ form.errors['qs_daten.teile'] }}
                        </p>

                        <!-- New Teil Form -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="grid grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Teilenummer</label>
                                    <input type="text"
                                        id="teilenummer"
                                        v-model="teilForm.teilenummer"
                                        @keydown="handleKeyDown($event, 'anzahl')"
                                        placeholder="Nummer eingeben"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !teilForm.teilenummer && !form.qs_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!teilForm.teilenummer && !form.qs_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-500">Trag bitte eine Teilenummer ein 🔢</p>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Anzahl</label>
                                    <input type="text"
                                        id="anzahl"
                                        v-model="teilForm.anzahl"
                                        @keydown="handleKeyDown($event, 'gepruefteTeile')"
                                        placeholder="Anzahl eingeben"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !teilForm.anzahl && !form.qs_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!teilForm.anzahl && !form.qs_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-500">Trag bitte die Anzahl ein 📊</p>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Geprüfte Teile</label>
                                    <input type="text"
                                        id="gepruefteTeile"
                                        v-model="teilForm.gepruefteTeile"
                                        @keydown="handleKeyDown($event, 'addTeilButton')"
                                        placeholder="Geprüfte Teile eingeben"
                                        :class="[
                                            'block w-full rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500',
                                            !teilForm.gepruefteTeile && !form.qs_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                        ]">
                                    <div v-if="!teilForm.gepruefteTeile && !form.qs_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                        <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm text-pink-500">Trag bitte die Anzahl der geprüften Teile ein ✓</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Teile List -->
                        <div class="overflow-hidden rounded-lg border border-gray-200">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Teilenummer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Anzahl</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Geprüft</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Aktionen</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="(teil, index) in form.qs_daten.teile" :key="index">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ teil.teilenummer }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ teil.anzahl }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="number" v-model="teil.gepruefteTeile"
                                                placeholder="0"
                                                class="block w-24 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                                min="0"
                                                :max="teil.anzahl">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex space-x-2">
                                                <button type="button" @click="teil.io_nio = 'IO'"
                                                    class="inline-flex items-center px-2.5 py-1.5 border rounded-md text-xs font-medium"
                                                    :class="teil.io_nio === 'IO' ? 'bg-green-100 text-green-800 border-green-500' : 'border-gray-300 text-gray-700 bg-white hover:bg-green-50'">
                                                    IO
                                                </button>
                                                <button type="button" @click="teil.io_nio = 'NIO'"
                                                    class="inline-flex items-center px-2.5 py-1.5 border rounded-md text-xs font-medium"
                                                    :class="teil.io_nio === 'NIO' ? 'bg-red-100 text-red-800 border-red-500' : 'border-gray-300 text-gray-700 bg-white hover:bg-red-50'">
                                                    NIO
                                                </button>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button @click.prevent="deleteTeil(index)"
                                                class="text-gray-400 hover:text-red-600">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Härtewerte Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">Härtewerte</h2>
                                <p class="text-sm text-gray-500">Erfassen Sie hier die Härtewerte</p>
                            </div>
                            
                            <button type="button"
                                @click="saveHaertewerte"
                                :disabled="!form.qs_daten.haertewerte.length"
                                class="ml-2 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                </svg>
                                Härtewerte speichern
                            </button>
                            <button type="button"
                                @click="addHaertewert"
                                :disabled="!haerteForm.eisenmarke || !haerteForm.soll_von || !haerteForm.soll_bis || !haerteForm.ist_wert"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Härtewert hinzufügen
                            </button>
                        </div>

                        <!-- New Härtewert Form -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="grid grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Eisenmarke</label>
                                    <input type="text"
                                        v-model="haerteForm.eisenmarke"
                                        placeholder="Eisenmarke eingeben"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nestnummer</label>
                                    <input type="text"
                                        v-model="haerteForm.nestnummer"
                                        placeholder="Nest Nr."
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Soll-Wert von</label>
                                    <input type="number"
                                        v-model="haerteForm.soll_von"
                                        placeholder="Von"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Soll-Wert bis</label>
                                    <input type="number"
                                        v-model="haerteForm.soll_bis"
                                        placeholder="Bis"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Ist-Wert</label>
                                    <input type="number"
                                        v-model="haerteForm.ist_wert"
                                        placeholder="Ist-Wert eingeben"
                                        @keydown.enter.prevent="addHaertewert"
                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                </div>
                            </div>
                        </div>

                        <!-- Durchschnittswert Anzeige -->
                        <div v-if="form.qs_daten.haertewerte && form.qs_daten.haertewerte.length > 0" 
                            class="bg-blue-50 rounded-lg p-4 mb-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <div>
                                    <span class="text-sm font-medium text-blue-700">Durchschnittlicher Härtewert:</span>
                                    <span class="ml-2 text-lg font-bold text-blue-800">{{ calculateAverageHaertewert }}</span>
                                </div>
                            </div>
                            <button
                                @click.prevent="downloadHaertewertePDF"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
                            >
                                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                PDF Exportieren
                            </button>
                        </div>

                        <!-- Härtewerte List -->
                        <div class="overflow-hidden rounded-lg border border-gray-200">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Eisenmarke</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nestnummer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Soll-Wert</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ist-Wert</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Aktionen</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="(wert, index) in form.qs_daten.haertewerte" :key="index">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ wert.eisenmarke }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ wert.nestnummer }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ wert.soll_von }} - {{ wert.soll_bis }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm" 
                                            :class="{
                                                'text-red-600 font-medium': isOutOfRange(wert),
                                                'text-green-600 font-medium': !isOutOfRange(wert)
                                            }">
                                            {{ wert.ist_wert }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button @click.prevent="deleteHaertewert(index)"
                                                class="text-gray-400 hover:text-red-600">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Vorschlag Prüfungen Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Vorschlag Prüfungen</h2>
                        <textarea
                            v-model="form.qs_daten.vorschlag_pruefungen"
                            rows="1"
                            readonly
                            class="mt-1 block w-full rounded-md bg-blue-50 border-blue-200 text-blue-800 font-medium shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 cursor-not-allowed resize-none px-3 py-2"
                            placeholder="Wird automatisch aus dem Fehlercode übernommen"
                        ></textarea>
                    </div>

                    <!-- Status Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Status</h2>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">QS Status</label>
                                <select v-model="form.qs_daten.status"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="Offen">Offen</option>
                                    <option value="In Bearbeitung">In Bearbeitung</option>
                                    <option value="Abgeschlossen">Abgeschlossen</option>
                                </select>
                                <p v-if="form.errors['qs_daten.status']" class="mt-1 text-sm text-red-600">
                                    {{ form.errors['qs_daten.status'] }}
                                </p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Abnahmebeauftragter</label>
                                <select v-model="form.qs_daten.serie_status"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="Entscheidung ausstehend">Entscheidung ausstehend</option>
                                    <option value="Teile in die Serie einreihen">Teile in die Serie einreihen</option>
                                    <option value="Zur Verschrottung freigegeben">Zur Verschrottung freigegeben</option>
                                </select>
                            </div>

                            <!-- System Information -->
                            <div class="pt-4 border-t border-gray-200">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">System Information</h3>
                                <div class="grid grid-cols-1 gap-4 mb-4">
                                    <div>
                                        <span class="text-xs text-gray-500">Benutzer</span>
                                        <p class="text-sm font-medium text-gray-900">{{ form.qs_daten.system_info.current.username }}</p>
                                    </div>
                                </div>

                                <!-- System Info History -->
                                <div v-if="form.qs_daten.system_info.history.length > 0">
                                    <h4 class="text-xs font-medium text-gray-500 mb-2">Historie</h4>
                                    <div class="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
                                        <div v-for="(entry, index) in form.qs_daten.system_info.history"
                                             :key="index"
                                             class="text-xs mb-2 last:mb-0 pb-2 last:pb-0 border-b last:border-b-0 border-gray-200">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <span class="font-medium">{{ entry.username }}</span>
                                                    <span class="text-gray-500"> auf </span>
                                                    <span class="font-medium">{{ entry.hostname }}</span>
                                                </div>
                                                <span class="text-gray-400">{{ entry.timestamp }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>                    
                    <!-- Action Buttons -->
                    <div class="bg-white rounded-lg shadow-sm p-6 space-y-3">
                        <button type="submit"
                            :disabled="form.processing"
                            class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                            {{ form.processing ? 'Wird gespeichert...' : 'Speichern' }}
                        </button>
                        <Link :href="route('dashboard')"
                            class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Abbrechen
                        </Link>
                    </div>
                </div>
            </form>

            <!-- Verschrottungskosten Berechnung -->
            <div v-if="form.qs_daten.teile && form.qs_daten.teile.length > 0" class="bg-white rounded-lg shadow-sm p-6 mb-6 mt-5">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Verschrottungsanalyse</h3>
                    <button
                        @click="calculateScrapCosts"
                        :disabled="calculatingScrap"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                        <svg v-if="calculatingScrap" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>{{ calculatingScrap ? 'Berechne...' : 'Verschrottungskosten berechnen' }}</span>
                    </button>
                </div>

                <div v-if="scrapCalculation?.success" class="space-y-6">
                    <!-- Zusammenfassung -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-base font-medium text-gray-900 mb-3">Gesamtübersicht</h4>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-white p-3 rounded-md shadow-sm">
                                <div class="text-sm text-gray-500">Gesamtwert der Teile</div>
                                <div class="text-lg font-semibold text-gray-900">
                                    {{ formatCurrency(scrapCalculation.summary.totalCost) }}
                                </div>
                            </div>
                            <div class="bg-white p-3 rounded-md shadow-sm">
                                <div class="text-sm text-gray-500">Gesamtgewicht</div>
                                <div class="text-lg font-semibold text-gray-900">
                                    {{ formatWeight(scrapCalculation.summary.totalWeight) }}
                                </div>
                            </div>
                            <div class="bg-white p-3 rounded-md shadow-sm">
                                <div class="text-sm text-gray-500">Schrottwert</div>
                                <div class="text-lg font-semibold text-green-600">
                                    {{ formatCurrency(scrapCalculation.summary.totalScrapValue) }}
                                </div>
                            </div>
                            <div class="bg-white p-3 rounded-md shadow-sm">
                                <div class="text-sm text-gray-500">Gesamtverlust</div>
                                <div class="text-lg font-semibold text-red-600">
                                    {{ formatCurrency(scrapCalculation.summary.totalLoss) }}
                                </div>
                            </div>
                        </div>

                        <!-- Prüfkostenanalyse -->
                        <div class="mt-4 p-4 rounded-lg" :class="[
                            scrapCalculation.summary.pruefungLohntSich 
                                ? 'bg-green-50 border border-green-200' 
                                : 'bg-yellow-50 border border-yellow-200'
                        ]">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <svg v-if="scrapCalculation.summary.pruefungLohntSich" class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    <svg v-else class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium" :class="[
                                        scrapCalculation.summary.pruefungLohntSich 
                                            ? 'text-green-800' 
                                            : 'text-yellow-800'
                                    ]">
                                        <span v-if="scrapCalculation.summary.pruefungLohntSich">
                                            Physikalische Prüfung wird empfohlen (Verlust > {{ formatCurrency(scrapCalculation.summary.physikalischePruefungKosten) }})
                                        </span>
                                        <span v-else>
                                            Physikalische Prüfung nicht empfohlen (Verlust < {{ formatCurrency(scrapCalculation.summary.physikalischePruefungKosten) }})
                                        </span>
                                    </h3>
                                    <div class="mt-2 text-sm" :class="[
                                        scrapCalculation.summary.pruefungLohntSich 
                                            ? 'text-green-700' 
                                            : 'text-yellow-700'
                                    ]">
                                        <p>Kosten für physikalische Prüfung: {{ formatCurrency(scrapCalculation.summary.physikalischePruefungKosten) }}</p>
                                        <p class="mt-1">
                                            <span v-if="scrapCalculation.summary.pruefungLohntSich">
                                                Der potenzielle Verlust ({{ formatCurrency(scrapCalculation.summary.totalLoss) }}) liegt über dem Limit von {{ formatCurrency(scrapCalculation.summary.physikalischePruefungKosten) }}. Eine physikalische Prüfung wird empfohlen.
                                            </span>
                                            <span v-else>
                                                Der potenzielle Verlust ({{ formatCurrency(scrapCalculation.summary.totalLoss) }}) liegt unter dem Limit von {{ formatCurrency(scrapCalculation.summary.physikalischePruefungKosten) }}. Eine physikalische Prüfung ist nicht wirtschaftlich.
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detaillierte Auflistung -->
                    <div v-if="scrapCalculation.details && scrapCalculation.details.length > 0" class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 text-sm">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Teilenummer</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Menge</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">€/Stk</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">kg/Stk</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">€/kg</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Gesamt €</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Gesamt kg</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Schrottwert</th>
                                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Verlust</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="(teil, index) in scrapCalculation.details" :key="index">
                                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ teil.teilenummer }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ teil.menge }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(teil.einzelPreis) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatWeight(teil.einzelGewicht) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(teil.schrottPreis) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(teil.gesamtPreis) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatWeight(teil.gesamtGewicht) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-green-600">{{ formatCurrency(teil.schrottWert) }}</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-red-600">{{ formatCurrency(teil.verlust) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Document Upload Section -->
            <QSDocumentUpload
                v-if="form.rotekarte_id"
                :rotekarte_id="form.rotekarte_id"
            />
        </div>
    </AppLayout>
</template>
