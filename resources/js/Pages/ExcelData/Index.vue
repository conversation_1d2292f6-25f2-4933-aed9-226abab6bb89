<template>
    <AppLayout title="Excel Daten">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Excel Daten
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Upload Form -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-6">
                    <form @submit.prevent="submit" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Excel-Datei auswählen</label>
                            <input
                                type="file"
                                @input="form.excel_file = $event.target.files[0]"
                                accept=".xlsx,.xls,.csv"
                                class="mt-1 block w-full text-sm text-gray-500
                                    file:mr-4 file:py-2 file:px-4
                                    file:rounded-full file:border-0
                                    file:text-sm file:font-semibold
                                    file:bg-blue-50 file:text-blue-700
                                    hover:file:bg-blue-100"
                            />
                            <div v-if="form.errors.excel_file" class="text-red-500 text-sm mt-1">
                                {{ form.errors.excel_file }}
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-500 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring focus:ring-blue-300 disabled:opacity-25 transition"
                            >
                                <span v-if="form.processing">Wird hochgeladen...</span>
                                <span v-else>Excel-Datei hochladen</span>
                            </button>

                            <button
                                v-if="excelData?.data?.length"
                                type="button"
                                @click="deleteData"
                                :disabled="deleteForm.processing"
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-900 focus:outline-none focus:border-red-900 focus:ring focus:ring-red-300 disabled:opacity-25 transition"
                            >
                                <span v-if="deleteForm.processing">Wird gelöscht...</span>
                                <span v-else>Daten löschen</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Last Sync -->
                <div v-if="lastSync" class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-6">
                    <p class="text-sm text-gray-600">
                        Letzte Synchronisation: {{ lastSync }}
                    </p>
                </div>

                <!-- Data Table -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div v-if="!excelData?.data?.length" class="p-6 text-center text-gray-500">
                        Keine Daten vorhanden. Bitte laden Sie eine Excel-Datei hoch.
                    </div>
                    <div v-else class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        v-for="header in Object.keys(excelData.data[0])"
                                        :key="header"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                    >
                                        {{ header }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="(row, index) in excelData.data" :key="index">
                                    <td
                                        v-for="(value, key) in row"
                                        :key="key"
                                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                                    >
                                        {{ value }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';

const props = defineProps({
    excelData: Object,
    lastSync: String
});

const form = useForm({
    excel_file: null
});

const deleteForm = useForm({});

const submit = () => {
    form.post(route('excel.import'), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset();
        }
    });
};

const deleteData = () => {
    if (confirm('Sind Sie sicher, dass Sie alle Excel-Daten löschen möchten?')) {
        deleteForm.delete(route('excel.destroy'), {
            preserveScroll: true
        });
    }
};
</script>
