<script setup lang="ts">
import { Head, Link } from '@inertiajs/vue3';
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { usePage } from '@inertiajs/vue3';

interface User {
  id: number;
  name: string;
  email: string;
}

interface PageProps {
  auth: {
    user: User | null;
  };
  [key: string]: any; // Add index signature for Inertia compatibility
}

const page = usePage<PageProps>();
const user = computed(() => page.props.auth.user);

// Type-safe route helper
declare function route(name: string): string;

// Dynamic clock
const currentTime = ref(new Date());
const operationHours = ref(0);
const operationMinutes = ref(0);
const operationSeconds = ref(0);

// Simulated data for charts and displays
const metalComposition = ref({
  iron: 65 + Math.random() * 5,
  carbon: 3 + Math.random() * 0.5,
  silicon: 2 + Math.random() * 0.3,
  manganese: 0.5 + Math.random() * 0.2,
  other: 0.2 + Math.random() * 0.1
});

const co2Reduction = ref(27 + Math.random() * 5);
const defectRate = ref(0.5 + Math.random() * 0.3);
const energyEfficiency = ref(82 + Math.random() * 8);

// Update clock and simulated data
let clockInterval: number | null = null;
let dataInterval: number | null = null;

onMounted(() => {
  // Start clock
  clockInterval = window.setInterval(() => {
    currentTime.value = new Date();
    
    // Simulate operation time (increases faster than real time)
    operationSeconds.value += 1;
    if (operationSeconds.value >= 60) {
      operationSeconds.value = 0;
      operationMinutes.value += 1;
      
      if (operationMinutes.value >= 60) {
        operationMinutes.value = 0;
        operationHours.value += 1;
      }
    }
  }, 1000);
  
  // Update simulated data
  dataInterval = window.setInterval(() => {
    // Update metal composition
    metalComposition.value = {
      iron: 65 + Math.random() * 5,
      carbon: 3 + Math.random() * 0.5,
      silicon: 2 + Math.random() * 0.3,
      manganese: 0.5 + Math.random() * 0.2,
      other: 0.2 + Math.random() * 0.1
    };
    
    // Update other metrics with small variations
    co2Reduction.value = Math.min(100, Math.max(0, co2Reduction.value + (Math.random() - 0.5) * 2));
    defectRate.value = Math.min(5, Math.max(0, defectRate.value + (Math.random() - 0.5) * 0.1));
    energyEfficiency.value = Math.min(100, Math.max(0, energyEfficiency.value + (Math.random() - 0.5) * 2));
  }, 3000);
  
  // Initialize particles
  initParticles();
});

onUnmounted(() => {
  if (clockInterval) window.clearInterval(clockInterval);
  if (dataInterval) window.clearInterval(dataInterval);
});

// Format numbers for display
const formatNumber = (num: number, decimals = 1) => {
  return num.toFixed(decimals);
};

// Particles background
const initParticles = () => {
  if (typeof document !== 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js';
    script.onload = () => {
      // @ts-ignore
      if (window.particlesJS) {
        // @ts-ignore
        window.particlesJS('particles-js', {
          particles: {
            number: { value: 80, density: { enable: true, value_area: 800 } },
            color: { value: '#3273dc' },
            shape: { type: 'circle' },
            opacity: { value: 0.5, random: false },
            size: { value: 3, random: true },
            line_linked: { enable: true, distance: 150, color: '#3273dc', opacity: 0.4, width: 1 },
            move: { enable: true, speed: 2, direction: 'none', random: false, straight: false, out_mode: 'out', bounce: false }
          },
          interactivity: {
            detect_on: 'canvas',
            events: { onhover: { enable: true, mode: 'grab' }, onclick: { enable: true, mode: 'push' }, resize: true },
            modes: { grab: { distance: 140, line_linked: { opacity: 1 } }, push: { particles_nb: 4 } }
          },
          retina_detect: true
        });
      }
    };
    document.head.appendChild(script);
  }
};

// Toggle between AI and manual inspection
const aiInspection = ref(true);
const toggleInspection = () => {
  aiInspection.value = !aiInspection.value;
};

// Language selection
const languages = ['EN', 'DE'];
const currentLanguage = ref('EN');
const changeLanguage = (lang: string) => {
  currentLanguage.value = lang;
};
</script>

<template>
  <Head title="Metallurgie der Zukunft">
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;600&display=swap" />
    </Head>

  <div class="min-h-screen bg-gray-900 text-white overflow-hidden">
    <!-- Particles Background -->
    <div id="particles-js" class="absolute inset-0 z-0"></div>
    
    <!-- Navigation -->
    <nav class="container mx-auto px-6 py-4 relative z-10">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <!-- Futuristic Logo -->
          <div class="relative h-10 w-10">
            <div class="absolute inset-0 bg-blue-500 rounded-full opacity-50 animate-pulse"></div>
            <svg class="h-10 w-10 relative z-10" viewBox="0 0 50 52" fill="none">
              <path d="M49.7 23.1L45.6 12.5C45.3 11.5 44.3 10.9 43.3 11.1L32.7 15.2C31.7 15.5 31.1 16.5 31.3 17.5L35.4 28.1C35.7 29.1 36.7 29.7 37.7 29.5L48.3 25.4C49.3 25.1 49.9 24.1 49.7 23.1Z" fill="#3273dc"/>
            </svg>
          </div>
          <span class="ml-2 text-xl font-semibold text-white font-mono tracking-wider">METALL<span class="text-blue-400">FUTURE</span></span>
        </div>

        <div class="flex items-center space-x-6">
          <!-- Language Selector -->
          <div class="flex space-x-2">
            <button 
              v-for="lang in languages" 
              :key="lang"
              @click="changeLanguage(lang)"
              :class="[
                'text-xs font-mono px-2 py-1 rounded transition-all duration-300',
                currentLanguage === lang 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
              ]"
            >
              {{ lang }}
            </button>
          </div>
          
          <!-- Auth Links -->
          <template v-if="user">
                <Link
                    :href="route('dashboard')"
              class="relative overflow-hidden group px-6 py-2 font-mono text-sm font-medium text-white rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-300"
                >
              <span class="relative z-10">DASHBOARD</span>
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </Link>
          </template>
                <template v-else>
                    <Link
                        :href="route('login')"
              class="text-sm font-mono text-gray-300 hover:text-white transition-colors duration-300"
                    >
              LOGIN
                    </Link>
                    <Link
                        :href="route('register')"
              class="relative overflow-hidden group px-6 py-2 font-mono text-sm font-medium text-white rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-300"
                    >
              <span class="relative z-10">REGISTER</span>
              <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                    </Link>
                </template>
        </div>
      </div>
            </nav>

    <!-- Hero Section -->
    <section class="container mx-auto px-6 py-16 relative z-10">
      <div class="flex flex-col items-center justify-center text-center">
        <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight">
          <span class="block mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Revolutionierung der Metallurgie</span>
          <span class="block text-white">& Qualitätsmanagement für die Gießerei-Industrie</span>
        </h1>
        
        <p class="mt-6 max-w-2xl text-xl text-gray-300 leading-relaxed">
          Die Plattform der nächsten Generation für Präzision, Effizienz und datengestützte Entscheidungsfindung.
        </p>

        <!-- Operation Time Display -->
        <div class="mt-8 bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-4 border border-gray-700 shadow-lg">
          <div class="text-sm font-mono text-gray-400 mb-1">BETRIEBSZEIT</div>
          <div class="text-2xl font-mono text-blue-400">
            {{ operationHours.toString().padStart(2, '0') }}:{{ operationMinutes.toString().padStart(2, '0') }}:{{ operationSeconds.toString().padStart(2, '0') }}
          </div>
        </div>

        <!-- CTA Button -->
        <button class="mt-10 group relative overflow-hidden rounded-lg bg-blue-600 px-8 py-4 font-mono text-lg font-medium text-white shadow-md transition-all duration-300 hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900">
          <span class="relative z-10">INNOVATIONEN ENTDECKEN</span>
          <div class="absolute inset-0 flex">
            <div class="h-full w-1/3 bg-gradient-to-r from-transparent via-blue-400 to-transparent skew-x-15 opacity-20 animate-shimmer"></div>
          </div>
        </button>
      </div>
    </section>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-16 relative z-10">
      <!-- Section 1: Industry 4.0 & AI Integration -->
      <section class="mb-24">
        <div class="flex flex-col lg:flex-row items-center gap-12">
          <div class="lg:w-1/2">
            <h2 class="text-3xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">
              Industrie 4.0 & KI-Integration
            </h2>
            <p class="text-gray-300 mb-6 leading-relaxed">
              Unsere fortschrittliche KI-Technologie revolutioniert die Qualitätskontrolle in Gießereien. Durch Echtzeit-Datenanalyse und prädiktive Wartung werden Produktionsprozesse optimiert und Ausfallzeiten minimiert.
            </p>
            
            <!-- Live Data Display -->
            <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-6 border border-gray-700 shadow-lg">
              <h3 class="text-lg font-mono text-blue-400 mb-4">LIVE-METALLZUSAMMENSETZUNG</h3>
              
              <div class="space-y-3">
                <div v-for="(value, key) in metalComposition" :key="key" class="flex items-center">
                  <div class="w-24 font-mono text-gray-400 text-sm">{{ key.toUpperCase() }}</div>
                  <div class="flex-1 bg-gray-700 rounded-full h-2">
                    <div 
                      class="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500 ease-out"
                      :style="{ width: `${value}%` }"
                    ></div>
                  </div>
                  <div class="w-16 text-right font-mono text-sm ml-3">{{ formatNumber(value) }}%</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 3D Visualization Placeholder -->
          <div class="lg:w-1/2 h-80 bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg border border-gray-700 shadow-lg overflow-hidden relative">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-40 h-40 relative">
                <!-- Animated Cube representing 3D model -->
                <div class="cube-wrapper">
                  <div class="cube">
                    <div class="cube-face front"></div>
                    <div class="cube-face back"></div>
                    <div class="cube-face right"></div>
                    <div class="cube-face left"></div>
                    <div class="cube-face top"></div>
                    <div class="cube-face bottom"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="absolute bottom-4 left-4 right-4 bg-gray-900 bg-opacity-70 backdrop-filter backdrop-blur-sm rounded p-3">
              <div class="text-xs font-mono text-gray-400">ECHTZEIT-VISUALISIERUNG</div>
              <div class="text-sm text-blue-400">Metallstruktur-Analyse</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Section 2: Sustainability & Smart Manufacturing -->
      <section class="mb-24">
        <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
          <div class="lg:w-1/2">
            <h2 class="text-3xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">
              Nachhaltigkeit & Smart Manufacturing
            </h2>
            <p class="text-gray-300 mb-6 leading-relaxed">
              Unsere innovativen Lösungen fördern umweltfreundliche Prozesse durch recycelbare Legierungen und energieeffiziente Gießverfahren. Wir setzen neue Maßstäbe für nachhaltige Produktion in der Metallurgie.
            </p>
            
            <!-- Sustainability Metrics -->
            <div class="grid grid-cols-2 gap-4">
              <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-4 border border-gray-700 shadow-lg">
                <div class="text-xs font-mono text-gray-400 mb-1">CO₂-REDUKTION</div>
                <div class="text-2xl font-mono text-green-400">{{ formatNumber(co2Reduction) }}%</div>
              </div>
              
              <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-4 border border-gray-700 shadow-lg">
                <div class="text-xs font-mono text-gray-400 mb-1">ENERGIEEFFIZIENZ</div>
                <div class="text-2xl font-mono text-blue-400">{{ formatNumber(energyEfficiency) }}%</div>
              </div>
            </div>
          </div>
          
          <!-- Sustainable Foundry Visualization -->
          <div class="lg:w-1/2 h-80 bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg border border-gray-700 shadow-lg overflow-hidden relative">
            <div class="absolute inset-0 flex items-center justify-center">
              <!-- Animated Eco-Foundry Representation -->
              <div class="eco-foundry">
                <div class="factory-base"></div>
                <div class="chimney">
                  <div class="clean-smoke"></div>
                </div>
                <div class="solar-panels"></div>
                <div class="recycling-symbol"></div>
              </div>
            </div>
            <div class="absolute bottom-4 left-4 right-4 bg-gray-900 bg-opacity-70 backdrop-filter backdrop-blur-sm rounded p-3">
              <div class="text-xs font-mono text-gray-400">NACHHALTIGE GIESSEREIMODELL</div>
              <div class="text-sm text-green-400">Kreislaufwirtschaft in Aktion</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Section 3: Quality Control & Automation -->
      <section>
        <div class="flex flex-col lg:flex-row items-center gap-12">
          <div class="lg:w-1/2">
            <h2 class="text-3xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">
              Qualitätskontrolle & Automatisierung
            </h2>
            <p class="text-gray-300 mb-6 leading-relaxed">
              Unser fortschrittliches automatisiertes Defekterkennungssystem kombiniert mit KI-gestützter metallurgischer Laborintegration ermöglicht sofortige Analysen und höchste Qualitätsstandards.
            </p>
            
            <!-- AI Toggle -->
            <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-6 border border-gray-700 shadow-lg mb-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-mono text-blue-400">INSPEKTIONSMODUS</h3>
                <button 
                  @click="toggleInspection" 
                  class="relative inline-flex h-6 w-12 items-center rounded-full transition-colors duration-300"
                  :class="aiInspection ? 'bg-blue-600' : 'bg-gray-600'"
                >
                                <span
                    class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300"
                    :class="aiInspection ? 'translate-x-7' : 'translate-x-1'"
                  ></span>
                </button>
              </div>
              <div class="text-center font-mono text-sm">
                {{ aiInspection ? 'KI-GESTÜTZTE INSPEKTION' : 'MANUELLE INSPEKTION' }}
              </div>
            </div>
            
            <!-- Defect Rate -->
            <div class="bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg p-4 border border-gray-700 shadow-lg">
              <div class="text-xs font-mono text-gray-400 mb-1">DEFEKTRATE</div>
              <div class="flex items-center">
                <div class="text-2xl font-mono" :class="defectRate < 1 ? 'text-green-400' : 'text-red-400'">
                  {{ formatNumber(defectRate) }}%
                </div>
                <div class="ml-3 text-xs font-mono text-gray-400">
                  {{ aiInspection ? '(KI-OPTIMIERT)' : '(STANDARD)' }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- Quality Control Visualization -->
          <div class="lg:w-1/2 h-80 bg-gray-800 bg-opacity-50 backdrop-filter backdrop-blur-lg rounded-lg border border-gray-700 shadow-lg overflow-hidden relative">
            <div class="absolute inset-0 flex items-center justify-center">
              <!-- Animated Quality Control System -->
              <div class="quality-control-system">
                <div class="scanner-beam"></div>
                <div class="part-conveyor"></div>
                <div class="detection-grid"></div>
              </div>
            </div>
            <div class="absolute bottom-4 left-4 right-4 bg-gray-900 bg-opacity-70 backdrop-filter backdrop-blur-sm rounded p-3">
              <div class="text-xs font-mono text-gray-400">AUTOMATISIERTE QUALITÄTSKONTROLLE</div>
              <div class="text-sm text-blue-400">Präzision auf molekularer Ebene</div>
            </div>
          </div>
        </div>
      </section>
            </main>

    <!-- Footer -->
    <footer class="mt-24 border-t border-gray-800 py-12 relative z-10">
      <div class="container mx-auto px-6">
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div class="flex items-center">
            <svg class="h-6 w-auto text-blue-500" viewBox="0 0 50 52" fill="none">
              <path d="M49.7 23.1L45.6 12.5C45.3 11.5 44.3 10.9 43.3 11.1L32.7 15.2C31.7 15.5 31.1 16.5 31.3 17.5L35.4 28.1C35.7 29.1 36.7 29.7 37.7 29.5L48.3 25.4C49.3 25.1 49.9 24.1 49.7 23.1Z" fill="currentColor"/>
            </svg>
            <span class="ml-2 text-sm font-mono text-gray-400">© 2024 A. Calhan. Alle Rechte vorbehalten.</span>
          </div>
        </div>
      </div>
    </footer>
    </div>
</template>

<style scoped>
/* Cube Animation */
.cube-wrapper {
  perspective: 800px;
  perspective-origin: 50% 50%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cube {
  width: 100px;
  height: 100px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 20s infinite linear;
}

.cube-face {
  position: absolute;
  width: 100px;
  height: 100px;
  background: rgba(50, 115, 220, 0.2);
  border: 1px solid rgba(50, 115, 220, 0.5);
  box-shadow: 0 0 20px rgba(50, 115, 220, 0.3);
}

.front { transform: translateZ(50px); }
.back { transform: rotateY(180deg) translateZ(50px); }
.right { transform: rotateY(90deg) translateZ(50px); }
.left { transform: rotateY(-90deg) translateZ(50px); }
.top { transform: rotateX(90deg) translateZ(50px); }
.bottom { transform: rotateX(-90deg) translateZ(50px); }

@keyframes rotate {
  0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
  100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
}

/* Eco-Foundry Animation */
.eco-foundry {
  position: relative;
  width: 200px;
  height: 150px;
}

.factory-base {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  background: linear-gradient(to bottom, #2a4365, #1a365d);
  border-radius: 5px;
}

.chimney {
  position: absolute;
  bottom: 60px;
  left: 30px;
  width: 20px;
  height: 50px;
  background: #2d3748;
}

.clean-smoke {
  position: absolute;
  bottom: 50px;
  left: -5px;
  width: 30px;
  height: 10px;
  background: rgba(226, 232, 240, 0.1);
  border-radius: 50%;
  animation: smoke 3s infinite ease-out;
}

.solar-panels {
  position: absolute;
  top: 10px;
  right: 20px;
  width: 80px;
  height: 40px;
  background: linear-gradient(135deg, #3182ce 0%, #2b6cb0 100%);
  transform: perspective(200px) rotateX(30deg);
}

.recycling-symbol {
  position: absolute;
  bottom: 20px;
  right: 30px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(56, 178, 172, 0.3);
  border: 2px solid rgba(56, 178, 172, 0.7);
  animation: pulse 2s infinite;
}

@keyframes smoke {
  0% { transform: translateY(0) scale(1); opacity: 0.3; }
  100% { transform: translateY(-20px) scale(2); opacity: 0; }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

/* Quality Control System Animation */
.quality-control-system {
  position: relative;
  width: 200px;
  height: 150px;
}

.scanner-beam {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100px;
  background: rgba(66, 153, 225, 0.7);
  animation: scan 2s infinite alternate;
}

.part-conveyor {
  position: absolute;
  bottom: 20px;
  width: 100%;
  height: 10px;
  background: #4a5568;
}

.detection-grid {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 60px;
  border: 1px solid rgba(66, 153, 225, 0.5);
  background: rgba(66, 153, 225, 0.1);
}

@keyframes scan {
  0% { transform: translateX(-50px); }
  100% { transform: translateX(50px); }
}

/* Shimmer Animation for CTA Button */
@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(100%) skewX(-15deg); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
</style>
