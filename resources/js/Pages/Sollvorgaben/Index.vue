<template>
    <AppLayout title="Sollvorgaben">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="bg-indigo-600 p-8">
                    <h2 class="text-2xl font-bold text-white">Sollvorgaben</h2>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <!-- Search and Add Button -->
                    <div class="flex justify-between mb-6">
                        <div class="flex items-center gap-4">
                            <div class="relative w-64">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input
                                    type="text"
                                    v-model="search"
                                    placeholder="Suchen..."
                                    class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>
                            <div class="flex items-center gap-2 border rounded-lg p-1">
                                <button
                                    @click="viewMode = 'box'"
                                    :class="[
                                        'px-3 py-1 rounded-md transition-colors',
                                        viewMode === 'box'
                                            ? 'bg-blue-600 text-white'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    ]"
                                >
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button
                                    @click="viewMode = 'table'"
                                    :class="[
                                        'px-3 py-1 rounded-md transition-colors',
                                        viewMode === 'table'
                                            ? 'bg-blue-600 text-white'
                                            : 'text-gray-600 hover:bg-gray-100'
                                    ]"
                                >
                                    <i class="fas fa-table"></i>
                                </button>
                            </div>
                        </div>
                        <button
                            @click="openCreateModal"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200 flex items-center gap-2"
                        >
                            <i class="fas fa-plus"></i>
                            Neue Sollvorgabe
                        </button>
                    </div>

                    <!-- Box View -->
                    <div v-if="viewMode === 'box'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div
                            v-for="item in filteredSollvorgaben"
                            :key="item.id"
                            class="bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow p-4"
                        >
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        {{ parseJSON(item.data).EM || parseJSON(item.data).EM_HF }}
                                    </h3>
                                    <p class="text-sm text-gray-500">ID: {{ item.id }}</p>
                                    <p v-if="parseJSON(item.data).Eigenschaft" class="inline-block px-2.5 py-1 mt-2 rounded-md bg-indigo-100 text-indigo-800 font-medium text-sm shadow-sm border border-indigo-200">
                                        {{ parseJSON(item.data).Eigenschaft }}
                                    </p>
                                    <p v-if="parseJSON(item.data).EM && parseJSON(item.data).EM_HF" class="text-sm text-blue-600 mt-1">
                                        HF: {{ parseJSON(item.data).EM_HF }}
                                    </p>
                                </div>
                                <div class="flex gap-2">
                                    <button
                                        @click="openViewModal(item)"
                                        class="text-blue-600 hover:text-blue-800 p-1"
                                        title="Anzeigen"
                                    >
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button
                                        @click="duplicateSollvorgabe(item)"
                                        class="text-purple-600 hover:text-purple-800 p-1"
                                        title="Duplizieren"
                                    >
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    <button
                                        @click="openEditModal(item)"
                                        class="text-green-600 hover:text-green-800 p-1"
                                        title="Bearbeiten"
                                    >
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button
                                        @click="confirmDelete(item)"
                                        class="text-red-600 hover:text-red-800 p-1"
                                        title="Löschen"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="grid grid-cols-2 gap-2 text-sm">
                                    <div>
                                        <span class="text-gray-600">Kohlenstoff:</span>
                                        <span class="font-medium">{{ parseJSON(item.data).C_percent }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Nickel:</span>
                                        <span class="font-medium">{{ parseJSON(item.data).Ni_percent }}</span>
                                    </div>
                                </div>
                                <div class="text-sm">
                                    <span class="text-gray-600">Liquidus:</span>
                                    <span class="font-medium">{{ parseJSON(item.data).Liq_percent }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table View -->
                    <div v-else class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 bg-white rounded-lg shadow">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                                        ID
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                                        Material
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                                        Eigenschaft
                                    </th>
                                    <th class="px-6 py-3 text-left text-sm font-semibold text-gray-900">
                                        Material HF
                                    </th>
                                    <th class="px-6 py-3 text-right text-sm font-semibold text-gray-900">
                                        Aktionen
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-for="item in filteredSollvorgaben" :key="item.id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ item.id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ parseJSON(item.data).EM }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span v-if="parseJSON(item.data).Eigenschaft" class="inline-block px-2.5 py-1 rounded-md bg-indigo-100 text-indigo-800 font-medium text-sm shadow-sm border border-indigo-200">
                                            {{ parseJSON(item.data).Eigenschaft }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ parseJSON(item.data).EM_HF }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                                        <div class="flex justify-end gap-3">
                                            <button
                                                @click="openViewModal(item)"
                                                class="text-blue-600 hover:text-blue-800"
                                                title="Anzeigen"
                                            >
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button
                                                @click="duplicateSollvorgabe(item)"
                                                class="text-purple-600 hover:text-purple-800"
                                                title="Duplizieren"
                                            >
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button
                                                @click="openEditModal(item)"
                                                class="text-green-600 hover:text-green-800"
                                                title="Bearbeiten"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button
                                                @click="confirmDelete(item)"
                                                class="text-red-600 hover:text-red-800"
                                                title="Löschen"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <Modal :show="showModal" @close="closeModal" :maxWidth="'2xl'">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Materialkennwerte Verwaltung
                    </h3>
                    <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Grundeigenschaften -->
                <div class="mb-4">
                    <h4 class="text-lg font-semibold mb-3">Grundeigenschaften</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Eigenschaft
                            </label>
                            <input
                                type="text"
                                v-model="formData.Eigenschaft"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Material (EM)
                            </label>
                            <input
                                type="text"
                                v-model="formData.EM"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Material HF (EM_HF)
                            </label>
                            <input
                                type="text"
                                v-model="formData.EM_HF"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>
                </div>

                <!-- Chemische Zusammensetzung -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-4">Chemische Zusammensetzung (%)</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Kohlenstoff (C)
                            </label>
                            <input
                                type="text"
                                v-model="formData.C_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Phosphor (P)
                            </label>
                            <input
                                type="text"
                                v-model="formData.P_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Schwefel (S)
                            </label>
                            <input
                                type="text"
                                v-model="formData.S_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Chrom (Cr)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Cr_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Kupfer (Cu)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Cu_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Mangan (Mn)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Mn_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Molybdän (Mo)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Mo_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Nickel (Ni)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Ni_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Silizium (Si)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Si_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Magnesium (Mg)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Mg_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Zinn (Sn)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Sn_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>
                </div>

                <!-- Temperaturwerte -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-4">Temperaturwerte (°C)</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Liquidustemperatur (Rotekarte)
                            </label>
                            <input
                                type="text"
                                v-model="formData.Liq_r_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Liquidustemperatur
                            </label>
                            <input
                                type="text"
                                v-model="formData.Liq_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Unterkühlung (Rotekarte)
                            </label>
                            <input
                                type="text"
                                v-model="formData.UK_r_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Abstichtemperatur
                            </label>
                            <input
                                type="text"
                                v-model="formData.Abstichtemperatur_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Warmhaltetemperatur
                            </label>
                            <input
                                type="text"
                                v-model="formData.Warmhaltetemperatur_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Überhitzungstemperatur
                            </label>
                            <input
                                type="text"
                                v-model="formData.Überhitzungstemperatur_percent"
                                class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-4">
                    <button
                        @click="closeModal"
                        class="px-3 py-1.5 border rounded-lg hover:bg-gray-100"
                    >
                        Abbrechen
                    </button>
                    <button
                        @click="saveSollvorgabe"
                        class="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        Speichern
                    </button>
                </div>
            </div>
        </Modal>

        <!-- View Modal -->
        <Modal :show="showViewModal" @close="closeViewModal">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    JSON Daten Ansicht
                </h3>
                <div class="mb-4 overflow-auto max-h-[60vh]">
                    <vue-json-pretty
                        v-if="selectedItem && selectedItem.data"
                        :data="parseJSON(selectedItem.data)"
                        :deep="2"
                        :show-double-quotes="true"
                        :show-length="true"
                        :show-line="true"
                    ></vue-json-pretty>
                </div>
                <div class="flex justify-end">
                    <button
                        @click="closeViewModal"
                        class="px-4 py-2 border rounded-lg hover:bg-gray-100"
                    >
                        Schließen
                    </button>
                </div>
            </div>
        </Modal>
    </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import Modal from '@/Components/Modal.vue'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import Swal from 'sweetalert2'

const props = defineProps({
    sollvorgaben: {
        type: Array,
        required: true
    }
})

const search = ref('')
const showModal = ref(false)
const showViewModal = ref(false)
const isEditing = ref(false)
const selectedItem = ref(null)
const formData = ref({
    Eigenschaft: '',
    EM: '',
    EM_HF: '',
    C_percent: '',
    P_percent: '',
    S_percent: '',
    Cr_percent: '',
    Cu_percent: '',
    Mn_percent: '',
    Mo_percent: '',
    Ni_percent: '',
    Si_percent: '',
    Sn_percent: '',
    Mg_percent: '',
    Liq_r_percent: '',
    Liq_percent: '',
    UK_r_percent: '',
    Abstichtemperatur_percent: '',
    Warmhaltetemperatur_percent: '',
    Überhitzungstemperatur_percent: ''
})
const viewMode = ref('box')

// Helper function to safely parse JSON
const parseJSON = (jsonString) => {
    try {
        return typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    } catch (error) {
        console.error('JSON Parse Error:', error)
        return {}
    }
}

const filteredSollvorgaben = computed(() => {
    if (!search.value) return props.sollvorgaben;

    const searchLower = search.value.toLowerCase();
    return props.sollvorgaben.filter(item => {
        const data = parseJSON(item.data);
        return (
            (data.Eigenschaft?.toString().toLowerCase().includes(searchLower)) ||
            (data.EM?.toString().toLowerCase().includes(searchLower)) ||
            (data.EM_HF?.toString().toLowerCase().includes(searchLower)) ||
            (data.C_percent?.toString().toLowerCase().includes(searchLower)) ||
            (data.Ni_percent?.toString().toLowerCase().includes(searchLower)) ||
            (data.Liq_percent?.toString().toLowerCase().includes(searchLower)) ||
            (data.Liq_r_percent?.toString().toLowerCase().includes(searchLower)) ||
            (data.UK_r_percent?.toString().toLowerCase().includes(searchLower)) ||
            item.id.toString().includes(searchLower)
        );
    });
});

const openCreateModal = () => {
    isEditing.value = false
    formData.value = {
        Eigenschaft: '',
        EM: '',
        EM_HF: '',
        C_percent: '',
        P_percent: '',
        S_percent: '',
        Cr_percent: '',
        Cu_percent: '',
        Mn_percent: '',
        Mo_percent: '',
        Ni_percent: '',
        Si_percent: '',
        Sn_percent: '',
        Mg_percent: '',
        Liq_r_percent: '',
        Liq_percent: '',
        UK_r_percent: '',
        Abstichtemperatur_percent: '',
        Warmhaltetemperatur_percent: '',
        Überhitzungstemperatur_percent: ''
    }
    showModal.value = true
}

const openEditModal = (item) => {
    isEditing.value = true
    selectedItem.value = item
    try {
        const data = typeof item.data === 'string' ? JSON.parse(item.data) : item.data
        formData.value = {
            Eigenschaft: data.Eigenschaft || '',
            EM: data.EM || '',
            EM_HF: data.EM_HF || '',
            C_percent: data.C_percent || '',
            P_percent: data.P_percent || '',
            S_percent: data.S_percent || '',
            Cr_percent: data.Cr_percent || '',
            Cu_percent: data.Cu_percent || '',
            Mn_percent: data.Mn_percent || '',
            Mo_percent: data.Mo_percent || '',
            Ni_percent: data.Ni_percent || '',
            Si_percent: data.Si_percent || '',
            Sn_percent: data.Sn_percent || '',
            Mg_percent: data.Mg_percent || '',
            Liq_r_percent: data.Liq_r_percent || '',
            Liq_percent: data.Liq_percent || '',
            UK_r_percent: data.UK_r_percent || '',
            Abstichtemperatur_percent: data.Abstichtemperatur_percent || '',
            Warmhaltetemperatur_percent: data.Warmhaltetemperatur_percent || '',
            Überhitzungstemperatur_percent: data.Überhitzungstemperatur_percent || ''
        }
    } catch (error) {
        console.error('Error parsing JSON:', error)
        formData.value = {
            Eigenschaft: '',
            EM: '',
            EM_HF: '',
            C_percent: '',
            P_percent: '',
            S_percent: '',
            Cr_percent: '',
            Cu_percent: '',
            Mn_percent: '',
            Mo_percent: '',
            Ni_percent: '',
            Si_percent: '',
            Sn_percent: '',
            Mg_percent: '',
            Liq_r_percent: '',
            Liq_percent: '',
            UK_r_percent: '',
            Abstichtemperatur_percent: '',
            Warmhaltetemperatur_percent: '',
            Überhitzungstemperatur_percent: ''
        }
    }
    showModal.value = true
}

const openViewModal = (item) => {
    selectedItem.value = item
    showViewModal.value = true
}

const closeModal = () => {
    showModal.value = false
    formData.value = {
        Eigenschaft: '',
        EM: '',
        EM_HF: '',
        C_percent: '',
        P_percent: '',
        S_percent: '',
        Cr_percent: '',
        Cu_percent: '',
        Mn_percent: '',
        Mo_percent: '',
        Ni_percent: '',
        Si_percent: '',
        Sn_percent: '',
        Mg_percent: '',
        Liq_r_percent: '',
        Liq_percent: '',
        UK_r_percent: '',
        Abstichtemperatur_percent: '',
        Warmhaltetemperatur_percent: '',
        Überhitzungstemperatur_percent: ''
    }
    selectedItem.value = null
}

const closeViewModal = () => {
    showViewModal.value = false
    selectedItem.value = null
}

const saveSollvorgabe = async () => {
    try {
        const jsonData = {
            Eigenschaft: formData.value.Eigenschaft,
            EM: formData.value.EM,
            EM_HF: formData.value.EM_HF,
            C_percent: formData.value.C_percent,
            P_percent: formData.value.P_percent,
            S_percent: formData.value.S_percent,
            Cr_percent: formData.value.Cr_percent,
            Cu_percent: formData.value.Cu_percent,
            Mn_percent: formData.value.Mn_percent,
            Mo_percent: formData.value.Mo_percent,
            Ni_percent: formData.value.Ni_percent,
            Si_percent: formData.value.Si_percent,
            Sn_percent: formData.value.Sn_percent,
            Mg_percent: formData.value.Mg_percent,
            Liq_r_percent: formData.value.Liq_r_percent,
            Liq_percent: formData.value.Liq_percent,
            UK_r_percent: formData.value.UK_r_percent,
            Abstichtemperatur_percent: formData.value.Abstichtemperatur_percent,
            Warmhaltetemperatur_percent: formData.value.Warmhaltetemperatur_percent,
            Überhitzungstemperatur_percent: formData.value.Überhitzungstemperatur_percent
        }

        const url = isEditing.value
            ? `/sollvorgaben/${selectedItem.value.id}`
            : '/sollvorgaben'

        const method = isEditing.value ? 'put' : 'post'

        await axios[method](url, { data: JSON.stringify(jsonData) })

        Swal.fire({
            icon: 'success',
            title: 'Erfolg!',
            text: isEditing.value
                ? 'Materialkennwerte wurden erfolgreich aktualisiert!'
                : 'Materialkennwerte wurden erfolgreich erstellt!',
            showConfirmButton: false,
            timer: 1500
        })

        closeModal()
        window.location.reload()
    } catch (error) {
        console.error('Save Error:', error)
        Swal.fire({
            icon: 'error',
            title: 'Fehler!',
            text: 'Schau dir deine Eingaben nochmal an.',
        })
    }
}

const confirmDelete = (item) => {
    Swal.fire({
        title: 'Bist du dir sicher?',
        text: "Diese Aktion kann nicht rückgängig gemacht werden!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ja, löschen!',
        cancelButtonText: 'Abbrechen'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                await axios.delete(`/sollvorgaben/${item.id}`)
                Swal.fire(
                    'Gelöscht!',
                    'Die Sollvorgabe wurde erfolgreich gelöscht.',
                    'success'
                )
                window.location.reload()
            } catch (error) {
                Swal.fire(
                    'Fehler!',
                    'Beim Löschen ist ein Fehler aufgetreten.',
                    'error'
                )
            }
        }
    })
}

const duplicateSollvorgabe = async (item) => {
    // Bestätigungsdialog anzeigen
    Swal.fire({
        title: 'Datensatz duplizieren?',
        text: "Möchten Sie diesen Datensatz wirklich duplizieren?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#7e3af2', // Lila Farbe für den Bestätigungs-Button
        cancelButtonColor: '#718096', // Grau für den Abbrechen-Button
        confirmButtonText: 'Ja, duplizieren!',
        cancelButtonText: 'Abbrechen'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                // Parse the data from the original item
                const originalData = parseJSON(item.data);
                
                // Create the JSON data for the new item
                const jsonData = {
                    Eigenschaft: originalData.Eigenschaft ? `${originalData.Eigenschaft} (Kopie)` : 'Kopie',
                    EM: originalData.EM ? `${originalData.EM} (Kopie)` : '',
                    EM_HF: originalData.EM_HF || '',
                    C_percent: originalData.C_percent || '',
                    P_percent: originalData.P_percent || '',
                    S_percent: originalData.S_percent || '',
                    Cr_percent: originalData.Cr_percent || '',
                    Cu_percent: originalData.Cu_percent || '',
                    Mn_percent: originalData.Mn_percent || '',
                    Mo_percent: originalData.Mo_percent || '',
                    Ni_percent: originalData.Ni_percent || '',
                    Si_percent: originalData.Si_percent || '',
                    Sn_percent: originalData.Sn_percent || '',
                    Mg_percent: originalData.Mg_percent || '',
                    Liq_r_percent: originalData.Liq_r_percent || '',
                    Liq_percent: originalData.Liq_percent || '',
                    UK_r_percent: originalData.UK_r_percent || '',
                    Abstichtemperatur_percent: originalData.Abstichtemperatur_percent || '',
                    Warmhaltetemperatur_percent: originalData.Warmhaltetemperatur_percent || '',
                    Überhitzungstemperatur_percent: originalData.Überhitzungstemperatur_percent || ''
                };

                // Save the new item
                await axios.post('/sollvorgaben', { data: JSON.stringify(jsonData) });

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Dupliziert!',
                    text: 'Der Datensatz wurde erfolgreich dupliziert.',
                    showConfirmButton: false,
                    timer: 1500
                });

                // Reload the page to show the new item
                setTimeout(() => {
                    window.location.reload();
                }, 1600);
            } catch (error) {
                console.error('Duplicate Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Fehler!',
                    text: 'Beim Duplizieren ist ein Fehler aufgetreten.',
                });
            }
        }
    });
}
</script>
