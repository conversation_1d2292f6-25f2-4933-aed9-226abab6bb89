<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div v-if="rotekarte.formanlage_daten" class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="rounded-md bg-yellow-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Formular bereits ausgefüllt
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>
                                    Die Formanlage-Daten wurden bereits für diese Rotekarte erfasst. Sie können die Daten in der Übersicht einsehen.
                                </p>
                            </div>
                            <div class="mt-4">
                                <div class="-mx-2 -my-1.5 flex">
                                    <Link
                                        :href="route('rotekarte.show', rotekarte.id)"
                                        class="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                                    >
                                        Zur Übersicht
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <!-- Original form content -->
                <div class="max-w-7xl mx-auto">
                    <!-- Header Card -->
                    <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                        <div class="flex justify-between items-center">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">
                                    Formanlage
                                </h1>
                                <p class="text-gray-500 mt-1">
                                    Rotekarte Nr. {{ props.rotekarte?.id }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message for Missing Rotekarte -->
                    <div v-if="!rotekarte" class="bg-white rounded-lg shadow-sm p-6">
                        <div class="bg-red-50 border-l-4 border-red-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Keine Rotekarte ausgewählt</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>Bitte wählt zuerst eine Rotekarte im Dashboard aus.</p>
                                    </div>
                                    <div class="mt-4">
                                        <Link
                                            :href="route('dashboard')"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        >
                                            Zurück zum Dashboard
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <form v-else @submit.prevent="submit" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column -->
                        <div class="lg:col-span-2 space-y-6">
                            <!-- Basis Informationen Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-6">Basis Informationen</h2>
                                <div class="grid grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Verantwortlicher</label>
                                        <input type="text" v-model="form.formanlage_daten.verantwortlicher"
                                            @keydown="handleKeyDown($event, 'giessdatum')"
                                            id="verantwortlicher"
                                            placeholder="Name eingeben"
                                            :class="[
                                                'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                !form.formanlage_daten.verantwortlicher && !form.formanlage_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.formanlage_daten.verantwortlicher" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Hey, bitte gib deinen Namen ein 😊</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Abteilung</label>
                                        <select v-model="form.formanlage_daten.abteilung"
                                            @keydown="handleKeyDown($event, 'anzahl_kasten')"
                                            id="abteilung"
                                            disabled
                                            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 bg-gray-100 cursor-not-allowed">
                                            <option value="">Bitte wählen</option>
                                            <option value="NG">NG</option>
                                            <option value="GG">GG</option>
                                            <option value="HF">HF</option>
                                        </select>
                                        <div v-if="!form.formanlage_daten.abteilung" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Wähle bitte eine Abteilung aus 🏢</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Gießdatum</label>
                                        <input type="date" v-model="form.formanlage_daten.giessdatum"
                                            @keydown="handleKeyDown($event, 'giesszeit')"
                                            id="giessdatum"
                                            :class="[
                                                'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                !form.formanlage_daten.giessdatum && !form.formanlage_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.formanlage_daten.giessdatum" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Wähle bitte ein Gießdatum aus 📅</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Gießzeit</label>
                                        <input type="time" v-model="form.formanlage_daten.giesszeit"
                                            @keydown="handleKeyDown($event, 'abteilung')"
                                            id="giesszeit"
                                            :class="[
                                                'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                !form.formanlage_daten.giesszeit && !form.formanlage_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <p v-if="form.errors['formanlage_daten.giesszeit']" class="mt-1 text-sm text-red-600">
                                            {{ form.errors['formanlage_daten.giesszeit'] }}
                                        </p>
                                        <div v-if="!form.formanlage_daten.giesszeit" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Wähle bitte eine Gießzeit aus ⏰</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Teile Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <div class="flex justify-between items-center mb-6">
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Teile</h2>
                                        <div v-if="(teilForm.teilenummer || teilForm.anzahl) && !form.formanlage_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Klick auf "Teil hinzufügen" um das Teil zu speichern ➕</p>
                                        </div>
                                    </div>
                                    <button type="button"
                                        @click="addTeil"
                                        :disabled="!teilForm.teilenummer || !teilForm.anzahl"
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                        <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Teil hinzufügen
                                    </button>
                                </div>

                                <!-- New Teil Form -->
                                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Teilenummer</label>
                                            <input type="text"
                                                v-model="teilForm.teilenummer"
                                                @keydown="handleKeyDown($event, 'anzahl')"
                                                id="teilenummer"
                                                placeholder="Teilenummer eingeben"
                                                :class="[
                                                    'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                    !teilForm.teilenummer && !form.formanlage_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                                ]">
                                            <div v-if="!teilForm.teilenummer && !form.formanlage_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                                <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p class="text-sm text-pink-500">Trag bitte eine Teilenummer ein 🔢</p>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Anzahl</label>
                                            <input type="number"
                                                v-model="teilForm.anzahl"
                                                @keydown="handleKeyDown($event, 'addTeilButton')"
                                                id="anzahl"
                                                placeholder="Anzahl eingeben"
                                                :class="[
                                                    'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                    !teilForm.anzahl && !form.formanlage_daten.teile.length ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                                ]">
                                            <div v-if="!teilForm.anzahl && !form.formanlage_daten.teile.length" class="mt-1.5 flex items-center space-x-1">
                                                <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p class="text-sm text-pink-500">Trag bitte die Anzahl ein 📊</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Teile List -->
                                <div class="overflow-hidden rounded-lg border border-gray-200">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Teilenummer</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Anzahl</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Aktionen</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="(teil, index) in form.formanlage_daten.teile" :key="index">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ teil.teilenummer }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ teil.anzahl }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <button @click="form.formanlage_daten.teile.splice(index, 1)"
                                                        class="text-gray-400 hover:text-red-600">
                                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                            <!-- Bemerkungen Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Bemerkungen</h2>
                                <textarea v-model="form.formanlage_daten.bemerkungen"
                                    rows="4"
                                    placeholder="Bemerkungen eingeben"
                                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                                <p v-if="form.errors['formanlage_daten.bemerkungen']" class="mt-1 text-sm text-red-600">
                                    {{ form.errors['formanlage_daten.bemerkungen'] }}
                                </p>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Checkboxes Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Prüfungen</h2>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <input type="checkbox" v-model="form.formanlage_daten.formen_gekennzeichnet"
                                                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                            <label class="ml-2 block text-sm text-gray-900">Formen gekennzeichnet?</label>
                                        </div>
                                        <div class="flex items-center">
                                            <span v-if="form.formanlage_daten.formen_gekennzeichnet" class="text-green-600">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </span>
                                            <span v-else class="text-red-600 font-medium flex items-center">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                <span class="ml-1">Nein</span>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <input type="checkbox" v-model="form.formanlage_daten.formen_an_anlage"
                                                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                            <label class="ml-2 block text-sm text-gray-900">Formen an der Anlage ausgeschleust?</label>
                                        </div>
                                        <div class="flex items-center">
                                            <span v-if="form.formanlage_daten.formen_an_anlage" class="text-green-600">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </span>
                                            <span v-else class="text-red-600 font-medium flex items-center">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                <span class="ml-1">Nein</span>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <input type="checkbox" v-model="form.formanlage_daten.ausschussseparation"
                                                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                            <label class="ml-2 block text-sm text-gray-900">Ausschussseparation durchgeführt?</label>
                                        </div>
                                        <div class="flex items-center">
                                            <span v-if="form.formanlage_daten.ausschussseparation" class="text-green-600">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </span>
                                            <span v-else class="text-red-600 font-medium flex items-center">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                <span class="ml-1">Nein</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Info Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <div class="flex items-start justify-between mb-4">
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Zusätzliche Informationen</h2>
                                        <div v-if="!form.formanlage_daten.anzahl_kasten || !form.formanlage_daten.sap_zahlnummer" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Daten bitte ergänzen.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Anzahl Kästen</label>
                                        <input type="number" v-model="form.formanlage_daten.anzahl_kasten"
                                            @keydown="handleKeyDown($event, 'sap_zahlnummer')"
                                            id="anzahl_kasten"
                                            placeholder="Anzahl eingeben"
                                            :class="[
                                                'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                !form.formanlage_daten.anzahl_kasten ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.formanlage_daten.anzahl_kasten" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Trag bitte die Anzahl der Kästen ein 📦</p>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">SAP Zahlnummer</label>
                                        <input type="text" v-model="form.formanlage_daten.sap_zahlnummer"
                                            @keydown="handleKeyDown($event, 'teilenummer')"
                                            id="sap_zahlnummer"
                                            placeholder="SAP-Nummer eingeben"
                                            :class="[
                                                'block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500',
                                                !form.formanlage_daten.sap_zahlnummer ? 'border-pink-300 focus:border-pink-500 focus:ring-pink-500' : 'border-gray-300'
                                            ]">
                                        <div v-if="!form.formanlage_daten.sap_zahlnummer" class="mt-1.5 flex items-center space-x-1">
                                            <svg class="h-4 w-4 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-sm text-pink-500">Trag bitte die SAP Zahlnummer ein 🔢</p>
                                        </div>
                                    </div>

                                    <!-- System Information -->
                                    <div class="col-span-2 pt-4 border-t border-gray-200">
                                        <h3 class="text-sm font-medium text-gray-700 mb-2">System Information</h3>
                                        <div class="grid grid-cols-1 gap-4 mb-4">
                                            <div>
                                                <span class="text-xs text-gray-500">Benutzer</span>
                                                <p class="text-sm font-medium text-gray-900">{{ form.formanlage_daten.system_info.current.username }}</p>
                                            </div>
                                        </div>

                                        <!-- System Info History -->
                                        <div v-if="form.formanlage_daten.system_info.history.length > 0">
                                            <h4 class="text-xs font-medium text-gray-500 mb-2">Historie</h4>
                                            <div class="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
                                                <div v-for="(entry, index) in form.formanlage_daten.system_info.history"
                                                     :key="index"
                                                     class="text-xs mb-2 last:mb-0 pb-2 last:pb-0 border-b last:border-b-0 border-gray-200">
                                                    <div class="flex justify-between items-start">
                                                        <div>
                                                            <span class="font-medium">{{ entry.username }}</span>
                                                            <span class="text-gray-500"> auf </span>
                                                            <span class="font-medium">{{ entry.hostname }}</span>
                                                        </div>
                                                        <span class="text-gray-400">{{ entry.timestamp }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Action Buttons Card -->
                            <div class="bg-white rounded-lg shadow-sm p-6 space-y-3">
                                <button type="submit"
                                    :disabled="form.processing"
                                    class="w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    {{ form.processing ? 'Wird gespeichert...' : 'Speichern' }}
                                </button>
                                <Link :href="route('dashboard')"
                                    class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Abbrechen
                                </Link>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';
import { useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import Swal from 'sweetalert2';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: false,
        default: null
    }
});

const teilForm = ref({
    teilenummer: '',
    anzahl: ''
});

const form = useForm({
    rotekarte_id: props.rotekarte?.id || null,
    formanlage_daten: {
        verantwortlicher: '',
        giessdatum: new Date().toISOString().split('T')[0],
        giesszeit: new Date().toTimeString().split(':').slice(0, 2).join(':'),
        abteilung: props.rotekarte?.spektrometer_daten?.abteilung || '',
        formen_gekennzeichnet: false,
        formen_an_anlage: false,
        ausschussseparation: false,
        teile: [],
        anzahl_kasten: '',
        sap_zahlnummer: '',
        bemerkungen: '',
        system_info: {
            current: props.rotekarte?.formanlage_daten?.system_info?.current || {
                username: '',
                hostname: '',
                timestamp: ''
            },
            history: props.rotekarte?.formanlage_daten?.system_info?.history || []
        }
    }
});

const addTeil = () => {
    if (teilForm.value.teilenummer && teilForm.value.anzahl) {
        form.formanlage_daten.teile.push({
            teilenummer: teilForm.value.teilenummer,
            anzahl: parseInt(teilForm.value.anzahl)
        });
        teilForm.value.teilenummer = '';
        teilForm.value.anzahl = '';
    }
};

const captureSystemInfo = async () => {
    try {
        const response = await fetch('/system-info', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.username || data.hostname) {
            form.formanlage_daten.system_info.current = {
                username: data.username || 'Nicht verfügbar',
                hostname: data.hostname || 'Nicht verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        } else {
            form.formanlage_daten.system_info.current = {
                username: 'Keine Daten verfügbar',
                hostname: 'Keine Daten verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        }
    } catch (error) {
        form.formanlage_daten.system_info.current = {
            username: 'Systemfehler',
            hostname: 'Systemfehler',
            timestamp: new Date().toLocaleString('de-DE')
        };
    }
};

// Call on component mount
onMounted(() => {
    captureSystemInfo();
});

const submit = () => {
    // Validate required fields before submission
    const requiredFields = {
        'Verantwortlicher': form.formanlage_daten.verantwortlicher,
        'Gießdatum': form.formanlage_daten.giessdatum,
        'Gießzeit': form.formanlage_daten.giesszeit,
        'Abteilung': form.formanlage_daten.abteilung,
        'Anzahl Kästen': form.formanlage_daten.anzahl_kasten,
        'SAP Zahlnummer': form.formanlage_daten.sap_zahlnummer
    };

    // Check if teile array is empty
    if (form.formanlage_daten.teile.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'Fehlende Teile',
            text: 'Bitte fügen Sie mindestens ein Teil hinzu.',
            showConfirmButton: true
        });
        return;
    }

    // Check required fields
    const missingFields = Object.entries(requiredFields)
        .filter(([_, value]) => !value)
        .map(([key]) => key);

    if (missingFields.length > 0) {
        Swal.fire({
            icon: 'error',
            title: 'Fehlende Pflichtfelder',
            text: `Bitte füllen Sie folgende Felder aus: ${missingFields.join(', ')}`,
            showConfirmButton: true
        });
        return;
    }

    // Add current system info to history before submitting
    const currentInfo = {
        username: form.formanlage_daten.system_info.current.username,
        hostname: form.formanlage_daten.system_info.current.hostname,
        timestamp: new Date().toLocaleString('de-DE')
    };

    // Ensure history array exists
    if (!Array.isArray(form.formanlage_daten.system_info.history)) {
        form.formanlage_daten.system_info.history = [];
    }

    // Add to history
    form.formanlage_daten.system_info.history.push(currentInfo);

    // Ensure checkbox fields are boolean
    form.formanlage_daten.formen_gekennzeichnet = !!form.formanlage_daten.formen_gekennzeichnet;
    form.formanlage_daten.formen_an_anlage = !!form.formanlage_daten.formen_an_anlage;
    form.formanlage_daten.ausschussseparation = !!form.formanlage_daten.ausschussseparation;

    form.post(route('formanlage.store'), {
        preserveScroll: true,
        onSuccess: () => {
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich gespeichert',
                text: 'Die Daten wurden erfolgreich gespeichert.',
                showConfirmButton: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '/';
                }
            });
        },
        onError: (errors) => {
            console.error('Fehler beim Speichern:', errors);
            let errorMessage = 'Folgende Fehler sind aufgetreten:\n\n';
            
            Object.entries(errors).forEach(([field, messages]) => {
                errorMessage += `${field}: ${messages.join(', ')}\n`;
            });

            Swal.fire({
                icon: 'error',
                title: 'Fehler beim Speichern',
                text: errorMessage,
                showConfirmButton: true
            });
        }
    });
};

const handleKeyDown = (event, nextFieldId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (nextFieldId === 'addTeilButton') {
            addTeil();
        } else {
            document.getElementById(nextFieldId)?.focus();
        }
    }
};
</script>
