<template>
    <AppLayout title="Prüfkatalog JSON-Korrektur">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Prüfkatalog JSON-Korrektur
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <p class="mb-4 text-gray-700">
                            Diese Funktion korrigiert die JSON-Daten für alle Prüfkatalog-Einträge, bei denen die
                            strukturierten Spalten und das JSON-Feld nicht übereinstimmen.
                        </p>

                        <p class="mb-4 text-gray-700">
                            Es stehen zwei Funktionen zur Verfügung:
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li><strong>JSON-Daten korrigieren</strong>: Aktualisiert das JSON-Feld basierend auf den strukturierten Spalten</li>
                                <li><strong>Strukturierte Felder korrigieren</strong>: Analysiert die Absolutwerte-Spalte und aktualisiert die strukturierten Felder</li>
                                <li><strong>Verbesserte Analyse</strong>: Verwendet einen verbesserten Parser für spezielle Formate wie "UGW – ≥ 0,05%"</li>
                                <li><strong>Vollständige Resynchronisierung</strong>: Parst alle Einträge neu und synchronisiert alle Felder</li>
                            </ul>
                        </p>

                        <div v-if="result" class="mt-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                        fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">Erfolgreich abgeschlossen</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            <li>Gesamtzahl der Einträge: {{ result.total }}</li>
                                            <li>Erfolgreich korrigiert: {{ result.success }}</li>
                                            <li>Fehler bei der Korrektur: {{ result.failed }}</li>
                                        </ul>
                                        
                                        <div v-if="result.examples && result.examples.length > 0" class="mt-4">
                                            <h4 class="text-sm font-medium text-green-800 mb-2">Beispiele für korrigierte Einträge:</h4>
                                            <div class="max-h-60 overflow-auto">
                                                <div v-for="(example, index) in result.examples" :key="index" class="mb-3 p-2 bg-green-50 border border-green-200 rounded">
                                                    <div class="text-sm">
                                                        <strong>Fehlercode:</strong> {{ example.fehlercode }}
                                                    </div>
                                                    <div class="text-sm">
                                                        <strong>Absolutwerte:</strong> {{ example.absolutwerte }}
                                                    </div>
                                                    <div class="text-sm">
                                                        <strong>Geparst:</strong>
                                                        <span class="ml-1">
                                                            Typ: {{ example.parsed.typ }},
                                                            Richtung: {{ example.parsed.richtung || '-' }},
                                                            Operator: {{ example.parsed.operator || '-' }},
                                                            Wert: {{ example.parsed.wert || '-' }},
                                                            Einheit: {{ example.parsed.einheit || '-' }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="error" class="mt-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                        fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1-9a1 1 0 112 0v4a1 1 0 11-2 0V9zm1-5.5a1 1 0 100 2 1 1 0 000-2z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">Es ist ein Fehler aufgetreten</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <p>{{ error }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex flex-wrap gap-4">
                            <button @click="fixJson" class="btn btn-primary" :disabled="loading">
                                <svg v-if="loading && activeOperation === 'json'" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {{ loading && activeOperation === 'json' ? 'Wird ausgeführt...' : 'JSON-Daten korrigieren' }}
                            </button>
                            
                            <button @click="fixStructuredFields" class="btn btn-secondary" :disabled="loading">
                                <svg v-if="loading && activeOperation === 'fields'" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {{ loading && activeOperation === 'fields' ? 'Wird ausgeführt...' : 'Strukturierte Felder aktualisieren' }}
                            </button>
                            
                            <button @click="reparseAbsolutwerte" class="btn btn-accent" :disabled="loading">
                                <svg v-if="loading && activeOperation === 'reparse'" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {{ loading && activeOperation === 'reparse' ? 'Wird ausgeführt...' : 'Verbesserte Analyse durchführen' }}
                            </button>
                            
                            <button @click="fullResync" class="btn btn-error" :disabled="loading">
                                <svg v-if="loading && activeOperation === 'fullresync'" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                {{ loading && activeOperation === 'fullresync' ? 'Wird ausgeführt...' : 'Vollständige Resynchronisierung' }}
                            </button>
                        </div>

                        <div class="mt-6">
                            <Link :href="route('pruefkatalog.index')" class="text-indigo-600 hover:text-indigo-900">
                                Zurück zum Prüfkatalog
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Link, useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';

const loading = ref(false);
const result = ref(null);
const error = ref(null);
const activeOperation = ref(null);

const fixJson = async () => {
    loading.value = true;
    error.value = null;
    result.value = null;
    activeOperation.value = 'json';
    
    try {
        const response = await axios.post(route('pruefkatalog.fix-json'));
        result.value = response.data.result;
    } catch (err) {
        error.value = err.response?.data?.message || 'Ein unerwarteter Fehler ist aufgetreten';
    } finally {
        loading.value = false;
        activeOperation.value = null;
    }
};

const fixStructuredFields = async () => {
    loading.value = true;
    error.value = null;
    result.value = null;
    activeOperation.value = 'fields';
    
    try {
        const response = await axios.post(route('pruefkatalog.fix-structured-fields'));
        result.value = response.data.result;
    } catch (err) {
        error.value = err.response?.data?.message || 'Ein unerwarteter Fehler ist aufgetreten';
    } finally {
        loading.value = false;
        activeOperation.value = null;
    }
};

const reparseAbsolutwerte = async () => {
    loading.value = true;
    error.value = null;
    result.value = null;
    activeOperation.value = 'reparse';
    
    try {
        const response = await axios.post(route('pruefkatalog.reparse-absolutwerte'));
        result.value = response.data.result;
    } catch (err) {
        error.value = err.response?.data?.message || 'Ein unerwarteter Fehler ist aufgetreten';
    } finally {
        loading.value = false;
        activeOperation.value = null;
    }
};

const fullResync = async () => {
    loading.value = true;
    error.value = null;
    result.value = null;
    activeOperation.value = 'fullresync';
    
    try {
        const response = await axios.post(route('pruefkatalog.full-resync'));
        result.value = response.data.result;
    } catch (err) {
        error.value = err.response?.data?.message || 'Ein unerwarteter Fehler ist aufgetreten';
    } finally {
        loading.value = false;
        activeOperation.value = null;
    }
};
</script> 