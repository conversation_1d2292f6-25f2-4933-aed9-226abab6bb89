<template>
    <AppLayout title="Prüfkatalog">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="bg-indigo-600 p-8">
                    <h2 class="text-2xl font-bold text-white">Prüfkatalog</h2>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <!-- Search and Add Button -->
                    <div class="flex justify-between mb-6">
                        <div class="flex items-center gap-4">
                            <div class="relative w-64">
                                <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </span>
                                <input
                                    type="text"
                                    v-model="search"
                                    placeholder="Suchen..."
                                    class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>
                            <Link :href="route('pruefkatalog.migration')" class="text-indigo-600 hover:text-indigo-900">
                                Grenzwert-Migration
                            </Link>
                        </div>
                        <button
                            @click="openModal()"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 -ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Neuer Eintrag
                        </button>
                    </div>

                    <!-- Pruefkatalog Table -->
                    <div class="relative">
                        <!-- Scroll Indicators entfernt, da wir ohne horizontales Scrollen auskommen wollen -->
                        
                        <div class="rounded-lg border border-gray-200">
                            <div class="w-full align-middle">
                                <div class="overflow-hidden">
                                    <table class="w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aktionen</th>
                                                <th @click="sort('fehlercode')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Fehlercode
                                                    <span v-if="sortColumn === 'fehlercode'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('eisenmarke')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    E-Marke
                                                    <span v-if="sortColumn === 'eisenmarke'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('element')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Element
                                                    <span v-if="sortColumn === 'element'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('absolutwerte')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Absolutwerte
                                                    <span v-if="sortColumn === 'absolutwerte'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('grenzwert_typ')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Typ
                                                    <span v-if="sortColumn === 'grenzwert_typ'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('grenzwert_wert')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Wert
                                                    <span v-if="sortColumn === 'grenzwert_wert'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('teilesperren')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Sperren
                                                    <span v-if="sortColumn === 'teilesperren'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('massnahmen')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Maßnahmen
                                                    <span v-if="sortColumn === 'massnahmen'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                                <th @click="sort('bemerkungen')" class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                                    Bemerkungen
                                                    <span v-if="sortColumn === 'bemerkungen'" class="ml-1">
                                                        {{ sortDirection === 'asc' ? '↑' : '↓' }}
                                                    </span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-for="item in sortedAndFilteredPruefkatalogs" :key="item.id">
                                                <td class="px-2 py-2 whitespace-nowrap">
                                                    <div class="flex items-center space-x-1">
                                                        <button
                                                            @click="editItem(item)"
                                                            class="inline-flex items-center p-1 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded-full"
                                                            title="Bearbeiten"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                                            </svg>
                                                        </button>
                                                        <button
                                                            @click="deleteItem(item)"
                                                            class="inline-flex items-center p-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-full"
                                                            title="Löschen"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                            </svg>
                                                        </button>
                                                        <button
                                                            @click="duplicateItem(item)"
                                                            class="inline-flex items-center p-1 text-green-600 hover:text-green-900 hover:bg-green-50 rounded-full"
                                                            title="Duplizieren"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                                                                <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                                                            </svg>
                                                        </button>
                                                        <button
                                                            v-if="item.grenzwerte_json"
                                                            @click="showJsonData(item)"
                                                            class="inline-flex items-center p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-full"
                                                            title="JSON-Daten anzeigen"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2-3a1 1 0 011 1v5a1 1 0 11-2 0v-5a1 1 0 011-1zm4-1a1 1 0 10-2 0v7a1 1 0 102 0V8z" clip-rule="evenodd" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td class="px-2 py-2 whitespace-nowrap text-xs font-medium text-gray-900">{{ item.fehlercode }}</td>
                                                <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-500">{{ item.eisenmarke }}</td>
                                                <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-500">{{ item.element }}</td>
                                                <td class="px-2 py-2 text-xs text-gray-500 max-w-[120px] truncate" :title="item.absolutwerte">{{ item.absolutwerte }}</td>
                                                <td class="px-2 py-2 text-xs text-gray-500 truncate">
                                                    {{ item.grenzwert_typ || '-' }}
                                                </td>
                                                <td class="px-2 py-2 text-xs text-gray-500 max-w-[80px] truncate" :title="item.grenzwert_wert !== null ? `${item.grenzwert_richtung || ''} ${item.grenzwert_operator || ''} ${item.grenzwert_wert}${item.grenzwert_einheit || ''}` : '-'">
                                                    <span v-if="item.grenzwert_wert !== null">
                                                        {{ item.grenzwert_richtung || '' }} {{ item.grenzwert_operator || '' }} {{ item.grenzwert_wert }}{{ item.grenzwert_einheit || '' }}
                                                    </span>
                                                    <span v-else>-</span>
                                                </td>
                                                <td class="px-2 py-2 text-xs text-center">
                                                    <span :class="item.teilesperren ? 'text-red-600' : 'text-green-600'" class="inline-flex justify-center w-full font-medium">
                                                        {{ item.teilesperren ? 'Ja' : 'Nein' }}
                                                    </span>
                                                </td>
                                                <td class="px-2 py-2 text-xs text-gray-500 max-w-[120px] truncate" :title="item.massnahmen">{{ item.massnahmen }}</td>
                                                <td class="px-2 py-2 text-xs text-gray-500 max-w-[120px] truncate" :title="item.bemerkungen">{{ item.bemerkungen }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hinweis-Text zum Hovern für mehr Information -->
                        <div class="text-xs text-gray-500 mt-2 text-center">
                            <span class="inline-flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Bewegen Sie den Mauszeiger über abgeschnittene Texte, um den vollständigen Inhalt zu sehen
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal for Create/Edit -->
        <Modal :show="showModal" @close="closeModal" :closeable="false">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    {{ isEditing ? 'Prüfkatalog bearbeiten' : 'Neuer Prüfkatalog' }}
                </h3>
                <form @submit.prevent="submitForm" class="space-y-4">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Fehlercode</label>
                            <input
                                type="text"
                                v-model="form.fehlercode"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.fehlercode }"
                            />
                            <p v-if="errors.fehlercode" class="mt-1 text-sm text-red-600">{{ errors.fehlercode }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Eisenmarke</label>
                            <input
                                type="text"
                                v-model="form.eisenmarke"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.eisenmarke }"
                            />
                            <p v-if="errors.eisenmarke" class="mt-1 text-sm text-red-600">{{ errors.eisenmarke }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Element</label>
                            <input
                                type="text"
                                v-model="form.element"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.element }"
                            />
                            <p v-if="errors.element" class="mt-1 text-sm text-red-600">{{ errors.element }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Absolutwerte</label>
                            <input
                                type="text"
                                v-model="form.absolutwerte"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.absolutwerte }"
                            />
                            <p v-if="errors.absolutwerte" class="mt-1 text-sm text-red-600">{{ errors.absolutwerte }}</p>
                        </div>
                        
                        <!-- Strukturierte Grenzwert-Felder -->
                        <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Strukturierte Grenzwert-Daten (Optional)</h4>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Grenzwert-Typ</label>
                                    <select
                                        v-model="form.grenzwert_typ"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        <option value="">Bitte wählen</option>
                                        <option value="UGW">UGW (Unterer Grenzwert)</option>
                                        <option value="OGW">OGW (Oberer Grenzwert)</option>
                                        <option value="ABSOLUTE">ABSOLUTE (Absoluter Wert)</option>
                                        <option value="SPECIAL">SPECIAL (Spezialfall)</option>
                                        <option value="TEXT">TEXT (Textbeschreibung)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Richtung</label>
                                    <select
                                        v-model="form.grenzwert_richtung"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        <option value="">Keine</option>
                                        <option value="+">+ (Plus/Über)</option>
                                        <option value="-">- (Minus/Unter)</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Operator</label>
                                    <select
                                        v-model="form.grenzwert_operator"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        <option value="">Keiner</option>
                                        <option value="=">=</option>
                                        <option value="<"><</option>
                                        <option value=">">></option>
                                        <option value="<=">≤</option>
                                        <option value=">=">≥</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Wert</label>
                                    <input
                                        type="number"
                                        step="0.000001"
                                        v-model="form.grenzwert_wert"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Einheit</label>
                                    <input
                                        type="text"
                                        v-model="form.grenzwert_einheit"
                                        placeholder="z.B. %"
                                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                            </div>
                            
                            <div class="mt-2 text-xs text-gray-500">
                                <p>Strukturierte Daten werden automatisch aus dem Absolutwerte-Feld abgeleitet oder können hier manuell angepasst werden.</p>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Teilesperren</label>
                            <select
                                v-model="form.teilesperren"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.teilesperren }"
                            >
                                <option :value="true">Ja</option>
                                <option :value="false">Nein</option>
                            </select>
                            <p v-if="errors.teilesperren" class="mt-1 text-sm text-red-600">{{ errors.teilesperren }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Maßnahmen</label>
                            <input
                                type="text"
                                v-model="form.massnahmen"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.massnahmen }"
                            />
                            <p v-if="errors.massnahmen" class="mt-1 text-sm text-red-600">{{ errors.massnahmen }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Bemerkungen</label>
                            <input
                                type="text"
                                v-model="form.bemerkungen"
                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': errors.bemerkungen }"
                            />
                            <p v-if="errors.bemerkungen" class="mt-1 text-sm text-red-600">{{ errors.bemerkungen }}</p>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button
                            type="button"
                            @click="closeModal"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Abbrechen
                        </button>
                        <button
                            type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            :disabled="processing"
                        >
                            <svg v-if="processing" class="animate-spin h-5 w-5 mr-2 -ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ isEditing ? 'Aktualisieren' : 'Erstellen' }}
                        </button>
                    </div>
                </form>
            </div>
        </Modal>

        <!-- Modal for JSON Data -->
        <Modal :show="showJsonModal" @close="closeJsonModal">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    Strukturierte Grenzwerte für {{ selectedJsonItem?.fehlercode || '' }}
                </h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <pre v-if="selectedJsonItem?.grenzwerte_json" class="text-sm overflow-x-auto whitespace-pre-wrap break-words">{{ JSON.stringify(selectedJsonItem.grenzwerte_json, null, 2) }}</pre>
                    <p v-else class="text-gray-500 text-sm">Keine strukturierten Daten verfügbar.</p>
                </div>
                <div class="mt-6 grid grid-cols-2 gap-4">
                    <div v-if="selectedJsonItem?.grenzwerte_json">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Typ</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.typ || '-' }}</p>
                    </div>
                    <div v-if="selectedJsonItem?.grenzwerte_json">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Richtung</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.richtung || '-' }}</p>
                    </div>
                    <div v-if="selectedJsonItem?.grenzwerte_json">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Operator</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.operator || '-' }}</p>
                    </div>
                    <div v-if="selectedJsonItem?.grenzwerte_json">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Wert</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.wert !== null ? selectedJsonItem.grenzwerte_json.wert : '-' }}</p>
                    </div>
                    <div v-if="selectedJsonItem?.grenzwerte_json">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Einheit</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.einheit || '-' }}</p>
                    </div>
                    <div v-if="selectedJsonItem?.grenzwerte_json?.text" class="col-span-2">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Original-Text</h4>
                        <p class="text-sm bg-white p-2 border border-gray-200 rounded">{{ selectedJsonItem.grenzwerte_json.text }}</p>
                    </div>
                </div>
                <div class="mt-6 flex justify-end">
                    <button
                        @click="closeJsonModal"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Schließen
                    </button>
                </div>
            </div>
        </Modal>
    </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Modal from '@/Components/Modal.vue';
import Swal from 'sweetalert2';
import axios from 'axios';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
    pruefkatalogs: {
        type: Array,
        required: true
    }
});

const search = ref('');
const showModal = ref(false);
const isEditing = ref(false);
const selectedItem = ref(null);
const processing = ref(false);
const errors = ref({});
const sortColumn = ref('fehlercode');
const sortDirection = ref('asc');

const form = ref({
    fehlercode: '',
    eisenmarke: '',
    element: '',
    absolutwerte: '',
    teilesperren: false,
    massnahmen: '',
    bemerkungen: '',
    grenzwert_typ: '',
    grenzwert_richtung: '',
    grenzwert_operator: '',
    grenzwert_wert: null,
    grenzwert_einheit: ''
});

const showJsonModal = ref(false);
const selectedJsonItem = ref(null);

const sort = (column) => {
    if (sortColumn.value === column) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn.value = column;
        sortDirection.value = 'asc';
    }
};

const sortedAndFilteredPruefkatalogs = computed(() => {
    let filtered = props.pruefkatalogs;
    
    if (search.value) {
        const searchLower = search.value.toLowerCase();
        filtered = filtered.filter(item => {
            return (
                item.fehlercode.toLowerCase().includes(searchLower) ||
                item.eisenmarke.toLowerCase().includes(searchLower) ||
                item.element.toLowerCase().includes(searchLower) ||
                item.absolutwerte.toLowerCase().includes(searchLower) ||
                (item.grenzwert_typ && item.grenzwert_typ.toLowerCase().includes(searchLower)) ||
                (item.grenzwert_operator && item.grenzwert_operator.toLowerCase().includes(searchLower)) ||
                (item.grenzwert_richtung && item.grenzwert_richtung.toLowerCase().includes(searchLower)) ||
                (item.grenzwert_einheit && item.grenzwert_einheit.toLowerCase().includes(searchLower)) ||
                (item.grenzwert_wert !== null && String(item.grenzwert_wert).includes(searchLower)) ||
                item.massnahmen.toLowerCase().includes(searchLower) ||
                (item.bemerkungen && item.bemerkungen.toLowerCase().includes(searchLower))
            );
        });
    }

    return filtered.sort((a, b) => {
        let aValue = a[sortColumn.value];
        let bValue = b[sortColumn.value];

        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();

        if (aValue < bValue) return sortDirection.value === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection.value === 'asc' ? 1 : -1;
        return 0;
    });
});

const openModal = () => {
    isEditing.value = false;
    selectedItem.value = null;
    errors.value = {};
    form.value = {
        fehlercode: '',
        eisenmarke: '',
        element: '',
        absolutwerte: '',
        teilesperren: false,
        massnahmen: '',
        bemerkungen: '',
        grenzwert_typ: '',
        grenzwert_richtung: '',
        grenzwert_operator: '',
        grenzwert_wert: null,
        grenzwert_einheit: ''
    };
    showModal.value = true;
};

const editItem = (item) => {
    isEditing.value = true;
    selectedItem.value = item;
    errors.value = {};
    form.value = { ...item };
    showModal.value = true;
};

const closeModal = () => {
    if (processing.value) return;
    
    showModal.value = false;
    errors.value = {};
    form.value = {
        fehlercode: '',
        eisenmarke: '',
        element: '',
        absolutwerte: '',
        teilesperren: false,
        massnahmen: '',
        bemerkungen: '',
        grenzwert_typ: '',
        grenzwert_richtung: '',
        grenzwert_operator: '',
        grenzwert_wert: null,
        grenzwert_einheit: ''
    };
};

const submitForm = () => {
    if (processing.value) return;
    
    processing.value = true;
    errors.value = {};

    const formData = {
        fehlercode: form.value.fehlercode,
        eisenmarke: form.value.eisenmarke,
        element: form.value.element,
        absolutwerte: form.value.absolutwerte,
        teilesperren: form.value.teilesperren,
        massnahmen: form.value.massnahmen,
        bemerkungen: form.value.bemerkungen,
        grenzwert_typ: form.value.grenzwert_typ,
        grenzwert_richtung: form.value.grenzwert_richtung,
        grenzwert_operator: form.value.grenzwert_operator,
        grenzwert_wert: form.value.grenzwert_wert,
        grenzwert_einheit: form.value.grenzwert_einheit
    };

    if (isEditing.value) {
        axios.put(`/pruefkatalog/${selectedItem.value.id}`, formData)
            .then(() => {
                showModal.value = false;
                processing.value = false;
                Swal.fire({
                    icon: 'success',
                    title: 'Erfolgreich aktualisiert',
                    showConfirmButton: false,
                    timer: 1500
                });
                router.reload();
            })
            .catch(error => {
                processing.value = false;
                if (error.response?.data?.errors) {
                    errors.value = error.response.data.errors;
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Fehler',
                        text: 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
                    });
                }
            });
    } else {
        axios.post('/pruefkatalog', formData)
            .then(() => {
                showModal.value = false;
                processing.value = false;
                Swal.fire({
                    icon: 'success',
                    title: 'Erfolgreich erstellt',
                    showConfirmButton: false,
                    timer: 1500
                });
                router.reload();
            })
            .catch(error => {
                processing.value = false;
                if (error.response?.data?.errors) {
                    errors.value = error.response.data.errors;
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Fehler',
                        text: 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.',
                    });
                }
            });
    }
};

const deleteItem = (item) => {
    Swal.fire({
        title: 'Sind Sie sicher?',
        text: "Diese Aktion kann nicht rückgängig gemacht werden!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ja, löschen!',
        cancelButtonText: 'Abbrechen'
    }).then((result) => {
        if (result.isConfirmed) {
            router.delete(route('pruefkatalog.destroy', item.id), {
                onSuccess: () => {
                    Swal.fire(
                        'Gelöscht!',
                        'Der Eintrag wurde erfolgreich gelöscht.',
                        'success'
                    );
                },
                onError: () => {
                    Swal.fire(
                        'Fehler',
                        'Der Eintrag konnte nicht gelöscht werden.',
                        'error'
                    );
                }
            });
        }
    });
};

const duplicateItem = (item) => {
    isEditing.value = false;
    selectedItem.value = null;
    errors.value = {};
    
    // Create a copy of the item with a modified fehlercode
    const duplicatedItem = { ...item };
    delete duplicatedItem.id; // Remove the ID to create a new record
    
    // Add a suffix to the fehlercode to make it unique
    const timestamp = new Date().getTime().toString().slice(-4);
    duplicatedItem.fehlercode = `${duplicatedItem.fehlercode}-${timestamp}`;
    
    form.value = duplicatedItem;
    showModal.value = true;
};

const showJsonData = (item) => {
    selectedJsonItem.value = item;
    showJsonModal.value = true;
};

const closeJsonModal = () => {
    showJsonModal.value = false;
    selectedJsonItem.value = null;
};
</script> 