<template>
    <AppLayout title="Prüfkatalog Datenmigration">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="bg-indigo-600 p-8">
                    <h2 class="text-2xl font-bold text-white">Prüfkatalog Datenmigration</h2>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Strukturierte Grenzwerte</h3>
                        <p class="text-gray-700 mb-4">
                            Dieses Tool migriert die bestehenden Absolutwerte-Strings in das neue strukturierte Format. 
                            Die Migration verarbeitet alle Einträge und aktualisiert die folgenden Felder:
                        </p>
                        
                        <ul class="list-disc list-inside mb-4 ml-4 text-gray-700">
                            <li><strong>grenzwert_typ</strong>: UGW, OGW, ABSOLUTE, SPECIAL, TEXT</li>
                            <li><strong>grenzwert_richtung</strong>: +, -</li>
                            <li><strong>grenzwert_operator</strong>: &gt;, &lt;, =, &gt;=, &lt;=</li>
                            <li><strong>grenzwert_wert</strong>: Numerischer Wert</li>
                            <li><strong>grenzwert_einheit</strong>: %, usw.</li>
                            <li><strong>grenzwerte_json</strong>: Vollständige JSON-Struktur</li>
                        </ul>
                        
                        <div class="bg-amber-50 border border-amber-300 p-4 rounded-md mb-6">
                            <div class="flex items-start">
                                <div class="shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-amber-800">Hinweis</h3>
                                    <div class="mt-2 text-sm text-amber-700">
                                        <p>Die Migration behält die originalen <strong>absolutwerte</strong>-Felder bei. 
                                            Das System wird weiterhin auch mit dem alten Format funktionieren.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button
                            @click="startMigration"
                            :disabled="processing"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <svg v-if="processing" class="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ processing ? 'Migration läuft...' : 'Datenmigration starten' }}
                        </button>
                    </div>
                    
                    <!-- Ergebnisse -->
                    <div v-if="result" class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Migrationsergebnisse</h3>
                        
                        <div class="bg-green-50 border border-green-300 p-4 rounded-md mb-6">
                            <div class="flex">
                                <div class="shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-800">Migration abgeschlossen</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white border border-gray-300 rounded-md shadow-sm overflow-hidden">
                            <div class="px-4 py-5 sm:p-6">
                                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Gesamtzahl Einträge</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ result.total }}</dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Erfolgreich</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ result.success }}</dd>
                                    </div>
                                    <div class="sm:col-span-1">
                                        <dt class="text-sm font-medium text-gray-500">Fehlgeschlagen</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ result.failed }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        
                        <div v-if="result.stats && result.stats.types" class="mt-6">
                            <h4 class="text-md font-medium text-gray-900 mb-2">Erkannte Muster</h4>
                            
                            <div class="bg-white border border-gray-300 rounded-md shadow-sm overflow-hidden">
                                <ul class="divide-y divide-gray-200">
                                    <li v-for="(count, type) in result.stats.types" :key="type" class="px-4 py-4 flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-900">{{ type }}</span>
                                        <span class="text-sm text-gray-500">{{ count }} Einträge</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="flex space-x-4">
                            <Link :href="route('pruefkatalog.index')" class="text-indigo-600 hover:text-indigo-900">
                                Zurück zum Prüfkatalog
                            </Link>
                            <Link :href="route('pruefkatalog.show-fix-json')" class="text-indigo-600 hover:text-indigo-900">
                                JSON-Daten korrigieren
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup>
import { ref } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';
import axios from 'axios';
import Swal from 'sweetalert2';

const processing = ref(false);
const result = ref(null);

const startMigration = () => {
    processing.value = true;
    result.value = null;
    
    axios.post(route('pruefkatalog.migrate-data'))
        .then(response => {
            processing.value = false;
            result.value = response.data.result;
            
            Swal.fire({
                icon: 'success',
                title: 'Migration abgeschlossen',
                text: `${response.data.result.success} von ${response.data.result.total} Einträgen erfolgreich migriert.`,
            });
        })
        .catch(error => {
            processing.value = false;
            
            Swal.fire({
                icon: 'error',
                title: 'Fehler',
                text: 'Bei der Migration ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut oder kontaktieren Sie den Administrator.',
            });
            
            console.error('Migrationsfehler:', error);
        });
};
</script> 