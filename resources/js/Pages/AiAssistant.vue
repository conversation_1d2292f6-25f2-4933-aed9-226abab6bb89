<template>
    <Head title="KI-Assistent" />
    <AppLayout>
        <template #header>
            <div class="w-full bg-gradient-to-r from-blue-600 to-indigo-800 -mt-4 -mx-4 py-6 px-4">
                <div class="max-w-7xl mx-auto">
                    <h2 class="font-bold text-2xl text-white leading-tight flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-3" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                        </svg>
                        KI-Assistent für Gussanalyse
                    </h2>
                    <p class="text-blue-100 mt-2 max-w-3xl text-sm">
                        Intelligente Analyse und Optimierung Ihrer Metall-Toleranzwerte
                    </p>
                </div>
            </div>
        </template>

        <div class="py-8 bg-gradient-to-b from-blue-50 to-gray-50">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- Main content - Input form -->
                    <div class="lg:col-span-3">
                        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg border border-blue-100">
                            <div class="p-4 sm:p-6 md:p-8">
                                <p class="text-gray-600 mb-6 max-w-3xl">
                                    Der KI-Assistent analysiert Ihre Metalldaten und liefert präzise Empfehlungen 
                                    für Prüfverfahren basierend auf den Toleranzabweichungen.
                                </p>
                                
                                <div class="mb-8">
                                    <form @submit.prevent="analyzeData">
                                        <!-- Eisenmarke -->
                                        <div class="mb-6">
                                            <label for="eisenmarke" class="block text-sm font-medium text-indigo-700 mb-2">
                                                Eisenmarke
                                            </label>
                                            <div class="relative rounded-md shadow-sm w-full md:w-1/3">
                                                <input 
                                                    id="eisenmarke" 
                                                    v-model="eisenmarke" 
                                                    type="text" 
                                                    class="form-input block w-full border-indigo-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                                    placeholder="z.B. 811"
                                                />
                                            </div>
                                        </div>
                                        
                                        <!-- Element-Tabelle -->
                                        <div class="mb-8">
                                            <div class="flex justify-between items-center mb-3">
                                                <label class="block text-sm font-medium text-indigo-700">Analysewerte</label>
                                                <div>
                                                    <button 
                                                        type="button" 
                                                        @click="addElement"
                                                        class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                        </svg>
                                                        Element hinzufügen
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="overflow-x-auto bg-white rounded-lg border border-indigo-200 shadow-md">
                                                <table class="min-w-full divide-y divide-indigo-100">
                                                    <thead>
                                                        <tr class="bg-gradient-to-r from-indigo-50 to-blue-50">
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-1/5">ELEMENT</th>
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-1/5">IST-WERT</th>
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-1/5">SOLL-MIN</th>
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-1/5">SOLL-MAX</th>
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-1/6">TOLERANZ</th>
                                                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-indigo-800 uppercase tracking-wider w-12"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="bg-white divide-y divide-indigo-50">
                                                        <tr v-for="(element, index) in elements" :key="index" class="hover:bg-indigo-50 transition-colors duration-150">
                                                            <td class="px-4 py-3">
                                                                <input 
                                                                    v-model="element.name" 
                                                                    type="text" 
                                                                    class="form-input block w-full border-indigo-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                                                    placeholder="z.B. Mg"
                                                                />
                                                            </td>
                                                            <td class="px-4 py-3">
                                                                <input 
                                                                    v-model="element.istWert" 
                                                                    type="text" 
                                                                    class="form-input block w-full border-indigo-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                                                    placeholder="z.B. 0,068"
                                                                    @input="calculateTolerance(index)"
                                                                />
                                                            </td>
                                                            <td class="px-4 py-3">
                                                                <input 
                                                                    v-model="element.sollWertMin" 
                                                                    type="text" 
                                                                    class="form-input block w-full border-indigo-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                                                    placeholder="z.B. 0,035"
                                                                    @input="calculateTolerance(index)"
                                                                />
                                                            </td>
                                                            <td class="px-4 py-3">
                                                                <input 
                                                                    v-model="element.sollWertMax" 
                                                                    type="text" 
                                                                    class="form-input block w-full border-indigo-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                                                    placeholder="z.B. 0,06"
                                                                    @input="calculateTolerance(index)"
                                                                />
                                                            </td>
                                                            <td class="px-4 py-3 relative">
                                                                <div class="flex items-center">
                                                                    <div v-if="element.toleranz" class="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                                                        <div 
                                                                            class="h-full transition-all duration-300"
                                                                            :class="{
                                                                                'bg-gradient-to-r from-green-400 to-green-500': element.toleranz === 'OK',
                                                                                'bg-gradient-to-r from-red-400 to-red-500': element.toleranz && element.toleranz.startsWith('+'),
                                                                                'bg-gradient-to-r from-yellow-400 to-yellow-500': element.toleranz && element.toleranz.startsWith('-')
                                                                            }"
                                                                            :style="getToleranzWidth(element.toleranz)"
                                                                        ></div>
                                                                    </div>
                                                                    <span 
                                                                        class="ml-2 inline-flex px-2.5 py-1 text-xs font-semibold rounded"
                                                                        :class="{
                                                                            'bg-green-100 text-green-800 border border-green-200': element.toleranz === 'OK',
                                                                            'bg-yellow-100 text-yellow-800 border border-yellow-200': element.toleranz && element.toleranz.startsWith('-'),
                                                                            'bg-red-100 text-red-800 border border-red-200': element.toleranz && element.toleranz.startsWith('+'),
                                                                            'bg-gray-100 text-gray-800 border border-gray-200': !element.toleranz
                                                                        }"
                                                                    >
                                                                        {{ element.toleranz || '-' }}
                                                                    </span>
                                                                </div>
                                                            </td>
                                                            <td class="px-4 py-3 text-right">
                                                                <button 
                                                                    type="button" 
                                                                    @click="removeElement(index)"
                                                                    class="inline-flex items-center text-red-600 hover:text-red-800 transition-colors duration-200"
                                                                    :disabled="elements.length === 1"
                                                                    v-if="elements.length > 1"
                                                                >
                                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                                    </svg>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <div class="flex justify-end">
                                            <button 
                                                type="submit" 
                                                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-md text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                :disabled="isLoading || !isFormValid"
                                            >
                                                <span v-if="!isLoading">Analysieren</span>
                                                <span v-else class="flex items-center">
                                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Analysiere...
                                                </span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                
                                <div v-if="aiResponse || error" class="mt-8 border-t border-indigo-100 pt-8">
                                    <div class="flex items-center mb-4">
                                        <div class="h-10 w-1.5 bg-gradient-to-b from-indigo-500 to-blue-600 rounded-full mr-3"></div>
                                        <h3 class="text-xl font-semibold text-indigo-900">Analyse & Empfehlungen</h3>
                                    </div>
                                    
                                    <div v-if="error" class="bg-red-50 border-l-4 border-red-500 p-4 rounded-r-md">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-red-800">{{ error }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div v-else-if="aiResponse" class="bg-white border border-indigo-200 rounded-lg shadow-lg overflow-hidden">
                                        <div class="p-6 bg-gradient-to-br from-white to-indigo-50/30">
                                            <div class="prose max-w-none" v-html="formattedResponse"></div>
                                        </div>
                                        
                                        <div v-if="metadata" class="bg-indigo-50 px-6 py-3 flex justify-between items-center text-sm text-indigo-700 border-t border-indigo-100">
                                            <div>
                                                <span v-if="metadata.source === 'database'" class="flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    Gespeicherte Antwort vom {{ formatDate(metadata.created_at) }}
                                                    <!-- <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200">
                                                        {{ metadata.usage_count }}x verwendet
                                                    </span> -->
                                                </span>
                                                <span v-else-if="metadata.source === 'cache'" class="flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                    </svg>
                                                    Zwischengespeicherte Antwort
                                                </span>
                                                <span v-else class="flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                    </svg>  
                                                    Neue Antwort vom {{ formatDate(metadata.generated_at) }}
                                                </span>
                                            </div>
                                            <button 
                                                v-if="metadata.can_refresh" 
                                                @click="refreshResponse" 
                                                class="inline-flex items-center px-3 py-1.5 border border-indigo-300 text-sm leading-4 font-medium rounded-md text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                                                :disabled="isLoading"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                                Neue Antwort generieren
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- History sidebar -->
                    <div class="lg:col-span-1">
                        <AiResponseHistory @response-selected="loadHistoricalResponse" />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import axios from 'axios';
import { ref, computed } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import AiResponseHistory from '@/Components/AiResponseHistory.vue';

export default {
    components: {
        AppLayout,
        Head,
        AiResponseHistory
    },
    
    setup() {
        const eisenmarke = ref('');
        const elements = ref([
            { name: '', istWert: '', sollWertMin: '', sollWertMax: '', toleranz: '' }
        ]);
        const aiResponse = ref('');
        const metadata = ref(null);
        const isLoading = ref(false);
        const error = ref('');
        
        const isFormValid = computed(() => {
            if (!eisenmarke.value.trim()) return false;
            
            // Prüfe, ob mindestens ein Element vollständig ausgefüllt ist
            return elements.value.some(element => 
                element.name.trim() && 
                element.istWert.trim() && 
                (element.sollWertMin.trim() || element.sollWertMax.trim())
            );
        });
        
        const formattedResponse = computed(() => {
            if (!aiResponse.value) return '';
            const html = marked(aiResponse.value);
            return DOMPurify.sanitize(html);
        });
        
        // Berechnet die Breite für die Toleranzanzeige
        const getToleranzWidth = (toleranz) => {
            if (!toleranz) return 'width: 0%';
            if (toleranz === 'OK') return 'width: 100%';
            
            // Bei Abweichungen eine prozentuale Breite berechnen
            let percentage = 0;
            if (toleranz.startsWith('+')) {
                // Positive Abweichung (rot) - je größer, desto breiter
                const value = parseFloat(toleranz.substring(1).replace(',', '.'));
                percentage = Math.min(100, value * 100); // Max 100%
            } else if (toleranz.startsWith('-')) {
                // Negative Abweichung (gelb) - je größer, desto breiter
                const value = parseFloat(toleranz.substring(1).replace(',', '.'));
                percentage = Math.min(100, value * 100); // Max 100%
            }
            
            return `width: ${percentage}%`;
        };
        
        // Berechnet die Toleranz für ein Element
        const calculateTolerance = (index) => {
            const element = elements.value[index];
            
            // Prüfen, ob die notwendigen Werte vorhanden sind
            if (!element.istWert || (!element.sollWertMin && !element.sollWertMax)) {
                element.toleranz = '';
                return;
            }
            
            // Konvertiere Komma zu Punkt für Berechnungen
            const istWert = parseFloat(element.istWert.replace(',', '.'));
            
            // Wenn IST-Wert keine Zahl ist, abbrechen
            if (isNaN(istWert)) {
                element.toleranz = '';
                return;
            }
            
            // Prüfen, ob ein Bereich oder ein einzelner Wert angegeben wurde
            if (element.sollWertMax) {
                const sollWertMax = parseFloat(element.sollWertMax.replace(',', '.'));
                
                if (!isNaN(sollWertMax)) {
                    // Berechne Toleranz, wenn IST-Wert über MAX liegt
                    if (istWert > sollWertMax) {
                        const diff = (istWert - sollWertMax).toFixed(3);
                        element.toleranz = `+${diff.replace('.', ',')}`;
                    } 
                    // Wenn MIN vorhanden ist, prüfe auch, ob IST-Wert unter MIN liegt
                    else if (element.sollWertMin) {
                        const sollWertMin = parseFloat(element.sollWertMin.replace(',', '.'));
                        if (!isNaN(sollWertMin) && istWert < sollWertMin) {
                            const diff = (sollWertMin - istWert).toFixed(3);
                            element.toleranz = `-${diff.replace('.', ',')}`;
                        } else {
                            element.toleranz = 'OK';
                        }
                    } else {
                        element.toleranz = 'OK';
                    }
                }
            } else if (element.sollWertMin) {
                const sollWertMin = parseFloat(element.sollWertMin.replace(',', '.'));
                
                if (!isNaN(sollWertMin)) {
                    // Berechne Toleranz, wenn IST-Wert unter MIN liegt
                    if (istWert < sollWertMin) {
                        const diff = (sollWertMin - istWert).toFixed(3);
                        element.toleranz = `-${diff.replace('.', ',')}`;
                    } else {
                        element.toleranz = 'OK';
                    }
                }
            }
        };
        
        // Element hinzufügen
        const addElement = () => {
            elements.value.push({ name: '', istWert: '', sollWertMin: '', sollWertMax: '', toleranz: '' });
        };
        
        // Element entfernen
        const removeElement = (index) => {
            if (elements.value.length > 1) {
                elements.value.splice(index, 1);
            }
        };
        
        // Prompt generieren
        const generatePrompt = () => {
            let prompt = `Eisenmarke: ${eisenmarke.value}\n\nAnalysewerte:\n`;
            
            elements.value.forEach(element => {
                if (element.name && element.istWert && (element.sollWertMin || element.sollWertMax)) {
                    const sollWert = element.sollWertMin && element.sollWertMax 
                        ? `${element.sollWertMin} - ${element.sollWertMax}`
                        : element.sollWertMin || element.sollWertMax;
                        
                    prompt += `${element.name}: Ist-Wert ${element.istWert}, Soll-Wert ${sollWert}\n`;
                }
            });
            
            return prompt;
        };
        
        const analyzeData = async () => {
            if (!isFormValid.value) {
                alert('Bitte füllen Sie mindestens die Eisenmarke und ein Element mit IST- und SOLL-Werten aus.');
                return;
            }
            
            isLoading.value = true;
            error.value = '';
            
            try {
                const prompt = generatePrompt();
                const response = await axios.post('/api/ai-assistant/query', {
                    prompt,
                    forceNew: false
                });
                
                aiResponse.value = response.data.response;
                metadata.value = response.data.metadata || {};
            } catch (err) {
                console.error('Error fetching AI response:', err);
                
                if (err.response && err.response.data) {
                    if (err.response.data.format_error) {
                        error.value = `Datenformat-Fehler: ${err.response.data.error}`;
                    } else if (err.response.data.api_error) {
                        error.value = 'Die KI-API ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.';
                    } else {
                        error.value = err.response.data.error || 'Ein unbekannter Fehler ist aufgetreten.';
                    }
                } else {
                    error.value = 'Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.';
                }
            } finally {
                isLoading.value = false;
            }
        };
        
        const refreshResponse = async () => {
            isLoading.value = true;
            error.value = '';
            
            try {
                const prompt = generatePrompt();
                const response = await axios.post('/api/ai-assistant/query', {
                    prompt,
                    forceNew: true
                });
                
                aiResponse.value = response.data.response;
                metadata.value = response.data.metadata || {};
            } catch (err) {
                console.error('Error refreshing AI response:', err);
                error.value = err.response?.data?.error || 'Fehler beim Aktualisieren der KI-Antwort';
            } finally {
                isLoading.value = false;
            }
        };
        
        const formatDate = (dateString) => {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('de-DE', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        };
        
        // For loading responses from history
        const loadHistoricalResponse = async (entry) => {
            isLoading.value = true;
            error.value = '';
            
            try {
                const response = await axios.get(`/api/ai-responses/${entry.id}`);
                
                aiResponse.value = response.data.response;
                metadata.value = response.data.metadata || {};
                
                // Scroll to the response section
                setTimeout(() => {
                    const responseSection = document.querySelector('.mt-8.border-t.border-indigo-100');
                    if (responseSection) {
                        responseSection.scrollIntoView({ behavior: 'smooth' });
                    }
                }, 100);
            } catch (err) {
                console.error('Error loading historical response:', err);
                error.value = err.response?.data?.error || 'Fehler beim Laden der historischen Antwort';
            } finally {
                isLoading.value = false;
            }
        };
        
        return {
            eisenmarke,
            elements,
            aiResponse,
            metadata,
            isLoading,
            error,
            isFormValid,
            formattedResponse,
            calculateTolerance,
            getToleranzWidth,
            addElement,
            removeElement,
            analyzeData,
            refreshResponse,
            formatDate,
            loadHistoricalResponse
        };
    }
};
</script>

<style>
/* Modernere Eingabefelder */
input.form-input {
    height: 42px;
    padding: 10px 12px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

/* Fokuszustände */
.form-input:focus {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Input Placeholder Styling */
.form-input::placeholder {
    opacity: 0.7;
    text-overflow: ellipsis;
}

/* Tabellenbreite anpassen */
.min-w-full {
    min-width: 100%;
    table-layout: fixed;
}

/* Zellbreiten in der Tabelle */
th, td {
    word-break: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Modernes Tabellendesign */
table {
    border-spacing: 0;
    width: 100%;
}

th {
    padding: 12px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

td {
    padding: 12px 16px;
}

/* Industriestandard-Übergänge */
.transition-colors {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

/* Markdown-Styling für die KI-Antwort */
.prose h1, .prose h2, .prose h3 {
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    color: #4338ca;
}

.prose h1 {
    font-size: 1.5rem;
    border-bottom: 1px solid #e0e7ff;
    padding-bottom: 0.5rem;
}

.prose h2 {
    font-size: 1.25rem;
}

.prose h3 {
    font-size: 1.1rem;
}

.prose ul, .prose ol {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
    padding-left: 1.5em;
}

.prose ul li, .prose ol li {
    margin-top: 0.375em;
    margin-bottom: 0.375em;
}

.prose strong {
    font-weight: 600;
    color: #4338ca;
}

.prose p {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
}

.prose table {
    border: 1px solid #e0e7ff;
    border-collapse: collapse;
    margin: 1em 0;
}

.prose th {
    background-color: #eef2ff;
    border: 1px solid #e0e7ff;
    padding: 0.5em;
    color: #4338ca;
}

.prose td {
    border: 1px solid #e0e7ff;
    padding: 0.5em;
}

/* Hintergrundfarbe */
.bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
</style> 