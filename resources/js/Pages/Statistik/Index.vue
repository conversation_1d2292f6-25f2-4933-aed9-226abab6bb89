<script setup>
import { ref, computed, onMounted } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link, router } from '@inertiajs/vue3';
import { Chart, registerables } from 'chart.js';
import axios from 'axios';
import Swal from 'sweetalert2';

// Chart.js registrieren
Chart.register(...registerables);

const props = defineProps({
    statistiken: {
        type: Object,
        required: true
    },
    filter: {
        type: Object,
        required: true
    }
});

// Reactive refs für Charts
const statusChartRef = ref(null);
const abteilungsChartRef = ref(null);
const trendChartRef = ref(null);
const durchlaufzeitChartRef = ref(null);

// Filter-Daten
const startDate = ref(props.filter.start_date);
const endDate = ref(props.filter.end_date);

// Paginierung für Durchlaufzeiten
const currentDurchlaufzeitPage = ref(1);

// Sortierung für Durchlaufzeiten
const sortField = ref(props.filter.sort_field || 'durchlaufzeit_stunden');
const sortDirection = ref(props.filter.sort_direction || 'desc');

// Export und E-Mail
const showEmailModal = ref(false);
const emailForm = ref({
    email: '',
    include_pdf: true,
    loading: false
});
const emailVerteiler = ref([]);
const exportLoading = ref(false);

// Computed properties für bessere Datenaufbereitung
const gesamtStatistiken = computed(() => props.statistiken.gesamt);
const statusVerteilung = computed(() => props.statistiken.status);
const abteilungsAnalyse = computed(() => props.statistiken.abteilungen);
const zeitTrends = computed(() => props.statistiken.trends);
const qualitaetsMetriken = computed(() => props.statistiken.qualitaet);
const durchlaufzeiten = computed(() => props.statistiken.durchlaufzeiten);
const problembereiche = computed(() => props.statistiken.problembereiche);
const analyseabweichungen = computed(() => props.statistiken.analyseabweichungen);
const eisenmarkenAnalyse = computed(() => props.statistiken.eisenmarken);
const schichtAnalyse = computed(() => props.statistiken.schichten);
const mitarbeiterAnalyse = computed(() => props.statistiken.mitarbeiter);
const fehlercodeAnalyse = computed(() => props.statistiken.fehlercodes);

// Chart-Konfigurationen
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
        }
    }
};

// Filter anwenden
const applyFilter = () => {
    currentDurchlaufzeitPage.value = 1; // Reset pagination
    router.get('/statistik', {
        start_date: startDate.value,
        end_date: endDate.value,
        durchlaufzeit_page: currentDurchlaufzeitPage.value,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
    }, {
        preserveState: true,
        preserveScroll: true
    });
};

// Durchlaufzeit-Paginierung
const changeDurchlaufzeitPage = (page) => {
    currentDurchlaufzeitPage.value = page;
    router.get('/statistik', {
        start_date: startDate.value,
        end_date: endDate.value,
        durchlaufzeit_page: page,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
    }, {
        preserveState: true,
        preserveScroll: true
    });
};

// Sortierung ändern
const changeSorting = (field) => {
    if (sortField.value === field) {
        // Gleiche Spalte: Richtung umkehren
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        // Neue Spalte: Standard-Richtung setzen
        sortField.value = field;
        sortDirection.value = field === 'durchlaufzeit_stunden' ? 'desc' : 'asc';
    }

    // Seite zurücksetzen und neu laden
    currentDurchlaufzeitPage.value = 1;
    router.get('/statistik', {
        start_date: startDate.value,
        end_date: endDate.value,
        durchlaufzeit_page: 1,
        sort_field: sortField.value,
        sort_direction: sortDirection.value
    }, {
        preserveState: true,
        preserveScroll: true
    });
};

// Charts initialisieren
onMounted(() => {
    // Kleine Verzögerung, um sicherzustellen, dass die DOM-Elemente verfügbar sind
    setTimeout(() => {
        initializeCharts();
    }, 100);
});

const initializeCharts = () => {
    try {
        // Status-Verteilungs-Chart
        if (statusChartRef.value && statusVerteilung.value.length > 0) {
            new Chart(statusChartRef.value, {
            type: 'doughnut',
            data: {
                labels: statusVerteilung.value.map(item => item.status),
                datasets: [{
                    data: statusVerteilung.value.map(item => item.anzahl),
                    backgroundColor: [
                        '#10B981', // Grün für Abgeschlossen
                        '#F59E0B', // Gelb für In Bearbeitung
                        '#EF4444', // Rot für Offen
                        '#8B5CF6', // Lila für andere
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    title: {
                        display: true,
                        text: 'Status-Verteilung'
                    }
                }
            }
        });
    }

        // Abteilungs-Chart
        if (abteilungsChartRef.value && abteilungsAnalyse.value.length > 0) {
            new Chart(abteilungsChartRef.value, {
            type: 'bar',
            data: {
                labels: abteilungsAnalyse.value.map(item => item.abteilung),
                datasets: [{
                    label: 'Anzahl Rotekarten',
                    data: abteilungsAnalyse.value.map(item => item.anzahl),
                    backgroundColor: [
                        '#3B82F6', // Blau für NG
                        '#10B981', // Grün für GG
                        '#8B5CF6', // Lila für HF
                    ],
                    borderRadius: 8,
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    title: {
                        display: true,
                        text: 'Verteilung nach Abteilungen'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

        // Trend-Chart
        if (trendChartRef.value && zeitTrends.value.length > 0) {
            new Chart(trendChartRef.value, {
            type: 'line',
            data: {
                labels: zeitTrends.value.map(item => item.datum_formatted),
                datasets: [{
                    label: 'Neue Rotekarten',
                    data: zeitTrends.value.map(item => item.anzahl),
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    title: {
                        display: true,
                        text: 'Zeitlicher Verlauf'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

        // Durchlaufzeit-Chart
        if (durchlaufzeitChartRef.value && durchlaufzeiten.value.data && durchlaufzeiten.value.data.length > 0) {
            new Chart(durchlaufzeitChartRef.value, {
            type: 'bar',
            data: {
                labels: durchlaufzeiten.value.data.map(item => `RT-${item.rotekarte_id}`),
                datasets: [{
                    label: 'Durchlaufzeit (Tage)',
                    data: durchlaufzeiten.value.data.map(item => item.durchlaufzeit_tage),
                    backgroundColor: '#F59E0B',
                    borderRadius: 8,
                }]
            },
            options: {
                ...chartOptions,
                plugins: {
                    ...chartOptions.plugins,
                    title: {
                        display: true,
                        text: 'Top 10 Durchlaufzeiten'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Tage'
                        }
                    }
                }
            }
        });
        }
    } catch (error) {
        console.error('Fehler beim Initialisieren der Charts:', error);
    }
};

// Utility-Funktionen
const getStatusColor = (status) => {
    switch (status) {
        case 'Abgeschlossen': return 'bg-green-100 text-green-800';
        case 'In Bearbeitung': return 'bg-yellow-100 text-yellow-800';
        case 'Offen': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

const getAbteilungsColor = (code) => {
    switch (code) {
        case 'NG': return 'bg-blue-100 text-blue-800';
        case 'GG': return 'bg-green-100 text-green-800';
        case 'HF': return 'bg-purple-100 text-purple-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

// Sortier-Icon bestimmen
const getSortIcon = (field) => {
    if (sortField.value !== field) {
        return '↕️'; // Neutral
    }
    return sortDirection.value === 'asc' ? '↑' : '↓';
};

// Prüfen ob Spalte aktiv sortiert ist
const isSortActive = (field) => {
    return sortField.value === field;
};

// Export-Funktionen
const exportPdf = () => {
    exportLoading.value = true;

    const params = new URLSearchParams({
        start_date: startDate.value,
        end_date: endDate.value
    });

    window.open(`/statistik/export-pdf?${params.toString()}`, '_blank');

    setTimeout(() => {
        exportLoading.value = false;
    }, 2000);
};

const printPage = () => {
    window.print();
};

const openEmailModal = async () => {
    showEmailModal.value = true;
    await loadEmailVerteiler();
};

const loadEmailVerteiler = async () => {
    try {
        const response = await axios.get('/statistik/email-verteiler');
        emailVerteiler.value = response.data;
    } catch (error) {
        console.error('Fehler beim Laden der E-Mail-Verteiler:', error);
    }
};

const selectEmail = (email) => {
    emailForm.value.email = email;
};

const sendEmail = async () => {
    if (!emailForm.value.email) {
        Swal.fire({
            icon: 'warning',
            title: 'E-Mail-Adresse erforderlich',
            text: 'Bitte geben Sie eine E-Mail-Adresse ein.',
            showConfirmButton: true,
            confirmButtonText: 'Ok'
        });
        return;
    }

    emailForm.value.loading = true;

    try {
        const response = await axios.post('/statistik/send-email', {
            email: emailForm.value.email,
            start_date: startDate.value,
            end_date: endDate.value,
            include_pdf: emailForm.value.include_pdf
        });

        if (response.data.success) {
            showEmailModal.value = false;
            emailForm.value.email = '';
            Swal.fire({
                icon: 'success',
                title: 'Erfolgreich versendet',
                text: 'Statistik-Report wurde erfolgreich versendet!',
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Fehler beim Versenden',
                text: 'Fehler beim Versenden: ' + response.data.message,
                showConfirmButton: true,
                confirmButtonText: 'Ok'
            });
        }
    } catch (error) {
        console.error('Fehler beim Versenden der E-Mail:', error);
        Swal.fire({
            icon: 'error',
            title: 'Fehler beim Versenden',
            text: 'Fehler beim Versenden der E-Mail. Bitte versuchen Sie es erneut.',
            showConfirmButton: true,
            confirmButtonText: 'Ok'
        });
    } finally {
        emailForm.value.loading = false;
    }
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 flex items-center gap-3">
                            📊 Intelligente Statistiken
                        </h1>
                        <p class="text-gray-600 mt-2">Umfassende Analyse Ihrer Rotekarten-Daten</p>
                    </div>
                    
                    <!-- Filter und Export -->
                    <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4">
                        <!-- Filter -->
                        <div class="flex items-center gap-4">
                            <div class="flex items-center gap-2">
                                <label class="text-sm font-medium text-gray-700">Von:</label>
                                <input
                                    type="date"
                                    v-model="startDate"
                                    class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                            </div>
                            <div class="flex items-center gap-2">
                                <label class="text-sm font-medium text-gray-700">Bis:</label>
                                <input
                                    type="date"
                                    v-model="endDate"
                                    class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                            </div>
                            <button
                                @click="applyFilter"
                                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                            >
                                Filter anwenden
                            </button>
                        </div>

                        <!-- Export-Buttons -->
                        <div class="flex items-center gap-2 border-l border-gray-300 pl-4">
                            <button
                                @click="printPage"
                                class="bg-gray-600 text-white px-3 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200 text-sm font-medium flex items-center gap-2"
                                title="Seite drucken"
                            >
                                🖨️ Drucken
                            </button>
                            <button
                                @click="exportPdf"
                                :disabled="exportLoading"
                                class="bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 transition-colors duration-200 text-sm font-medium flex items-center gap-2 disabled:opacity-50"
                                title="Als PDF exportieren"
                            >
                                <span v-if="exportLoading">⏳</span>
                                <span v-else>📄</span>
                                PDF Export
                            </button>
                            <button
                                @click="openEmailModal"
                                class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 transition-colors duration-200 text-sm font-medium flex items-center gap-2"
                                title="Per E-Mail versenden"
                            >
                                📧 E-Mail
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gesamtstatistiken -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Gesamt</p>
                            <p class="text-3xl font-bold text-gray-900">{{ gesamtStatistiken.total }}</p>
                        </div>
                        <div class="text-blue-500 text-2xl">📋</div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Heute</p>
                            <p class="text-3xl font-bold text-gray-900">{{ gesamtStatistiken.heute }}</p>
                        </div>
                        <div class="text-green-500 text-2xl">📅</div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-yellow-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Diese Woche</p>
                            <p class="text-3xl font-bold text-gray-900">{{ gesamtStatistiken.diese_woche }}</p>
                        </div>
                        <div class="text-yellow-500 text-2xl">📊</div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Dieser Monat</p>
                            <p class="text-3xl font-bold text-gray-900">{{ gesamtStatistiken.dieser_monat }}</p>
                        </div>
                        <div class="text-purple-500 text-2xl">📈</div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-indigo-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Ø pro Tag</p>
                            <p class="text-3xl font-bold text-gray-900">{{ gesamtStatistiken.durchschnitt_pro_tag }}</p>
                        </div>
                        <div class="text-indigo-500 text-2xl">⚡</div>
                    </div>
                </div>
            </div>

            <!-- Charts Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Status-Verteilung -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Status-Verteilung</h3>
                    <div class="h-64">
                        <canvas ref="statusChartRef"></canvas>
                    </div>
                </div>

                <!-- Abteilungsanalyse -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Abteilungsanalyse</h3>
                    <div class="h-64">
                        <canvas ref="abteilungsChartRef"></canvas>
                    </div>
                </div>
            </div>

            <!-- Zeitlicher Trend -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Zeitlicher Verlauf</h3>
                <div class="h-80">
                    <canvas ref="trendChartRef"></canvas>
                </div>
            </div>

            <!-- Qualitätsmetriken und Durchlaufzeiten -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Qualitätsmetriken -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Qualitätsmetriken</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-green-800">Abschlussrate</p>
                                <p class="text-2xl font-bold text-green-900">{{ qualitaetsMetriken.abschluss_rate }}%</p>
                            </div>
                            <div class="text-green-500 text-3xl">✅</div>
                        </div>

                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center p-3 bg-gray-50 rounded-lg">
                                <p class="text-sm text-gray-600">Abgeschlossen</p>
                                <p class="text-xl font-bold text-gray-900">{{ qualitaetsMetriken.abgeschlossen }}</p>
                            </div>
                            <div class="text-center p-3 bg-yellow-50 rounded-lg">
                                <p class="text-sm text-gray-600">In Bearbeitung</p>
                                <p class="text-xl font-bold text-gray-900">{{ qualitaetsMetriken.in_bearbeitung }}</p>
                            </div>
                            <div class="text-center p-3 bg-red-50 rounded-lg">
                                <p class="text-sm text-gray-600">Offen</p>
                                <p class="text-xl font-bold text-gray-900">{{ qualitaetsMetriken.offen }}</p>
                            </div>
                        </div>

                        <div class="p-4 bg-blue-50 rounded-lg">
                            <p class="text-sm font-medium text-blue-800">Ø Bearbeitungszeit</p>
                            <p class="text-2xl font-bold text-blue-900">{{ qualitaetsMetriken.avg_bearbeitungszeit_stunden }}h</p>
                        </div>
                    </div>
                </div>

                <!-- Durchlaufzeiten -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        Aktuelle Durchlaufzeiten
                        <span class="text-sm text-gray-500 font-normal">
                            ({{ durchlaufzeiten.data ? durchlaufzeiten.data.length : 0 }} von {{ durchlaufzeiten.total || 0 }})
                        </span>
                    </h3>
                    <div class="h-64" v-if="durchlaufzeiten.data && durchlaufzeiten.data.length > 0">
                        <canvas ref="durchlaufzeitChartRef"></canvas>
                    </div>
                    <div v-else class="h-64 flex items-center justify-center text-gray-500">
                        <div class="text-center">
                            <div class="text-4xl mb-2">📊</div>
                            <p>Keine abgeschlossenen Rotekarten im Zeitraum</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detaillierte Listen -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Status-Details -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Status-Details</h3>
                    <div class="space-y-3">
                        <div
                            v-for="status in statusVerteilung"
                            :key="status.status"
                            class="flex justify-between items-center p-3 rounded-lg border"
                        >
                            <div class="flex items-center gap-3">
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium"
                                    :class="getStatusColor(status.status)"
                                >
                                    {{ status.status }}
                                </span>
                            </div>
                            <span class="text-lg font-bold text-gray-900">{{ status.anzahl }}</span>
                        </div>
                    </div>
                </div>

                <!-- Abteilungs-Details -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Abteilungs-Details</h3>
                    <div class="space-y-3">
                        <div
                            v-for="abteilung in abteilungsAnalyse"
                            :key="abteilung.code"
                            class="flex justify-between items-center p-3 rounded-lg border"
                        >
                            <div class="flex items-center gap-3">
                                <span
                                    class="px-3 py-1 rounded-full text-sm font-medium"
                                    :class="getAbteilungsColor(abteilung.code)"
                                >
                                    {{ abteilung.abteilung }}
                                </span>
                            </div>
                            <span class="text-lg font-bold text-gray-900">{{ abteilung.anzahl }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Problembereiche -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-8" v-if="problembereiche.length > 0">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🚨 Problembereiche</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div
                        v-for="problem in problembereiche"
                        :key="problem.code"
                        class="p-4 bg-red-50 border border-red-200 rounded-lg"
                    >
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-medium text-red-900">{{ problem.bereich }}</p>
                                <p class="text-sm text-red-700">Offene Probleme</p>
                            </div>
                            <span class="text-2xl font-bold text-red-600">{{ problem.anzahl_probleme }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Durchlaufzeit-Details -->
            <div class="bg-white rounded-lg shadow-sm p-6" v-if="durchlaufzeiten.data && durchlaufzeiten.data.length > 0">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Durchlaufzeit-Details
                        <span class="text-sm text-gray-500 font-normal">
                            ({{ durchlaufzeiten.from }}-{{ durchlaufzeiten.to }} von {{ durchlaufzeiten.total }})
                        </span>
                    </h3>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    @click="changeSorting('rotekarte_id')"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                                    :class="{ 'bg-gray-200': isSortActive('rotekarte_id') }"
                                >
                                    <div class="flex items-center gap-2">
                                        <span>Rotekarte</span>
                                        <span class="text-sm">{{ getSortIcon('rotekarte_id') }}</span>
                                    </div>
                                </th>
                                <th
                                    @click="changeSorting('created_at')"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                                    :class="{ 'bg-gray-200': isSortActive('created_at') }"
                                >
                                    <div class="flex items-center gap-2">
                                        <span>Erstellt am</span>
                                        <span class="text-sm">{{ getSortIcon('created_at') }}</span>
                                    </div>
                                </th>
                                <th
                                    @click="changeSorting('updated_at')"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                                    :class="{ 'bg-gray-200': isSortActive('updated_at') }"
                                >
                                    <div class="flex items-center gap-2">
                                        <span>Abgeschlossen am</span>
                                        <span class="text-sm">{{ getSortIcon('updated_at') }}</span>
                                    </div>
                                </th>
                                <th
                                    @click="changeSorting('durchlaufzeit_stunden')"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                                    :class="{ 'bg-gray-200': isSortActive('durchlaufzeit_stunden') }"
                                >
                                    <div class="flex items-center gap-2">
                                        <span>Durchlaufzeit</span>
                                        <span class="text-sm">{{ getSortIcon('durchlaufzeit_stunden') }}</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="item in durchlaufzeiten.data" :key="item.rotekarte_id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                                        RT-{{ item.rotekarte_id }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ item.erstellt_am }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ item.abgeschlossen_am }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium">{{ item.durchlaufzeit_tage }} Tage</span>
                                        <span class="text-gray-500">({{ item.durchlaufzeit_stunden }}h)</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Paginierung -->
                <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4" v-if="durchlaufzeiten.last_page > 1">
                    <div class="flex flex-1 justify-between sm:hidden">
                        <button
                            @click="changeDurchlaufzeitPage(durchlaufzeiten.current_page - 1)"
                            :disabled="durchlaufzeiten.current_page <= 1"
                            class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Zurück
                        </button>
                        <button
                            @click="changeDurchlaufzeitPage(durchlaufzeiten.current_page + 1)"
                            :disabled="durchlaufzeiten.current_page >= durchlaufzeiten.last_page"
                            class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Weiter
                        </button>
                    </div>
                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Zeige <span class="font-medium">{{ durchlaufzeiten.from }}</span> bis
                                <span class="font-medium">{{ durchlaufzeiten.to }}</span> von
                                <span class="font-medium">{{ durchlaufzeiten.total }}</span> Ergebnissen
                            </p>
                        </div>
                        <div>
                            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                <!-- Zurück Button -->
                                <button
                                    @click="changeDurchlaufzeitPage(durchlaufzeiten.current_page - 1)"
                                    :disabled="durchlaufzeiten.current_page <= 1"
                                    class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span class="sr-only">Zurück</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>

                                <!-- Seitenzahlen -->
                                <template v-for="page in Math.min(durchlaufzeiten.last_page, 7)" :key="page">
                                    <button
                                        v-if="page === 1 || page === durchlaufzeiten.last_page || (page >= durchlaufzeiten.current_page - 2 && page <= durchlaufzeiten.current_page + 2)"
                                        @click="changeDurchlaufzeitPage(page)"
                                        :class="[
                                            page === durchlaufzeiten.current_page
                                                ? 'relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                                                : 'relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                                        ]"
                                    >
                                        {{ page }}
                                    </button>
                                </template>

                                <!-- Weiter Button -->
                                <button
                                    @click="changeDurchlaufzeitPage(durchlaufzeiten.current_page + 1)"
                                    :disabled="durchlaufzeiten.current_page >= durchlaufzeiten.last_page"
                                    class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span class="sr-only">Weiter</span>
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Erweiterte Analysen -->
            <div class="mt-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-8 flex items-center gap-3">
                    🔬 Erweiterte Analysen
                    <span class="text-sm font-normal text-gray-500">Detaillierte Ursachenanalyse</span>
                </h2>

                <!-- Analyseabweichungen und Eisenmarken -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Analyseabweichungen -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">🧪 Häufigste Analyseabweichungen</h3>
                        <div class="space-y-4" v-if="analyseabweichungen.length > 0">
                            <div
                                v-for="abweichung in analyseabweichungen.slice(0, 10)"
                                :key="abweichung.element"
                                class="flex justify-between items-center p-4 bg-red-50 border border-red-200 rounded-lg"
                            >
                                <div>
                                    <p class="font-medium text-red-900">{{ abweichung.element }}</p>
                                    <p class="text-sm text-red-700">
                                        Ø {{ abweichung.durchschnitt_abweichung }}% Abweichung
                                        (Max: {{ abweichung.max_abweichung.toFixed(1) }}%)
                                    </p>
                                </div>
                                <span class="text-2xl font-bold text-red-600">{{ abweichung.anzahl_abweichungen }}</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-2">✅</div>
                            <p>Keine signifikanten Analyseabweichungen gefunden</p>
                        </div>
                    </div>

                    <!-- Eisenmarken-Analyse -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">⚙️ Eisenmarken-Analyse</h3>
                        <div class="space-y-4" v-if="eisenmarkenAnalyse.length > 0">
                            <div
                                v-for="eisenmarke in eisenmarkenAnalyse.slice(0, 10)"
                                :key="eisenmarke.eisenmarke"
                                class="flex justify-between items-center p-4 bg-orange-50 border border-orange-200 rounded-lg"
                            >
                                <div>
                                    <p class="font-medium text-orange-900">{{ eisenmarke.eisenmarke }}</p>
                                    <p class="text-sm text-orange-700">
                                        Letzte Rotekarte: {{ eisenmarke.letzte_rotekarte }}
                                    </p>
                                    <div class="flex gap-2 mt-1">
                                        <span
                                            v-for="(anzahl, abteilung) in eisenmarke.abteilungen"
                                            :key="abteilung"
                                            class="px-2 py-1 text-xs rounded-full bg-orange-200 text-orange-800"
                                        >
                                            {{ abteilung }}: {{ anzahl }}
                                        </span>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-orange-600">{{ eisenmarke.anzahl_rotekarten }}</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-2">⚙️</div>
                            <p>Keine Eisenmarken-Daten verfügbar</p>
                        </div>
                    </div>
                </div>

                <!-- Schicht- und Mitarbeiter-Analyse -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Schicht-Analyse -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">🕐 Schicht-Analyse</h3>
                        <div class="space-y-4" v-if="schichtAnalyse.length > 0">
                            <div
                                v-for="schicht in schichtAnalyse"
                                :key="schicht.schicht"
                                class="flex justify-between items-center p-4 bg-blue-50 border border-blue-200 rounded-lg"
                            >
                                <div>
                                    <p class="font-medium text-blue-900">{{ schicht.schicht }}</p>
                                    <div class="flex gap-2 mt-1">
                                        <span
                                            v-for="(anzahl, abteilung) in schicht.abteilungen"
                                            :key="abteilung"
                                            class="px-2 py-1 text-xs rounded-full bg-blue-200 text-blue-800"
                                        >
                                            {{ abteilung }}: {{ anzahl }}
                                        </span>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-blue-600">{{ schicht.anzahl_rotekarten }}</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-2">🕐</div>
                            <p>Keine Schichtdaten verfügbar</p>
                        </div>
                    </div>

                    <!-- Mitarbeiter-Analyse -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">👥 Mitarbeiter-Analyse</h3>
                        <div class="space-y-4" v-if="mitarbeiterAnalyse.length > 0">
                            <div
                                v-for="mitarbeiter in mitarbeiterAnalyse.slice(0, 10)"
                                :key="mitarbeiter.name"
                                class="flex justify-between items-center p-4 bg-purple-50 border border-purple-200 rounded-lg"
                            >
                                <div>
                                    <p class="font-medium text-purple-900">{{ mitarbeiter.name }}</p>
                                    <p class="text-sm text-purple-700">
                                        Letzte Aktivität: {{ mitarbeiter.letzte_aktivitaet }}
                                    </p>
                                    <div class="flex gap-2 mt-1">
                                        <span
                                            v-for="(anzahl, abteilung) in mitarbeiter.abteilungen"
                                            :key="abteilung"
                                            class="px-2 py-1 text-xs rounded-full bg-purple-200 text-purple-800"
                                        >
                                            {{ abteilung }}: {{ anzahl }}
                                        </span>
                                    </div>
                                </div>
                                <span class="text-2xl font-bold text-purple-600">{{ mitarbeiter.anzahl_rotekarten }}</span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-2">👥</div>
                            <p>Keine Mitarbeiterdaten verfügbar</p>
                        </div>
                    </div>
                </div>

                <!-- Fehlercode-Analyse -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">⚠️ Häufigste Fehlercodes</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" v-if="fehlercodeAnalyse.length > 0">
                        <div
                            v-for="fehlercode in fehlercodeAnalyse.slice(0, 12)"
                            :key="fehlercode.fehlercode"
                            class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg"
                        >
                            <div class="flex justify-between items-start mb-2">
                                <p class="font-medium text-yellow-900 text-sm">{{ fehlercode.fehlercode }}</p>
                                <span class="text-lg font-bold text-yellow-600">{{ fehlercode.anzahl_rotekarten }}</span>
                            </div>
                            <p class="text-xs text-yellow-700 mb-2">
                                Letzte: {{ fehlercode.letzte_rotekarte }}
                            </p>
                            <div class="flex flex-wrap gap-1">
                                <span
                                    v-for="(anzahl, abteilung) in fehlercode.abteilungen"
                                    :key="abteilung"
                                    class="px-2 py-1 text-xs rounded-full bg-yellow-200 text-yellow-800"
                                >
                                    {{ abteilung }}: {{ anzahl }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-8 text-gray-500">
                        <div class="text-4xl mb-2">✅</div>
                        <p>Keine Fehlercodes erfasst</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- E-Mail Modal -->
        <div v-if="showEmailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">📧 Statistik-Report per E-Mail versenden</h3>
                        <button
                            @click="showEmailModal = false"
                            class="text-gray-400 hover:text-gray-600 text-xl"
                        >
                            ×
                        </button>
                    </div>

                    <div class="space-y-4">
                        <!-- E-Mail-Eingabe -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">E-Mail-Adresse</label>
                            <input
                                type="email"
                                v-model="emailForm.email"
                                placeholder="<EMAIL>"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                        </div>

                        <!-- E-Mail-Verteiler -->
                        <div v-if="emailVerteiler.length > 0">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Oder aus Verteiler wählen:</label>
                            <div class="max-h-32 overflow-y-auto border border-gray-200 rounded-md">
                                <button
                                    v-for="verteiler in emailVerteiler"
                                    :key="verteiler.email"
                                    @click="selectEmail(verteiler.email)"
                                    class="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm border-b border-gray-100 last:border-b-0"
                                >
                                    <div class="font-medium">{{ verteiler.name }}</div>
                                    <div class="text-gray-500 text-xs">{{ verteiler.email }}</div>
                                </button>
                            </div>
                        </div>

                        <!-- PDF-Option -->
                        <div class="flex items-center">
                            <input
                                type="checkbox"
                                id="include_pdf"
                                v-model="emailForm.include_pdf"
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <label for="include_pdf" class="ml-2 block text-sm text-gray-700">
                                PDF-Report als Anhang hinzufügen
                            </label>
                        </div>

                        <!-- Zeitraum-Info -->
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                            <div class="text-sm text-blue-800">
                                <strong>Zeitraum:</strong> {{ startDate }} bis {{ endDate }}
                            </div>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="flex justify-end gap-3 mt-6">
                        <button
                            @click="showEmailModal = false"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors duration-200"
                        >
                            Abbrechen
                        </button>
                        <button
                            @click="sendEmail"
                            :disabled="emailForm.loading || !emailForm.email"
                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            <span v-if="emailForm.loading">⏳</span>
                            <span v-else>📧</span>
                            {{ emailForm.loading ? 'Wird versendet...' : 'E-Mail senden' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
@media print {
    /* Verstecke Navigation und nicht-druckbare Elemente */
    nav, .no-print, button, .fixed {
        display: none !important;
    }

    /* Optimiere für Druck */
    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .bg-white {
        background: white !important;
    }

    .shadow-sm, .shadow-xl {
        box-shadow: none !important;
    }

    /* Seitenumbrüche */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* Charts für Druck optimieren */
    canvas {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Tabellen für Druck optimieren */
    table {
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    /* Farben für Druck anpassen */
    .text-blue-600, .text-blue-800, .text-blue-900 {
        color: #000 !important;
    }

    .bg-blue-50, .bg-blue-100 {
        background: #f5f5f5 !important;
    }
}
</style>
