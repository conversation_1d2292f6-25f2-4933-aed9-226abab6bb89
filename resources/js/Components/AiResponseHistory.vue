<template>
    <div class="ai-history-container rounded-lg overflow-hidden">
        <div class="bg-white rounded-lg border border-gray-200 shadow-sm mb-0">
            <div class="p-4 border-b border-gray-200 bg-blue-50 rounded-t-lg">
                <div class="flex flex-col items-start">
                    <h3 class="text-lg font-semibold text-indigo-800 mb-3">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span class="text-xl">Anfrageverlauf</span>
                        </div>
                    </h3>
                    
                    <div class="relative w-full">
                        <input 
                            type="text" 
                            v-model="searchTerm" 
                            placeholder="Suchen..." 
                            class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-10 py-3 text-sm w-full"
                            @input="handleSearch"
                        />
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="overflow-hidden">
                <div v-if="loading" class="flex justify-center items-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-2 text-gray-600">Lade Verlauf...</span>
                </div>
                
                <div v-else-if="history.length === 0" class="py-8 text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-indigo-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-indigo-700 font-medium mb-1">Keine Einträge gefunden</p>
                    <p class="text-sm text-gray-500">Beginnen Sie mit einer neuen Analyse</p>
                </div>
                
                <ul v-else class="divide-y divide-gray-200">
                    <li v-for="entry in history" :key="entry.id" class="history-item transition-colors duration-150 cursor-pointer" @click="selectResponse(entry)">
                        <div class="p-5">
                            <div class="flex flex-col">
                                <div class="text-base font-medium text-gray-800 mb-3">
                                    {{ formatPrompt(entry.prompt) }}
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        {{ formatDate(entry.created_at) }}
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span v-if="entry.usage_count > 0" class="inline-flex items-center px-3 py-1 rounded-full text-xs bg-indigo-50 text-indigo-600">
                                            {{ entry.usage_count }}x verwendet
                                        </span>
                                        <button 
                                            @click.stop="deleteHistoryEntry(entry.id)" 
                                            class="delete-button p-1 rounded-full hover:bg-red-50 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-200"
                                            title="Eintrag löschen"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                
                <div v-if="history.length > 0 && totalPages > 1" class="border-t border-gray-200 px-4 py-3 flex items-center justify-between">
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Zeige <span class="font-medium">{{ (currentPage - 1) * perPage + 1 }}</span>
                                bis <span class="font-medium">{{ Math.min(currentPage * perPage, totalItems) }}</span>
                                von <span class="font-medium">{{ totalItems }}</span> Einträgen
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button
                                    @click="changePage(currentPage - 1)"
                                    :disabled="currentPage === 1"
                                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    :class="{ 'cursor-not-allowed opacity-50': currentPage === 1 }"
                                >
                                    <span class="sr-only">Zurück</span>
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                                
                                <button
                                    @click="changePage(currentPage + 1)"
                                    :disabled="currentPage === totalPages"
                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                    :class="{ 'cursor-not-allowed opacity-50': currentPage === totalPages }"
                                >
                                    <span class="sr-only">Weiter</span>
                                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { ref, onMounted, watch } from 'vue';

export default {
    emits: ['response-selected'],
    
    setup(props, { emit }) {
        const history = ref([]);
        const loading = ref(true);
        const searchTerm = ref('');
        const searchTimeout = ref(null);
        const currentPage = ref(1);
        const perPage = ref(5);
        const totalPages = ref(0);
        const totalItems = ref(0);
        
        const fetchHistory = async () => {
            loading.value = true;
            
            try {
                const params = {
                    page: currentPage.value,
                    perPage: perPage.value,
                    search: searchTerm.value
                };
                
                const response = await axios.get('/api/ai-responses/history', { params });
                
                history.value = response.data.data;
                totalPages.value = response.data.last_page;
                totalItems.value = response.data.total;
            } catch (error) {
                console.error('Error fetching AI response history:', error);
            } finally {
                loading.value = false;
            }
        };
        
        const handleSearch = () => {
            if (searchTimeout.value) {
                clearTimeout(searchTimeout.value);
            }
            
            searchTimeout.value = setTimeout(() => {
                currentPage.value = 1;
                fetchHistory();
            }, 300);
        };
        
        const changePage = (page) => {
            if (page < 1 || page > totalPages.value) return;
            
            currentPage.value = page;
            fetchHistory();
        };
        
        const selectResponse = (entry) => {
            emit('response-selected', entry);
        };
        
        const formatPrompt = (prompt) => {
            // Extrahiere die Eisenmarke aus dem Prompt
            const match = prompt.match(/Eisenmarke:\s*([^\n]+)/);
            if (match && match[1]) {
                return `Eisenmarke: ${match[1]}`;
            }
            
            // Fallback, falls keine Eisenmarke gefunden wird
            const lines = prompt.split('\n');
            const firstLine = lines[0];
            return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
        };
        
        const formatDate = (dateString) => {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('de-DE', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        };
        
        const deleteHistoryEntry = async (id) => {
            if (confirm('Möchten Sie diesen Verlaufseintrag wirklich löschen?')) {
                try {
                    await axios.delete(`/api/ai-responses/history/${id}`);
                    fetchHistory();
                } catch (error) {
                    console.error('Error deleting AI response history entry:', error);
                }
            }
        };
        
        onMounted(() => {
            fetchHistory();
        });
        
        watch([searchTerm], () => {
            handleSearch();
        });
        
        return {
            history,
            loading,
            searchTerm,
            currentPage,
            perPage,
            totalPages,
            totalItems,
            handleSearch,
            changePage,
            selectResponse,
            formatPrompt,
            formatDate,
            deleteHistoryEntry
        };
    }
};
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item {
    position: relative;
    border-bottom: 1px solid #e5e7eb;
    background-color: white;
}

.history-item:hover {
    background-color: rgba(235, 240, 255, 0.4);
}

/* Text-Styles für die Historieneinträge */
.history-item .text-base {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    line-height: 1.4;
}

/* Delete Button Styling */
.delete-button {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.history-item:hover .delete-button {
    opacity: 1;
    transform: scale(1);
}

/* Badge Optimierungen */
.inline-flex.rounded-full {
    background-color: rgba(224, 231, 255, 0.7);
    color: #4338ca;
}

/* Container-Hintergrund */
.ai-history-container {
    background-color: #f6f9ff;
}

/* Suchfeld-Optimierungen */
input[placeholder="Suchen..."] {
    background-color: white;
    border-color: #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    height: 45px;
}

/* Kopfzeilen-Hintergrund */
.border-b.border-gray-200.bg-blue-50 {
    background-color: #f0f5ff;
}
</style> 