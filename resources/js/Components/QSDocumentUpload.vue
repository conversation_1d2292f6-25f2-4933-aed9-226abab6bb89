<script setup>
import { ref, onMounted } from 'vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';

const props = defineProps({
    rotekarte_id: {
        type: Number,
        required: true
    }
});

const emit = defineEmits(['uploaded']);

const dropzone = ref(null);
const fileInput = ref(null);
const isDragging = ref(false);
const uploadProgress = ref(0);
const documents = ref([]);
const showUploadModal = ref(false);
const currentFile = ref(null);
const showPreviewModal = ref(false);
const previewDocument = ref(null);
const showDeleteModal = ref(false);
const documentToDelete = ref(null);

const uploadForm = useForm({
    file: null,
    rotekarte_id: props.rotekarte_id,
    description: ''
});

const loadDocuments = async () => {
    try {
        const response = await axios.get(`/qs-documents?rotekarte_id=${props.rotekarte_id}`);
        documents.value = response.data;
    } catch (error) {
        console.error('Error loading documents:', error);
    }
};

onMounted(() => {
    loadDocuments();
});

const handleDrop = (e) => {
    e.preventDefault();
    isDragging.value = false;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
};

const handleDragOver = (e) => {
    e.preventDefault();
    isDragging.value = true;
};

const handleDragLeave = () => {
    isDragging.value = false;
};

const handleFileSelect = (file) => {
    // Validate file type
    const allowedTypes = [
        'image/jpeg', 
        'image/png', 
        'application/pdf', 
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv',
        'application/json',
        'application/xml',
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        'application/x-tar',
        'application/gzip',
        'application/x-bzip2'
    ];
    if (!allowedTypes.includes(file.type)) {
        alert('Nicht unterstütztes Dateiformat. Erlaubt sind: JPG, PNG, PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, CSV, JSON, XML, ZIP, RAR, 7Z, TAR, GZ, BZ2');
        return;
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('Die Datei ist zu groß. Maximale Größe ist 10MB.');
        return;
    }

    currentFile.value = file;
    showUploadModal.value = true;
};

const uploadFile = async () => {
    const formData = new FormData();
    formData.append('file', currentFile.value);
    formData.append('rotekarte_id', props.rotekarte_id);
    formData.append('description', uploadForm.description);

    try {
        const response = await axios.post('/qs-documents', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'X-Requested-With': 'XMLHttpRequest'
            },
            onUploadProgress: (progressEvent) => {
                uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            }
        });

        if (response.data.success) {
            showUploadModal.value = false;
            uploadProgress.value = 0;
            uploadForm.reset();
            currentFile.value = null;
            await loadDocuments();
            emit('uploaded', response.data.document);
        }
    } catch (error) {
        console.error('Upload error:', error);
        alert('Fehler beim Hochladen der Datei.');
    }
};

const deleteDocument = async (documentId) => {
    showDeleteModal.value = true;
    documentToDelete.value = documentId;
};

const confirmDelete = async () => {
    try {
        await axios.delete(`/qs-documents/${documentToDelete.value}`, {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });
        documents.value = documents.value.filter(doc => doc.id !== documentToDelete.value);
        showDeleteModal.value = false;
        documentToDelete.value = null;
    } catch (error) {
        console.error('Delete error:', error);
        alert('Fehler beim Löschen der Datei.');
    }
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

const openFileDialog = () => {
    fileInput.value.click();
};

const openPreview = (doc) => {
    previewDocument.value = doc;
    showPreviewModal.value = true;
};

const closePreview = () => {
    showPreviewModal.value = false;
    previewDocument.value = null;
};
</script>

<template>
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <!-- Upload Area -->
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border-2 border-dashed border-blue-300 transition-all duration-300 ease-in-out cursor-pointer hover:border-blue-400 hover:from-blue-100 hover:to-blue-50"
            ref="dropzone"
            @drop.prevent="handleDrop"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @click="openFileDialog"
            :class="{ 'border-blue-500 from-blue-100 to-blue-50': isDragging }"
        >
            <div class="flex flex-col items-center justify-center p-6 space-y-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-sm font-medium text-gray-700">Dateien hier ablegen oder klicken</p>
                <p class="text-xs text-gray-500">Unterstützte Formate: JPG, PNG, PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, CSV, JSON, XML, ZIP, RAR, 7Z, TAR, GZ, BZ2 (max. 10MB)</p>
                <input
                    ref="fileInput"
                    type="file"
                    @change="e => handleFileSelect(e.target.files[0])"
                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.csv,.json,.xml,.zip,.rar,.7z,.tar,.gz,.bz2"
                    class="hidden"
                >
                <div v-if="uploadProgress > 0" class="w-full mt-2">
                    <div class="h-1 bg-blue-100 rounded-full overflow-hidden">
                        <div class="h-full bg-blue-500 transition-all duration-300 ease-out" :style="{ width: uploadProgress + '%' }"></div>
                    </div>
                    <p class="text-xs text-blue-600 text-center mt-1">{{ uploadProgress }}% hochgeladen</p>
                </div>
            </div>
        </div>

        <!-- Document List -->
        <div v-if="documents.length > 0" class="mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Hochgeladene Dateien</h3>
            <div class="space-y-4">
                <div v-for="doc in documents" :key="doc.id" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <!-- Icon based on file type -->
                        <div class="flex-shrink-0">
                            <svg v-if="doc.file_type === 'image'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>

                        <!-- File Info -->
                        <div>
                            <button
                                @click.prevent="openPreview(doc)"
                                class="text-sm font-medium text-blue-600 hover:text-blue-800"
                            >
                                {{ doc.filename }}
                            </button>
                            <div class="text-xs text-gray-500">
                                {{ formatFileSize(doc.file_size) }} • {{ doc.created_at }}
                            </div>
                            <div v-if="doc.description" class="text-sm text-gray-600 mt-1">
                                {{ doc.description }}
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center space-x-2">
                        <a
                            :href="doc.url"
                            target="_blank"
                            class="text-gray-600 hover:text-gray-800"
                            title="Download"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                        </a>
                        <button
                            type="button"
                            @click.stop.prevent="deleteDocument(doc.id)"
                            class="text-red-600 hover:text-red-800"
                            title="Löschen"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div v-if="showUploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div class="bg-white rounded-lg p-6 max-w-md w-full">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Datei hochladen</h3>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Dateiname</label>
                        <p class="mt-1 text-sm text-gray-500">{{ currentFile?.name }}</p>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Bezeichnung</label>
                        <textarea
                            id="description"
                            v-model="uploadForm.description"
                            rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        ></textarea>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button
                        @click.prevent="showUploadModal = false"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Abbrechen
                    </button>
                    <button
                        @click.prevent="uploadFile"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Hochladen
                    </button>
                </div>
            </div>
        </div>

        <!-- Preview Modal -->
        <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div class="bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">{{ previewDocument?.filename }}</h3>
                    <button @click="closePreview" class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="flex-1 overflow-auto">
                    <!-- Image Preview -->
                    <img v-if="previewDocument?.file_type === 'image'"
                        :src="previewDocument?.url"
                        :alt="previewDocument?.filename"
                        class="max-w-full h-auto mx-auto"
                    >
                    <!-- PDF Preview -->
                    <iframe v-else-if="previewDocument?.mime_type === 'application/pdf'"
                        :src="previewDocument?.url"
                        class="w-full h-[calc(90vh-8rem)]"
                        frameborder="0"
                    ></iframe>
                    <!-- Other file types -->
                    <div v-else class="text-center py-8">
                        <p class="text-gray-500">Diese Datei kann nicht direkt angezeigt werden.</p>
                        <a :href="previewDocument?.url"
                           target="_blank"
                           class="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                            Datei herunterladen
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div v-if="showDeleteModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                    <button
                        @click="showDeleteModal = false"
                        type="button"
                        class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        <span class="sr-only">Schließen</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <h3 class="text-base font-semibold leading-6 text-gray-900">Datei löschen</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Willst du die Datei wirklich löschen? Das lässt sich nicht rückgängig machen.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse gap-3">
                    <button
                        type="button"
                        @click="confirmDelete"
                        class="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:w-auto"
                    >
                        Löschen
                    </button>
                    <button
                        type="button"
                        @click="showDeleteModal = false"
                        class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto"
                    >
                        Abbrechen
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.upload-box {
    @apply relative w-full transition-all duration-300 ease-in-out;
}

.upload-box:hover {
    @apply shadow-lg;
}

.upload-box.dragging {
    @apply bg-blue-50 border-blue-500;
}
</style>
