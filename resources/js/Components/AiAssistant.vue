<template>
    <div class="ai-assistant-container">
        <!-- Loading state -->
        <div v-if="loading" class="loading">
            <div class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span class="ml-2 text-gray-600">Generiere Antwort...</span>
            </div>
        </div>

        <!-- Error state -->
        <div v-else-if="error" class="error-container bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="text-red-600 font-semibold">{{ error }}</div>
            <button @click="fetchResponse(true)" class="btn btn-sm btn-error mt-2">
                Erneut versuchen
            </button>
        </div>

        <!-- Results -->
        <div v-else-if="aiResponse" class="results">
            <div class="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                <div class="prose max-w-none" v-html="formattedResponse"></div>
                
                <!-- Metadata and controls -->
                <div class="mt-4 pt-3 border-t border-gray-200 flex justify-between items-center text-sm text-gray-600">
                    <div>
                        <span v-if="metadata.source === 'database'">
                            Gespeicherte Antwort vom {{ formatDate(metadata.created_at) }}
                            <span class="ml-2 badge badge-sm">{{ metadata.usage_count }}x verwendet</span>
                        </span>
                        <span v-else-if="metadata.source === 'cache'">
                            Zwischengespeicherte Antwort
                        </span>
                        <span v-else>
                            Neue Antwort vom {{ formatDate(metadata.generated_at) }}
                        </span>
                    </div>
                    <button 
                        v-if="metadata.can_refresh" 
                        @click="fetchResponse(true)" 
                        class="btn btn-sm btn-primary"
                        :disabled="loading"
                    >
                        Neue Antwort generieren
                    </button>
                </div>
            </div>
        </div>

        <!-- No response yet -->
        <div v-else class="no-response">
            <div class="text-center py-4 text-gray-500">
                Noch keine KI-Antwort generiert.
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

export default {
    props: {
        prompt: {
            type: String,
            required: true
        },
        analyseId: {
            type: Number,
            required: false,
            default: null
        },
        autoFetch: {
            type: Boolean,
            default: true
        }
    },
    
    data() {
        return {
            aiResponse: null,
            metadata: {},
            loading: false,
            error: null
        };
    },
    
    computed: {
        formattedResponse() {
            if (!this.aiResponse) return '';
            // Convert markdown to HTML and sanitize
            const html = marked(this.aiResponse);
            return DOMPurify.sanitize(html);
        }
    },
    
    mounted() {
        if (this.autoFetch && this.prompt) {
            this.fetchResponse();
        }
    },
    
    methods: {
        fetchResponse(forceNew = false) {
            this.loading = true;
            this.error = null;
            
            const data = {
                prompt: this.prompt,
                forceNew: forceNew
            };
            
            if (this.analyseId) {
                data.analyseId = this.analyseId;
            }
            
            axios.post('/api/ai-assistant/query', data)
                .then(response => {
                    this.aiResponse = response.data.response;
                    this.metadata = response.data.metadata || {};
                    this.loading = false;
                })
                .catch(error => {
                    console.error('Error fetching AI response:', error);
                    this.error = error.response?.data?.error || 'Fehler beim Abrufen der KI-Antwort';
                    this.loading = false;
                });
        },
        
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('de-DE', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    },
    
    watch: {
        prompt(newPrompt, oldPrompt) {
            if (newPrompt !== oldPrompt && newPrompt) {
                this.fetchResponse();
            }
        }
    }
};
</script>

<style scoped>
.ai-assistant-container {
    margin-bottom: 1rem;
}
</style> 