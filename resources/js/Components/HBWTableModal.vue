<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import Modal from '@/Components/Modal.vue';
import axios from 'axios';

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close']);

const searchTerm = ref('');
const selectedMaterial = ref('all');
const materials = ref([]);
const loading = ref(true);
const error = ref(null);

// Fetch data from the database when the component is mounted
const fetchMaterials = async () => {
    try {
        loading.value = true;
        error.value = null;
        const response = await axios.get('/hbwtable/all');
        materials.value = response.data;
        loading.value = false;
    } catch (err) {
        console.error('Fehler beim Laden der HBW-Daten:', err);
        error.value = '<PERSON>hler beim <PERSON>den der Daten. Bitte versuchen Sie es später erneut.';
        loading.value = false;
    }
};

// Load data when component is mounted
onMounted(fetchMaterials);

// Reload data when the modal is shown
const onShowChange = async (val) => {
    if (val) {
        await fetchMaterials();
    }
};

// Watch for prop changes
watch(() => props.show, onShowChange);

const uniqueMaterials = computed(() => {
    return ['all', ...new Set(materials.value.map(m => m.type))];
});

const filteredMaterials = computed(() => {
    return materials.value.filter(material =>
        selectedMaterial.value === 'all'
            ? material.type.toLowerCase().includes(searchTerm.value.toLowerCase())
            : material.type === selectedMaterial.value && material.type.toLowerCase().includes(searchTerm.value.toLowerCase())
    );
});
</script>

<template>
    <Modal :show="show" @close="$emit('close')" max-width="3xl">
        <div class="bg-white rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-4 py-3">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-lg font-semibold">
                            HBW = Härte Brinell Härteprüfung (HB30) nach DIN EN ISO 6506 -1
                        </h1>
                        <p class="text-xs opacity-90 mt-1">
                            Dokument: A691_14.doc | Version: 22 | Stand: 18.07.2024
                        </p>
                        <p class="text-xs opacity-90">
                            DIN EN ISO 6506-1
                        </p>
                    </div>
                    <button @click="$emit('close')" class="text-white hover:text-gray-200">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="p-4">
                <div class="flex flex-col sm:flex-row gap-3 mb-4">
                    <div class="relative flex-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-2 top-2 h-4 w-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                        <input
                            type="text"
                            placeholder="Material suchen..."
                            class="w-full pl-8 pr-3 py-1.5 text-sm border rounded-lg"
                            v-model="searchTerm"
                        >
                    </div>
                    <select
                        class="px-3 py-1.5 border rounded-lg bg-white text-sm"
                        v-model="selectedMaterial"
                    >
                        <option value="all">EM</option>
                        <option v-for="material in materials" :key="material.type" :value="material.type">
                            {{ material.type }}
                        </option>
                    </select>
                </div>

                <!-- Loading indicator -->
                <div v-if="loading" class="flex justify-center items-center py-10">
                    <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
                </div>

                <!-- Error message -->
                <div v-else-if="error" class="py-8 text-center text-red-600">
                    {{ error }}
                </div>

                <!-- Table with data -->
                <div v-else class="overflow-x-auto">
                    <table class="w-full border-collapse text-sm">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-3 py-2 text-left font-medium text-gray-600 border-b">Material</th>
                                <th class="px-3 py-2 text-left font-medium text-gray-600 border-b">Wanddicke [mm]</th>
                                <th class="px-3 py-2 text-center font-medium text-gray-600 border-b">Min. HBW</th>
                                <th class="px-3 py-2 text-center font-medium text-gray-600 border-b">Max. HBW</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="material in filteredMaterials" :key="material.type">
                                <tr v-for="(range, rangeIdx) in material.ranges" :key="`${material.type}-${rangeIdx}`"
                                    class="hover:bg-blue-50 transition-colors">
                                    <td v-if="rangeIdx === 0"
                                        class="px-3 py-2 border-b font-medium"
                                        :rowspan="material.ranges.length">
                                        {{ material.type }}
                                    </td>
                                    <td class="px-3 py-2 border-b">{{ range.thickness }}</td>
                                    <td class="px-3 py-2 border-b text-center">{{ range.min }}</td>
                                    <td class="px-3 py-2 border-b text-center">{{ range.max }}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 text-xs text-gray-500 space-y-1">
                    <p>Die Härtewerte dienen nur zur Information. Maßgeblich sind die mit dem Kunden vereinbarten physikalischen Werkstoffeigenschaften.</p>
                    <p>Sollten keine speziellen Eigenschaften vereinbart sein, gelten die Vorgaben der DIN EN 1561 und DIN EN 1563.</p>
                </div>
            </div>
        </div>
    </Modal>
</template>
