<?php
require 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Pruefkatalog;
use Illuminate\Support\Facades\DB;

// Simulationswerte
$analysewert = 0.794;
$eisenmarke = '811';
$element = 'Cu';

echo "Simulation für $element=$analysewert und Eisenmarke $eisenmarke:\n\n";

// VERBESSERTE SUCHE: Suche nach Fehlercodes, die die Eisenmarke enthalten könnten
echo "Suche nach passenden Fehlercodes mit verbessertem Algorithmus:\n";

// Methode 1: Suche mit LIKE-Operator für die Eisenmarke
$codesWithLike = DB::table('pruefkatalogs')
    ->where('element', $element)
    ->where(function($query) use ($eisenmarke) {
        $query->where('eisenmarke', $eisenmarke)
              ->orWhere('eisenmarke', 'LIKE', "$eisenmarke,%")
              ->orWhere('eisenmarke', 'LIKE', "%, $eisenmarke,%")
              ->orWhere('eisenmarke', 'LIKE', "%, $eisenmarke");
    })
    ->get();

echo "Gefundene Fehlercodes mit LIKE-Suche: " . count($codesWithLike) . "\n";
foreach ($codesWithLike as $code) {
    echo "- {$code->fehlercode} | Eisenmarke: {$code->eisenmarke} | {$code->absolutwerte} | ";
    echo "Typ: {$code->grenzwert_typ} | Operator: {$code->grenzwert_operator} | ";
    echo "Wert: {$code->grenzwert_wert}\n";
}

echo "\n";

// Methode 2: Suche mit manueller Überprüfung der Eisenmarke
$allCodes = Pruefkatalog::where('element', $element)->get();
$matchingCodes = [];

foreach ($allCodes as $code) {
    $eisenmarken = array_map('trim', explode(',', $code->eisenmarke));
    if (in_array($eisenmarke, $eisenmarken)) {
        $matchingCodes[] = $code;
    }
}

echo "Gefundene Fehlercodes mit manueller Eisenmarke-Prüfung: " . count($matchingCodes) . "\n";
foreach ($matchingCodes as $code) {
    echo "- {$code->fehlercode} | Eisenmarke: {$code->eisenmarke} | {$code->absolutwerte} | ";
    echo "Typ: {$code->grenzwert_typ} | Operator: {$code->grenzwert_operator} | ";
    echo "Wert: {$code->grenzwert_wert}\n";
}

echo "\n\n";

// VERBESSERTE ZUORDNUNG: Prüfe, ob der Analysewert den Bedingungen entspricht
echo "Prüfe Zuordnung des Analysewerts zu den gefundenen Fehlercodes:\n";
$foundMatch = false;

foreach ($matchingCodes as $code) {
    $match = false;
    $matchDescription = "";
    
    // Prüfen ob der Wert den Grenzwerten entspricht
    if ($code->grenzwert_operator && $code->grenzwert_wert !== null) {
        switch ($code->grenzwert_operator) {
            case '<':
                $match = $analysewert < $code->grenzwert_wert;
                $matchDescription = "$analysewert < {$code->grenzwert_wert}";
                break;
            case '<=':
                $match = $analysewert <= $code->grenzwert_wert;
                $matchDescription = "$analysewert <= {$code->grenzwert_wert}";
                break;
            case '>':
                $match = $analysewert > $code->grenzwert_wert;
                $matchDescription = "$analysewert > {$code->grenzwert_wert}";
                break;
            case '>=':
                $match = $analysewert >= $code->grenzwert_wert;
                $matchDescription = "$analysewert >= {$code->grenzwert_wert}";
                break;
        }
    } else if ($code->grenzwert_typ === 'UGW' && $code->grenzwert_richtung === '-') {
        // Für UGW ohne Operator: Wert unter Grenzwert prüfen
        if ($code->grenzwert_wert !== null) {
            $match = $analysewert < $code->grenzwert_wert;
            $matchDescription = "$analysewert < {$code->grenzwert_wert} (UGW implizit)";
        }
    } else if ($code->grenzwert_typ === 'OGW' && $code->grenzwert_richtung === '+') {
        // Für OGW ohne Operator: Wert über Grenzwert prüfen
        if ($code->grenzwert_wert !== null) {
            $match = $analysewert > $code->grenzwert_wert;
            $matchDescription = "$analysewert > {$code->grenzwert_wert} (OGW implizit)";
        }
    }
    
    echo "Prüfe {$code->fehlercode} | {$code->absolutwerte}: ";
    if ($match) {
        echo "MATCH! Bedingung: $matchDescription = " . ($match ? 'true' : 'false') . "\n";
        $foundMatch = true;
    } else {
        echo "KEIN MATCH. Bedingung: $matchDescription = " . ($match ? 'true' : 'false') . "\n";
    }
}

if (!$foundMatch) {
    echo "\nKein passender Fehlercode gefunden! Mögliche Gründe:\n";
    
    // Sollwertbereich prüfen
    $sollwertMin = 1.0;
    $sollwertMax = 1.1;
    if ($analysewert < $sollwertMin) {
        $abweichung = $analysewert - $sollwertMin;
        echo "Analysewert $analysewert ist unter dem Mindestsollwert $sollwertMin (Abweichung: $abweichung)\n";
        
        // Prüfe, ob es einen UGW-Fehlercode gibt, der dieser Bedingung entspricht
        foreach ($matchingCodes as $code) {
            if ($code->grenzwert_typ === 'UGW' && $code->grenzwert_richtung === '-') {
                echo "Es gibt einen UGW-Fehlercode ({$code->fehlercode}), der eigentlich passen sollte!\n";
                echo "Erwarteter Fehlercode wäre: {$code->fehlercode}\n";
                
                // Prüfen, warum dieser nicht zugeordnet wurde
                if ($code->grenzwert_operator === '>=') {
                    echo "Der Fehlercode hat den Operator '>=' statt '<', was ungewöhnlich für UGW ist.\n";
                    echo "Bei UGW - ≥ 0,04 % sollte der Wert MINDESTENS 0,04 sein, was erfüllt ist ($analysewert >= 0.04).\n";
                    echo "Aber das ist möglicherweise ein logischer Fehler in der Bedingung oder Interpretation.\n";
                }
            }
        }
    } else if ($analysewert > $sollwertMax) {
        $abweichung = $analysewert - $sollwertMax;
        echo "Analysewert $analysewert ist über dem Maximalsollwert $sollwertMax (Abweichung: $abweichung)\n";
    } else {
        echo "Analysewert $analysewert liegt innerhalb des Sollwertbereichs $sollwertMin - $sollwertMax\n";
    }
} 