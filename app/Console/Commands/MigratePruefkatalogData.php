<?php

namespace App\Console\Commands;

use App\Services\PruefkatalogMigrationService;
use Illuminate\Console\Command;

class MigratePruefkatalogData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pruefkatalog:migrate-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing Pruefkatalog absolutwerte data to structured format';

    /**
     * Execute the console command.
     */
    public function handle(PruefkatalogMigrationService $migrationService)
    {
        $this->info('Starting migration of Pruefkatalog data...');
        
        $result = $migrationService->migrateExistingData();
        
        $this->info("Migration complete!");
        $this->info("Total records: {$result['total']}");
        $this->info("Successful: {$result['success']}");
        $this->info("Failed: {$result['failed']}");
        
        if (count($result['stats']['types']) > 0) {
            $this->info('Detected pattern types:');
            
            foreach ($result['stats']['types'] as $type => $count) {
                $this->info("  {$type}: {$count}");
            }
        }
        
        return Command::SUCCESS;
    }
} 