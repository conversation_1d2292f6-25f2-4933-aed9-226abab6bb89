<?php

namespace App\Console\Commands;

use App\Models\QSDocument;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupQSDocuments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qs:cleanup-documents {--days=30 : Number of days to keep documents}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old QS documents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $date = Carbon::now()->subDays($days);

        $documents = QSDocument::where('created_at', '<', $date)->get();

        $count = 0;
        foreach ($documents as $document) {
            if (Storage::disk('public')->exists($document->path)) {
                Storage::disk('public')->delete($document->path);
            }
            $document->delete();
            $count++;
        }

        $this->info("Deleted {$count} documents older than {$days} days.");
    }
}
