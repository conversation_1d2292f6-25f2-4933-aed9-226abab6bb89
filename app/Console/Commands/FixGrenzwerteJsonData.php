<?php

namespace App\Console\Commands;

use App\Models\Pruefkatalog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixGrenzwerteJsonData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pruefkatalog:fix-json';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Korrigiert die JSON-Datenstruktur für bestehende Pruefkatalog-Einträge';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starte Korrektur der JSON-Daten für Pruefkatalog-Einträge...');
        
        $count = 0;
        $total = Pruefkatalog::count();
        
        $this->output->progressStart($total);
        
        // Verarbeite alle Einträge
        Pruefkatalog::chunk(100, function($pruefkatalogs) use (&$count) {
            foreach ($pruefkatalogs as $pruefkatalog) {
                DB::beginTransaction();
                try {
                    // Erstelle das JSON-Objekt basierend auf den strukturierten Feldern
                    $json = [
                        'typ' => $pruefkatalog->grenzwert_typ,
                        'richtung' => $pruefkatalog->grenzwert_richtung,
                        'operator' => $pruefkatalog->grenzwert_operator,
                        'wert' => $pruefkatalog->grenzwert_wert,
                        'einheit' => $pruefkatalog->grenzwert_einheit,
                        'text' => $pruefkatalog->absolutwerte
                    ];
                    
                    // Aktualisiere das JSON-Feld
                    $pruefkatalog->grenzwerte_json = $json;
                    $pruefkatalog->save();
                    
                    DB::commit();
                    $count++;
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Fehler beim Aktualisieren des JSON-Feldes', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'error' => $e->getMessage()
                    ]);
                }
                
                $this->output->progressAdvance();
            }
        });
        
        $this->output->progressFinish();
        
        $this->info("Fertig! {$count} von {$total} Einträgen wurden erfolgreich aktualisiert.");
        
        return Command::SUCCESS;
    }
} 