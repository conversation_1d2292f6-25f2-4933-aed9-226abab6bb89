<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use App\Models\Sollvorgaben;
use App\Models\Spektrometer;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use App\Mail\SpektrometerNotification;
use App\Models\EmailVerteiler;
use Illuminate\Support\Facades\Mail;
use App\Services\FehlercodeSuggestService;
use App\Models\Pruefkatalog;
use Illuminate\Support\Facades\Log;

class SpektrometerController extends Controller
{
    protected $emailService;
    protected $fehlercodeSuggestService;

    public function __construct(EmailService $emailService, FehlercodeSuggestService $fehlercodeSuggestService)
    {
        $this->emailService = $emailService;
        $this->fehlercodeSuggestService = $fehlercodeSuggestService;
    }

    public function index()
    {
        return Inertia::render('Spektrometer/Index');
    }

    public function getSollwerte(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'eisenmarke' => 'nullable|string',
            'abteilung' => 'required|string|in:NG,GG,HF'
        ]);

        // Debug logging
        \Log::info('Sollwerte request:', [
            'validated' => $validated,
            'abteilung' => $validated['abteilung']
        ]);

        // Wenn keine Eisenmarke angegeben ist und die Abteilung HF ist, gib alle HF-Eisenmarken zurück
        if (!isset($validated['eisenmarke']) && $validated['abteilung'] === 'HF') {
            $sollvorgaben = Sollvorgaben::all();

            // Debug logging
            \Log::info('HF Sollvorgaben:', [
                'count' => $sollvorgaben->count(),
                'data' => $sollvorgaben->toArray()
            ]);

            return response()->json($sollvorgaben);
        }

        // Wenn keine Eisenmarke angegeben ist und die Abteilung NG oder GG ist, gib alle regulären Eisenmarken zurück
        if (!isset($validated['eisenmarke']) && in_array($validated['abteilung'], ['NG', 'GG'])) {
            $sollvorgaben = Sollvorgaben::whereRaw("JSON_EXTRACT(data, '$.EM') IS NOT NULL")
                ->whereRaw("JSON_EXTRACT(data, '$.EM_HF') IS NULL")
                ->get();
            return response()->json($sollvorgaben);
        }

        // Wenn eine Eisenmarke angegeben ist, suche nach dieser spezifischen Eisenmarke
        $sollvorgaben = Sollvorgaben::findByEisenmarke($validated['eisenmarke'], $validated['abteilung']);

        if (!$sollvorgaben) {
            return response()->json([
                'message' => 'Keine Sollwerte für diese Eisenmarke gefunden'
            ], 404);
        }

        return response()->json($sollvorgaben->data);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'spektrometer_daten.name' => 'required|string',
            'spektrometer_daten.datum' => 'required|date',
            'spektrometer_daten.uhrzeit' => 'required|string',
            'spektrometer_daten.abteilung' => 'required|string|in:NG,GG,HF',
            'spektrometer_daten.chargennummer' => 'required|string',
            'spektrometer_daten.eisenmarke' => 'required|string',
            'spektrometer_daten.fehlercode' => 'nullable|string',
            'spektrometer_daten.proben' => 'required|array',
            'spektrometer_daten.proben.*.probenummer' => 'required|string',
            'spektrometer_daten.proben.*.gidNummer' => 'required|string',
            'spektrometer_daten.analysewerte' => 'required|array',
            'spektrometer_daten.analysewerte.*.element' => 'required|string',
            'spektrometer_daten.analysewerte.*.istWert' => 'required|string',
            'spektrometer_daten.analysewerte.*.sollWert' => 'required|string',
            'spektrometer_daten.bemerkungen' => 'nullable|string',
            'spektrometer_daten.system_info' => 'nullable|array'
        ]);

        // Calculate tolerance values for each analysis value
        $spektrometerDaten = $request->spektrometer_daten;
        if (!empty($spektrometerDaten['analysewerte'])) {
            foreach ($spektrometerDaten['analysewerte'] as &$analyse) {
                $istWert = str_replace(',', '.', $analyse['istWert']);
                $sollWert = $analyse['sollWert'];
                
                // Handle range values (e.g. "1140 - 1160")
                if (strpos($sollWert, '-') !== false) {
                    $sollWerte = array_map('trim', explode('-', $sollWert));
                    if (count($sollWerte) === 2) {
                        $min = (float) str_replace(',', '.', $sollWerte[0]);
                        $max = (float) str_replace(',', '.', $sollWerte[1]);
                        
                        // Convert istWert to float
                        $istWertFloat = (float) $istWert;
                        
                        // If value is below min, return negative deviation
                        if ($istWertFloat < $min) {
                            $analyse['toleranz'] = round($istWertFloat - $min, 3);
                        }
                        // If value is above max, return positive deviation
                        elseif ($istWertFloat > $max) {
                            $analyse['toleranz'] = round($istWertFloat - $max, 3);
                        }
                        // If value is within range, return 0
                        else {
                            $analyse['toleranz'] = 0.0;
                        }
                    }
                }
                // Handle single values
                else {
                    $sollWert = str_replace(',', '.', str_replace(['≤', '≥'], '', $sollWert));
                    $analyse['toleranz'] = round((float) $istWert - (float) $sollWert, 3);
                }
            }
        }

        // Check if selected error code requires parts lock and has measures
        $selectedFehlercode = null;
        if ($request->spektrometer_daten['fehlercode']) {
            $selectedFehlercode = \App\Models\Pruefkatalog::where('fehlercode', $request->spektrometer_daten['fehlercode'])->first();
        }

        // Determine if we should create a yellow card
        $createYellowCard = $selectedFehlercode && 
                           !$selectedFehlercode->teilesperren && 
                           (!$selectedFehlercode->massnahmen || $selectedFehlercode->massnahmen === 'keine' || $selectedFehlercode->massnahmen === 'Keine Maßnahme');

        // Create new Rotekarte with updated Spektrometer data including tolerance values
        $rotekarte = Rotekarte::create([
            'status' => 'spektrometer_complete',
            'spektrometer_daten' => $spektrometerDaten,
            'type' => $createYellowCard ? 'yellow' : 'red'
        ]);

        try {
            if ($createYellowCard) {
                // Generate yellow card PDF
                $pdf = \PDF::loadView('pdf.gelbkarte', [
                    'rotekarte' => $rotekarte,
                    'spektrometer' => $spektrometerDaten
                ]);

                // Set PDF options
                $pdf->setPaper('A4');
                $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

                // Create directory if it doesn't exist
                $directory = storage_path('app/public/GelbeKarte');
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                // Save the PDF
                $filename = 'Gelbkarte-' . $rotekarte->id . '.pdf';
                $pdf->save($directory . '/' . $filename);

                // Don't send emails for yellow cards
                return redirect()->route('dashboard')->with('success', 'Spektrometer Daten wurden erfolgreich gespeichert und eine Gelbkarte wurde erstellt.');
            } else {
                // Send emails based on department for red cards
                $this->emailService->sendSpektrometerEmails(
                    $spektrometerDaten,
                    $spektrometerDaten['abteilung'],
                    $rotekarte->id
                );

                return redirect()->route('dashboard')->with('success', 'Spektrometer Daten wurden erfolgreich gespeichert und eine Rotekarte wurde erstellt.');
            }
        } catch (\Exception $e) {
            \Log::error('Error in SpektrometerController@store', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->withErrors(['error' => 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.']);
        }
    }

    public function sendNotification(Request $request)
    {
        try {
            // Validate request
            $validated = $request->validate([
                'spektrometer_id' => 'required|exists:spektrometer,id',
                'rotekarte_id' => 'required|exists:rotekarten,id'
            ]);

            // Get spektrometer and rotekarte data
            $spektrometer = Spektrometer::with(['rotekarte'])->where('id', $validated['spektrometer_id'])->first();
            $rotekarte = Rotekarte::where('id', $validated['rotekarte_id'])->first();

            // Get active email recipients
            $recipients = EmailVerteiler::where('active', true)->get();

            if ($recipients->isEmpty()) {
                return response()->json([
                    'message' => 'Keine aktiven E-Mail-Empfänger gefunden.'
                ], 404);
            }

            // Send email to each recipient
            foreach ($recipients as $recipient) {
                Mail::to($recipient->email)
                    ->queue(new SpektrometerNotification($spektrometer, $rotekarte));
            }

            return response()->json([
                'message' => 'E-Mail-Benachrichtigungen wurden erfolgreich in die Warteschlange gestellt.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Fehler beim Senden der Spektrometer-Benachrichtigung: ' . $e->getMessage());
            return response()->json([
                'message' => 'Fehler beim Senden der E-Mail-Benachrichtigungen.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a list of all fehlercodes
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFehlercodes()
    {
        try {
            // Verwende einen Subquery, um einen repräsentativen Eintrag pro Fehlercode zu erhalten
            $fehlercodes = Pruefkatalog::select('fehlercode')
                ->distinct()
                ->orderBy('fehlercode')
                ->get()
                ->map(function($item) {
                    // Für jeden eindeutigen Fehlercode holen wir einen vollständigen Datensatz
                    return Pruefkatalog::where('fehlercode', $item->fehlercode)
                        ->select('id', 'fehlercode', 'element', 'eisenmarke', 'absolutwerte', 'teilesperren', 'massnahmen')
                        ->first();
                });
            
            return response()->json($fehlercodes);
        } catch (\Exception $e) {
            Log::error('Fehler beim Abrufen der Fehlercodes', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(['error' => 'Fehler beim Abrufen der Fehlercodes'], 500);
        }
    }

    /**
     * Suggest a fehlercode based on eisenmarke and analysewerte
     */
    public function suggestFehlercode(Request $request)
    {
        $validated = $request->validate([
            'eisenmarke' => 'required|string',
            'analysewerte' => 'required|array'
        ]);

        // Debugging: Prüfkatalog-Einträge prüfen
        $eisenmarke = $validated['eisenmarke'];
        $element = $validated['analysewerte'][0]['element'] ?? null;
        
        if ($element && $element !== 'Nicht analysierbar') {
            $pruefkatalogEntries = \App\Models\Pruefkatalog::where('eisenmarke', $eisenmarke)
                ->where('element', $element)
                ->get();
                
            \Illuminate\Support\Facades\Log::info('Verfügbare Prüfkatalog-Einträge für Anfrage:', [
                'eisenmarke' => $eisenmarke,
                'element' => $element,
                'entries' => $pruefkatalogEntries->toArray()
            ]);
        }

        // Die injizierte Service-Instanz verwenden
        $result = $this->fehlercodeSuggestService->suggestFehlercode(
            $validated['eisenmarke'],
            $validated['analysewerte']
        );

        return response()->json($result);
    }
}
