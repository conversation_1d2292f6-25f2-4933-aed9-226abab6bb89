<?php

namespace App\Http\Controllers;

use App\Models\ExcelData;
use App\Imports\ExcelDataImport;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ExcelDataController extends Controller
{
    public function index()
    {
        $excelData = ExcelData::latest()->first();

        return Inertia::render('ExcelData/Index', [
            'excelData' => $excelData,
            'lastSync' => $excelData?->last_sync?->format('d.m.Y H:i:s')
        ]);
    }

    public function import(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        $file = $request->file('excel_file');
        $filename = $file->getClientOriginalName();

        Excel::import(new ExcelDataImport($filename), $file);

        return redirect()->back()->with('message', 'Excel-Datei erfolgreich importiert.');
    }

    public function destroy()
    {
        ExcelData::truncate();

        return redirect()->back()->with('message', 'Alle Excel-Daten wurden erfolgreich gelöscht.');
    }
}
