<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GussnachbehandlungController extends Controller
{
    public function index(Request $request)
    {
        $rotekarte = null;
        if ($request->has('rotekarte')) {
            $rotekarte = Rotekarte::findOrFail($request->rotekarte);
        }

        return Inertia::render('Gussnachbehandlung/Index', [
            'rotekarte' => $rotekarte
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'rotekarte_id' => 'required|exists:rotekarten,id',
            'gussnachbehandlung_daten' => 'required|array',
            'gussnachbehandlung_daten.verantwortlicher' => 'required|string|max:255',
            'gussnachbehandlung_daten.datum' => 'required|date',
            'gussnachbehandlung_daten.bemerkungen' => 'nullable|string',
            'gussnachbehandlung_daten.system_info' => 'required|array',
            'gussnachbehandlung_daten.system_info.current' => 'required|array',
            'gussnachbehandlung_daten.system_info.history' => 'required|array'
        ]);

        $rotekarte = Rotekarte::findOrFail($validated['rotekarte_id']);
        $rotekarte->gussnachbehandlung_daten = $validated['gussnachbehandlung_daten'];
        $rotekarte->status = 'guss_complete';
        $rotekarte->save();

        return redirect()->route('dashboard')->with('success', 'Gussnachbehandlung wurde erfolgreich gespeichert');
    }
}
