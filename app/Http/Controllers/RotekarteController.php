<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Log;

class RotekarteController extends Controller
{
    public function index(): Response
    {
        $rotekarten = Rotekarte::query()
            ->latest()
            ->with(['user'])
            ->paginate(10);

        return Inertia::render('Rotekarte/Index', [
            'rotekarten' => $rotekarten
        ]);
    }

    public function edit($id)
    {
        $rotekarte = Rotekarte::findOrFail($id);

        return Inertia::render('Rotekarte/Edit', [
            'rotekarte' => $rotekarte
        ]);
    }

    public function update(Request $request, $id)
    {
        $rotekarte = Rotekarte::findOrFail($id);

        // Validierung der Eingaben
        $validated = $request->validate([
            'status' => 'required|string',
            'spektrometer_daten' => 'required|array',
            // Weitere Validierungsregeln je nach Bedarf
        ]);

        $rotekarte->update($validated);

        return redirect()->back()->with('success', 'Rotekarte wurde erfolgreich aktualisiert.');
    }

    public function show(Rotekarte $rotekarte)
    {
        return Inertia::render('Rotekarte/Show', [
            'rotekarte' => $rotekarte
        ]);
    }

    public function generateFinalPdf(Rotekarte $rotekarte)
    {
        try {
            // Check if the Rotekarte is completed
            if ($rotekarte->status !== 'Abgeschlossen') {
                Log::error('Rotekarte status is not Abgeschlossen', ['status' => $rotekarte->status]);
                abort(403, 'Die Rotekarte muss abgeschlossen sein, um das finale PDF zu generieren.');
            }

            Log::info('Starting PDF generation for Rotekarte', ['id' => $rotekarte->id]);

            // Generate PDF using Laravel PDF package (e.g., DomPDF)
            $pdf = \PDF::loadView('pdf.rotekarte-final', [
                'rotekarte' => $rotekarte,
                'spektrometer' => $rotekarte->spektrometer_daten,
                'formanlage' => $rotekarte->formanlage_daten,
                'gussNachbehandlung' => $rotekarte->gussNachbehandlung_daten,
                'qs' => $rotekarte->qs_daten
            ]);

            // Set PDF options for better quality and layout
            $pdf->setPaper('A4');
            $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

            // Generate a filename with the Rotekarte ID and date
            $filename = 'Rotekarte-' . $rotekarte->id . '-Final.pdf';
            
            // Create RotekarteFinal directory if it doesn't exist
            $directory = storage_path('app/public/RotekarteFinal');
            if (!file_exists($directory)) {
                Log::info('Creating RotekarteFinal directory', ['path' => $directory]);
                mkdir($directory, 0755, true);
            }

            $fullPath = $directory . '/' . $filename;
            Log::info('Saving PDF to path', ['path' => $fullPath]);

            // Save the PDF to the RotekarteFinal directory
            $pdf->save($fullPath);

            // Verify file was created
            if (!file_exists($fullPath)) {
                Log::error('PDF file was not created', ['path' => $fullPath]);
                throw new \Exception('PDF konnte nicht erstellt werden.');
            }

            Log::info('PDF generated successfully', ['path' => $fullPath]);

            // Return the PDF for download
            return response()->file($fullPath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"'
            ]);

        } catch (\Exception $e) {
            Log::error('Error generating PDF', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'rotekarte_id' => $rotekarte->id
            ]);

            abort(500, 'Fehler beim Generieren der PDF: ' . $e->getMessage());
        }
    }
}
