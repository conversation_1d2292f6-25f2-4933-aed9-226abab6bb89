<?php

namespace App\Http\Controllers;

use App\Models\GlobalSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    public function get($key)
    {
        try {
            $value = GlobalSetting::getValue($key);
            return response()->json(['value' => $value]);
        } catch (\Exception $e) {
            Log::error('Error getting setting: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get setting'], 500);
        }
    }

    public function update(Request $request)
    {
        try {
            $validated = $request->validate([
                'key' => 'required|string',
                'value' => 'required'
            ]);

            GlobalSetting::setValue($validated['key'], $validated['value']);
            
            return response()->json(['message' => 'Setting updated successfully']);
        } catch (\Exception $e) {
            Log::error('Error updating setting: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update setting'], 500);
        }
    }
} 