<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;

class RotekartePDFController extends Controller
{
    private function calculateTolerance($istWert, $sollWert): ?float
    {
        // Wenn der Soll-Wert ein Bereich ist (z.B. "0.03 - 0.04")
        if (strpos($sollWert, '-') !== false) {
            $sollWerte = array_map('trim', explode('-', $sollWert));
            if (count($sollWerte) === 2) {
                $min = (float) $sollWerte[0];
                $max = (float) $sollWerte[1];
                $mittelwert = ($min + $max) / 2;
                return round((float) $istWert - $mittelwert, 3);
            }
        }
        // Wenn der Soll-Wert ein einzelner Wert ist
        else {
            $sollWert = (float) str_replace(['≤', '≥'], '', $sollWert);
            return round((float) $istWert - $sollWert, 3);
        }

        return null;
    }

    public function generatePDF(Rotekarte $rotekarte)
    {
        $spektrometerDaten = $rotekarte->spektrometer_daten;
        
        // Berechne Toleranz für jeden Analysewert
        if (!empty($spektrometerDaten['analysewerte'])) {
            foreach ($spektrometerDaten['analysewerte'] as &$analyse) {
                if (!isset($analyse['toleranz'])) {
                    $analyse['toleranz'] = $this->calculateTolerance(
                        $analyse['istWert'],
                        $analyse['sollWert']
                    );
                }
            }
            // Aktualisiere die Spektrometer-Daten mit den berechneten Toleranzwerten
            $rotekarte->spektrometer_daten = $spektrometerDaten;
            $rotekarte->save();
        }

        // Rest des Codes...
    }
} 