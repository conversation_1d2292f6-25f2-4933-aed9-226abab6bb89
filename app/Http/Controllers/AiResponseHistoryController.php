<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AiResponseHistoryController extends Controller
{
    /**
     * Get paginated AI response history
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->input('perPage', 5);
        $search = $request->input('search', '');
        
        $query = DB::table('ai_responses')
            ->whereNull('rotekarte_id')
            ->orderBy('created_at', 'desc');
            
        if ($search) {
            $query->where('prompt', 'like', '%' . $search . '%');
        }
        
        $history = $query->paginate($perPage);
        
        return response()->json($history);
    }
    
    /**
     * Get a specific AI response by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $response = DB::table('ai_responses')->find($id);
        
        if (!$response) {
            return response()->json([
                'error' => 'AI response not found'
            ], 404);
        }
        
        return response()->json([
            'response' => $response->response,
            'prompt' => $response->prompt,
            'metadata' => [
                'source' => 'database',
                'created_at' => $response->created_at,
                'usage_count' => $response->usage_count,
                'can_refresh' => true
            ]
        ]);
    }
    
    /**
     * Delete an AI response by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $deleted = DB::table('ai_responses')->where('id', $id)->delete();
        
        if (!$deleted) {
            return response()->json([
                'error' => 'Failed to delete AI response'
            ], 404);
        }
        
        return response()->json([
            'message' => 'AI response deleted successfully'
        ]);
    }
} 