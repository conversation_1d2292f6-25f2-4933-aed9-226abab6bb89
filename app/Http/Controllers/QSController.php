<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use App\Models\EmailVerteiler;
use App\Models\GlobalSetting;
use App\Mail\QsCompleteMail;
use App\Mail\SeriesStatusUpdateMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class QSController extends Controller
{
    public function index(Request $request)
    {
        $rotekarte = null;
        if ($request->has('rotekarte')) {
            $rotekarte = Rotekarte::findOrFail($request->rotekarte);
        }

        return Inertia::render('QS/Index', [
            'rotekarte' => $rotekarte
        ]);
    }

    public function store(Request $request)
    {
        Log::info('QS Store Request:', $request->all());

        try {
            Log::info('Starting validation...');
            $validated = $request->validate([
                'rotekarte_id' => 'required|exists:rotekarten,id',
                'qs_daten' => 'required|array',
                'qs_daten.qs_type' => 'required|in:kleinguss,grossguss',
                'qs_daten.pruefer' => 'required|array',
                'qs_daten.pruefer.*.name' => 'required|string',
                'qs_daten.pruefer.*.datum' => 'required|date',
                'qs_daten.vorschlag_pruefungen' => 'nullable|string',
                'qs_daten.teile' => 'required|array',
                'qs_daten.teile.*.teilenummer' => 'required|string',
                'qs_daten.teile.*.anzahl' => 'required|numeric',
                'qs_daten.teile.*.gepruefteTeile' => 'required|numeric',
                'qs_daten.teile.*.io_nio' => 'nullable|in:IO,NIO',
                'qs_daten.io_nio' => 'required|in:IO,NIO',
                'qs_daten.bemerkungen' => 'nullable|array',
                'qs_daten.bemerkungen.*.text' => 'required|string',
                'qs_daten.bemerkungen.*.pruefer' => 'required|string',
                'qs_daten.bemerkungen.*.timestamp' => 'required|string',
                'qs_daten.bemerkungen.*.locked' => 'required|boolean',
                'qs_daten.status' => 'required|in:Offen,In Bearbeitung,Abgeschlossen,Abgelehnt',
                'qs_daten.serie_status' => 'required|in:Entscheidung ausstehend,Teile in die Serie einreihen,Zur Verschrottung freigegeben',
                'qs_daten.system_info' => 'required|array',
                'qs_daten.system_info.current' => 'required|array',
                'qs_daten.system_info.history' => 'required|array'
            ]);
            Log::info('Validation passed:', $validated);

            Log::info('Finding Rotekarte...');
            $rotekarte = Rotekarte::where('id', $validated['rotekarte_id'])->first();
            if (!$rotekarte) {
                Log::error('Rotekarte not found with ID: ' . $validated['rotekarte_id']);
                throw new \Exception('Rotekarte not found');
            }
            Log::info('Found Rotekarte:', ['id' => $rotekarte->id]);

            // Get the current status and series status before update
            $previousStatus = isset($rotekarte->qs_daten['status']) ? $rotekarte->qs_daten['status'] : null;
            $previousSeriesStatus = isset($rotekarte->qs_daten['serie_status']) ? $rotekarte->qs_daten['serie_status'] : null;

            $newStatus = $validated['qs_daten']['status'];
            $newSeriesStatus = $validated['qs_daten']['serie_status'];

            // Log current state
            Log::info('Current Rotekarte state:', [
                'id' => $rotekarte->id,
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
                'previous_series_status' => $previousSeriesStatus,
                'new_series_status' => $newSeriesStatus,
                'current_qs_daten' => $rotekarte->qs_daten,
                'new_qs_daten' => $validated['qs_daten']
            ]);

            // Preserve existing Härtewerte if they exist in the database but not in the submitted form
            if (isset($rotekarte->qs_daten['haertewerte']) && is_array($rotekarte->qs_daten['haertewerte']) && 
                count($rotekarte->qs_daten['haertewerte']) > 0 && 
                (!isset($validated['qs_daten']['haertewerte']) || count($validated['qs_daten']['haertewerte']) === 0)) {
                
                Log::info('Preserving existing Härtewerte in the database');
                $validated['qs_daten']['haertewerte'] = $rotekarte->qs_daten['haertewerte'];
            }

            Log::info('Updating Rotekarte...');
            $rotekarte->qs_daten = $validated['qs_daten'];
            
            // Update the main Rotekarte status and generate PDF only when Abnahmebeauftragter makes a decision
            if ($validated['qs_daten']['serie_status'] !== 'Entscheidung ausstehend' && 
                ($previousSeriesStatus === 'Entscheidung ausstehend' || $previousSeriesStatus === null)) {
                $rotekarte->status = 'Abgeschlossen';

                // Generate final PDF
                try {
                    // Stelle sicher, dass die Toleranzwerte berechnet sind
                    $spektrometerDaten = $rotekarte->spektrometer_daten;
                    if (!empty($spektrometerDaten['analysewerte'])) {
                        $updated = false;
                        foreach ($spektrometerDaten['analysewerte'] as &$analyse) {
                            if (!isset($analyse['toleranz'])) {
                                $analyse['toleranz'] = $this->calculateTolerance($analyse['istWert'], $analyse['sollWert']);
                                $updated = true;
                            }
                        }
                        if ($updated) {
                            $rotekarte->spektrometer_daten = $spektrometerDaten;
                            $rotekarte->save();
                        }
                    }

                    $pdf = \PDF::loadView('pdf.rotekarte-final', [
                        'rotekarte' => $rotekarte,
                        'spektrometer' => $rotekarte->spektrometer_daten,
                        'formanlage' => $rotekarte->formanlage_daten,
                        'gussNachbehandlung' => $rotekarte->gussNachbehandlung_daten,
                        'qs' => $rotekarte->qs_daten
                    ]);

                    // Set PDF options
                    $pdf->setPaper('A4');
                    $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

                    // Create directory if it doesn't exist
                    $directory = storage_path('app/public/RotekarteFinal');
                    if (!file_exists($directory)) {
                        mkdir($directory, 0755, true);
                    }

                    // Save the PDF
                    $filename = 'Rotekarte-' . $rotekarte->id . '-Final.pdf';
                    $pdf->save($directory . '/' . $filename);

                    Log::info('Final PDF generated for Rotekarte', ['id' => $rotekarte->id]);
                } catch (\Exception $e) {
                    Log::error('Error generating final PDF', [
                        'error' => $e->getMessage(),
                        'rotekarte_id' => $rotekarte->id
                    ]);
                }
            }
            $rotekarte->save();
            Log::info('Rotekarte updated successfully');

            // Check if series status has changed and is not set to "Entscheidung ausstehend"
            if ($previousSeriesStatus !== $newSeriesStatus && $newSeriesStatus !== 'Entscheidung ausstehend') {
                Log::info('Sending series status update email...');
                $this->sendSeriesStatusUpdateEmail($rotekarte);
                Log::info('Series status update email sent for change from ' . ($previousSeriesStatus ?? 'null') . ' to ' . $newSeriesStatus);
            } else {
                Log::info('No series status update email sent - status is "Entscheidung ausstehend" or unchanged');
            }

            // Check if we should send the QS complete email
            if ($newStatus === 'Abgeschlossen' &&
                (!$previousStatus ||
                 $previousStatus === 'Offen' ||
                 $previousStatus === 'In Bearbeitung')) {
                Log::info('Sending QS complete email...');
                // Refresh model before sending email
                $rotekarte = $rotekarte->fresh();
                $this->sendQsCompleteEmail($rotekarte);
                Log::info('QS Complete email sent for status change from ' . ($previousStatus ?? 'null') . ' to Abgeschlossen');
            } else {
                Log::info('No QS complete email sent - status change from ' . ($previousStatus ?? 'null') . ' to ' . $newStatus);
            }

            Log::info('QS Data saved successfully for Rotekarte Nr. ' . $rotekarte->id);

            return redirect('/')->with('success', 'QS-Daten wurden erfolgreich gespeichert.');
        } catch (\Exception $e) {
            Log::error('Error saving QS data: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return back()->withErrors(['error' => 'Fehler beim Speichern der QS-Daten: ' . $e->getMessage()]);
        }
    }

    private function sendSeriesStatusUpdateEmail(Rotekarte $rotekarte)
    {
        try {
            // Determine the email distribution list based on the department
            $verteilerType = in_array($rotekarte->spektrometer_daten['abteilung'], ['GG', 'HF'])
                ? 'qs_gg'  // For GG and HF
                : 'qs_kg'; // For NG and others

            // Get email addresses from the appropriate distribution list
            $emailVerteiler = EmailVerteiler::where('type', $verteilerType)->get();

            if ($emailVerteiler->isEmpty()) {
                Log::warning("Keine E-Mail-Adressen für Verteiler {$verteilerType} gefunden.");
                return;
            }

            // Split emails into TO and CC
            $toEmails = $emailVerteiler->where('is_cc', false)->pluck('email')->toArray();
            $ccEmails = $emailVerteiler->where('is_cc', true)->pluck('email')->toArray();

            // Send the email
            Mail::to($toEmails)
                ->cc($ccEmails)
                ->send(new SeriesStatusUpdateMail($rotekarte));

            Log::info("Series status update email sent for Rotekarte Nr. {$rotekarte->id} to {$verteilerType}");
        } catch (\Exception $e) {
            Log::error("Error sending series status update email: " . $e->getMessage());
        }
    }

    private function sendQsCompleteEmail(Rotekarte $rotekarte)
    {
        try {
            // Bestimme den E-Mail-Verteiler basierend auf der Abteilung
            $verteilerType = in_array($rotekarte->spektrometer_daten['abteilung'], ['GG', 'HF'])
                ? 'abnahmebeauftragter_gg'  // Für GG und HF
                : 'abnahmebeauftragter';    // Für NG und andere

            // Hole die E-Mail-Adressen aus dem entsprechenden Verteiler
            $emailVerteiler = EmailVerteiler::where('type', $verteilerType)->get();

            if ($emailVerteiler->isEmpty()) {
                Log::warning("Keine E-Mail-Adressen für Verteiler {$verteilerType} gefunden.");
                return;
            }

            // Teile die E-Mails in TO und CC
            $toEmails = $emailVerteiler->where('is_cc', false)->pluck('email')->toArray();
            $ccEmails = $emailVerteiler->where('is_cc', true)->pluck('email')->toArray();

            // Sende die E-Mail
            Mail::to($toEmails)
                ->cc($ccEmails)
                ->send(new QsCompleteMail($rotekarte));

            Log::info("QS Complete E-Mail für Rotekarte Nr.  {$rotekarte->id} an {$verteilerType} gesendet.");
        } catch (\Exception $e) {
            Log::error("Fehler beim Senden der QS Complete E-Mail: " . $e->getMessage());
        }
    }

    private function calculateTolerance($istWert, $sollWert): ?float
    {
        // Wenn der Soll-Wert ein Bereich ist (z.B. "0.03 - 0.04")
        if (strpos($sollWert, '-') !== false) {
            $sollWerte = array_map('trim', explode('-', $sollWert));
            if (count($sollWerte) === 2) {
                $min = (float) $sollWertes[0];
                $max = (float) $sollWertes[1];
                $mittelwert = ($min + $max) / 2;
                return round((float) $istWert - $mittelwert, 3);
            }
        }
        // Wenn der Soll-Wert ein einzelner Wert ist
        else {
            $sollWert = (float) str_replace(['≤', '≥'], '', $sollWert);
            return round((float) $istWert - $sollWert, 3);
        }

        return null;
    }

    /**
     * Berechnet die Verschrottungskosten für die angegebenen Teile.
     */
    public function calculateScrapCosts(Request $request)
    {
        try {
            $teile = $request->input('teile', []);
            \Log::info('Received parts for scrap calculation:', $teile);
            
            $results = [];
            $totalCost = 0;
            $totalWeight = 0;
            $totalScrapValue = 0;
            $physikalischePruefungKosten = floatval(GlobalSetting::getValue('physical_examination_cost', 600.00));

            foreach ($teile as $teil) {
                // Debug-Ausgabe für eingehende Daten
                \Log::info('Processing part:', $teil);
                
                $teilNummer = $teil['teilenummer'] ?? '';
                $anzahl = intval($teil['anzahl'] ?? $teil['gepruefteTeile'] ?? 1);
                
                \Log::info("Searching for part number: {$teilNummer} with quantity: {$anzahl}");
                
                // Suche direkt nach der Teilenummer in der Datenbank
                $teilInfo = DB::table('teil_nummern')
                    ->where('teil_nummer', $teilNummer)
                    ->first();
                
                if ($teilInfo) {
                    \Log::info("Found part info in database:", (array)$teilInfo);
                    
                    $einzelPreis = floatval($teilInfo->preis);
                    $einzelGewicht = floatval($teilInfo->gewicht);
                    $schrottPreis = floatval($teilInfo->schrott_preis);
                    
                    $gesamtPreis = $einzelPreis * $anzahl;
                    $gesamtGewicht = $einzelGewicht * $anzahl;
                    $gesamtSchrottWert = $schrottPreis * $gesamtGewicht;
                    $verlust = $gesamtPreis - $gesamtSchrottWert;
                    
                    $results[] = [
                        'teilenummer' => $teilNummer,
                        'menge' => $anzahl,
                        'einzelPreis' => $einzelPreis,
                        'einzelGewicht' => $einzelGewicht,
                        'schrottPreis' => $schrottPreis,
                        'gesamtPreis' => $gesamtPreis,
                        'gesamtGewicht' => $gesamtGewicht,
                        'schrottWert' => $gesamtSchrottWert,
                        'verlust' => $verlust
                    ];
                    
                    $totalCost += $gesamtPreis;
                    $totalWeight += $gesamtGewicht;
                    $totalScrapValue += $gesamtSchrottWert;
                    
                    \Log::info("Calculated values for part:", end($results));
                } else {
                    \Log::warning("Part not found in database: {$teilNummer}");
                }
            }
            
            $totalLoss = $totalCost - $totalScrapValue;
            
            $summary = [
                'totalCost' => $totalCost,
                'totalWeight' => $totalWeight,
                'totalScrapValue' => $totalScrapValue,
                'totalLoss' => $totalLoss,
                'physikalischePruefungKosten' => $physikalischePruefungKosten,
                'pruefungLohntSich' => $totalLoss > $physikalischePruefungKosten,
                'message' => $totalLoss > $physikalischePruefungKosten 
                    ? "Physikalische Prüfung wird empfohlen (Verlust > {$physikalischePruefungKosten}€)"
                    : "Physikalische Prüfung nicht empfohlen (Verlust < {$physikalischePruefungKosten}€)"
            ];
            
            \Log::info("Final calculation summary:", $summary);
            
            return response()->json([
                'success' => true,
                'details' => $results,
                'summary' => $summary
            ]);
            
        } catch (\Exception $e) {
            \Log::error("Error in scrap calculation: " . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler bei der Berechnung: ' . $e->getMessage()
            ], 500);
        }
    }

    public function saveHaertewerte(Request $request)
    {
        try {
            \Log::info('saveHaertewerte wurde aufgerufen mit Daten:', [
                'rotekarte_id' => $request->rotekarte_id, 
                'haertewerte_count' => count($request->haertewerte ?? [])
            ]);
            
            // Ändere die Validierung, um auch leere Arrays zuzulassen
            $request->validate([
                'rotekarte_id' => 'required|exists:rotekarten,id',
                'haertewerte' => 'present|array', // 'present' statt 'required', erlaubt leere Arrays
            ]);

            // Validiere nur die Elemente, wenn das Array nicht leer ist
            if (!empty($request->haertewerte)) {
                foreach ($request->haertewerte as $index => $haertewert) {
                    $request->validate([
                        "haertewerte.{$index}.eisenmarke" => 'required|string',
                        "haertewerte.{$index}.nestnummer" => 'required|string',
                        "haertewerte.{$index}.soll_von" => 'required|numeric',
                        "haertewerte.{$index}.soll_bis" => 'required|numeric',
                        "haertewerte.{$index}.ist_wert" => 'required|numeric',
                    ]);
                }
            }

            $rotekarte = Rotekarte::findOrFail($request->rotekarte_id);
            
            // Aktualisiere die Härtewerte im qs_daten JSON
            $qs_daten = $rotekarte->qs_daten ?? [];
            $oldCount = count($qs_daten['haertewerte'] ?? []);
            $qs_daten['haertewerte'] = $request->haertewerte ?? [];
            $newCount = count($request->haertewerte ?? []);
            
            \Log::info("Härtewerte werden aktualisiert", [
                'rotekarte_id' => $rotekarte->id,
                'alteAnzahl' => $oldCount,
                'neueAnzahl' => $newCount,
                'haertewerte' => $request->haertewerte
            ]);
            
            $rotekarte->qs_daten = $qs_daten;
            $rotekarte->save();

            \Log::info("Härtewerte erfolgreich gespeichert");
            
            return response()->json([
                'success' => true,
                'message' => 'Härtewerte wurden erfolgreich gespeichert',
                'debug_info' => [
                    'alteAnzahl' => $oldCount,
                    'neueAnzahl' => $newCount
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Speichern der Härtewerte: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Speichern der Härtewerte: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
