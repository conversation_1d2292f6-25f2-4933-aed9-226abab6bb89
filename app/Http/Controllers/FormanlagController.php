<?php

namespace App\Http\Controllers;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Mail\FormanlagCompleteMail;
use App\Models\EmailVerteiler;
use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Inertia\Response;

class FormanlagController extends Controller
{
    public function index(Request $request): Response
    {
        $rotekarte = null;

        // Prüfe ob eine Rotekarte ID übergeben wurde
        if ($request->has('rotekarte')) {
            $rotekarte = Rotekarte::findOrFail($request->rotekarte);
        }

        return Inertia::render('Formanlage/Index', [
            'rotekarte' => $rotekarte
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'rotekarte_id' => 'required|exists:rotekarten,id',
            'formanlage_daten' => 'required|array',
            'formanlage_daten.verantwortlicher' => 'required|string|max:255',
            'formanlage_daten.giessdatum' => 'required|date',
            'formanlage_daten.giesszeit' => 'required',
            'formanlage_daten.abteilung' => 'required|string|in:NG,GG,HF',
            'formanlage_daten.formen_gekennzeichnet' => 'boolean',
            'formanlage_daten.formen_an_anlage' => 'boolean',
            'formanlage_daten.ausschussseparation' => 'boolean',
            'formanlage_daten.teile' => 'required|array|min:1',
            'formanlage_daten.teile.*.teilenummer' => 'required|string|max:255',
            'formanlage_daten.teile.*.anzahl' => 'required|integer|min:1',
            'formanlage_daten.anzahl_kasten' => 'required|integer|min:1',
            'formanlage_daten.sap_zahlnummer' => 'required|string|max:255',
            'formanlage_daten.bemerkungen' => 'nullable|string',
            'formanlage_daten.system_info' => 'required|array',
            'formanlage_daten.system_info.current' => 'required|array',
            'formanlage_daten.system_info.history' => 'required|array',
        ]);

        $rotekarte = Rotekarte::where('id', $validated['rotekarte_id'])->first();
        if (!$rotekarte) {
            throw new \Exception('Rotekarte not found');
        }

        // Ensure boolean fields have default values
        $validated['formanlage_daten']['formen_gekennzeichnet'] = $validated['formanlage_daten']['formen_gekennzeichnet'] ?? false;
        $validated['formanlage_daten']['formen_an_anlage'] = $validated['formanlage_daten']['formen_an_anlage'] ?? false;
        $validated['formanlage_daten']['ausschussseparation'] = $validated['formanlage_daten']['ausschussseparation'] ?? false;

        // Calculate tolerance values if not already set
        $spektrometerDaten = $rotekarte->spektrometer_daten;
        if (!empty($spektrometerDaten['analysewerte'])) {
            $updated = false;
            foreach ($spektrometerDaten['analysewerte'] as &$analyse) {
                if (!isset($analyse['toleranz'])) {
                    $istWert = str_replace(',', '.', $analyse['istWert']);
                    $sollWert = $analyse['sollWert'];
                    
                    // Handle range values (e.g. "1140 - 1160")
                    if (strpos($sollWert, '-') !== false) {
                        $sollWertes = array_map('trim', explode('-', $sollWert));
                        if (count($sollWertes) === 2) {
                            $min = (float) str_replace(',', '.', $sollWertes[0]);
                            $max = (float) str_replace(',', '.', $sollWertes[1]);
                            
                            // Convert istWert to float after replacing comma with dot
                            $istWertFloat = (float) str_replace(',', '.', $istWert);
                            
                            // If value is below min, return negative deviation
                            if ($istWertFloat < $min) {
                                $analyse['toleranz'] = round($istWertFloat - $min, 3);
                            }
                            // If value is above max, return positive deviation
                            elseif ($istWertFloat > $max) {
                                $analyse['toleranz'] = round($istWertFloat - $max, 3);
                            }
                            // If value is within range, return 0
                            else {
                                $analyse['toleranz'] = 0.0;
                            }
                        }
                    }
                    // Handle single values
                    else {
                        $sollWert = str_replace(',', '.', str_replace(['≤', '≥'], '', $sollWert));
                        $istWert = str_replace(',', '.', $istWert);
                        $analyse['toleranz'] = round((float) $istWert - (float) $sollWert, 3);
                    }
                    $updated = true;
                }
            }
            if ($updated) {
                $rotekarte->spektrometer_daten = $spektrometerDaten;
                $rotekarte->save();
            }
        }

        $rotekarte->update([
            'formanlage_daten' => $validated['formanlage_daten'],
            'status' => 'formanlage_complete'
        ]);

        // Generate PDF
        $pdf = Pdf::loadView('pdf.rotekarte-analyse', [
            'rotekarte' => $rotekarte->fresh()
        ]);
        $pdfPath = storage_path('app/public/rotekarten/rotekarte-' . $rotekarte->id . '.pdf');
        $pdf->save($pdfPath);

        // Refresh model before sending emails
        $rotekarte = $rotekarte->fresh();
        $this->sendEmails($rotekarte);

        return redirect()->route('dashboard')
            ->with('success', 'Formanlage-Daten wurden erfolgreich zur Rotekarte Nr. ' . $rotekarte->id . ' hinzugefügt.');
    }

    private function generateRotekartePdf(Rotekarte $rotekarte)
    {
        // Berechne Toleranzwerte falls noch nicht vorhanden
        $spektrometerDaten = $rotekarte->spektrometer_daten;
        if (!empty($spektrometerDaten['analysewerte'])) {
            $updated = false;
            foreach ($spektrometerDaten['analysewerte'] as &$analyse) {
                if (!isset($analyse['toleranz'])) {
                    $analyse['toleranz'] = $this->calculateTolerance($analyse['istWert'], $analyse['sollWert']);
                    $updated = true;
                }
            }
            if ($updated) {
                $rotekarte->spektrometer_daten = $spektrometerDaten;
                $rotekarte->save();
            }
        }

        $pdf = Pdf::loadView('pdf.rotekarte-analyse', [
            'rotekarte' => $rotekarte
        ]);
        return $pdf;
    }

    private function calculateTolerance($istWert, $sollWert): ?float
    {
        // Wenn der Soll-Wert ein Bereich ist (z.B. "0.03 - 0.04")
        if (strpos($sollWert, '-') !== false) {
            $sollWertes = array_map('trim', explode('-', $sollWert));
            if (count($sollWertes) === 2) {
                $min = (float) str_replace(',', '.', $sollWertes[0]);
                $max = (float) str_replace(',', '.', $sollWertes[1]);
                
                // Convert istWert to float after replacing comma with dot
                $istWertFloat = (float) str_replace(',', '.', $istWert);
                
                // If value is below min, return negative deviation
                if ($istWertFloat < $min) {
                    return round($istWertFloat - $min, 3);
                }
                // If value is above max, return positive deviation
                elseif ($istWertFloat > $max) {
                    return round($istWertFloat - $max, 3);
                }
                // If value is within range, return 0
                else {
                    return 0.0;
                }
            }
        }
        // Wenn der Soll-Wert ein einzelner Wert ist
        else {
            $sollWert = str_replace(',', '.', str_replace(['≤', '≥'], '', $sollWert));
            $istWert = str_replace(',', '.', $istWert);
            return round((float) $istWert - (float) $sollWert, 3);
        }

        return null;
    }

    private function sendEmails(Rotekarte $rotekarte)
    {
        $abteilung = $rotekarte->formanlage_daten['abteilung'];

        switch ($abteilung) {
            case 'NG':
                // Send to QS-Kleinguss
                $qsEmails = EmailVerteiler::where('type', 'qs_kg')->get();
                foreach ($qsEmails as $email) {
                    Mail::to($email->email)
                        ->cc($email->is_cc ? $email->email : [])
                        ->send(new FormanlagCompleteMail($rotekarte));
                }

                // Send to KG-Nachbehandlung
                $nachbehandlungEmails = EmailVerteiler::where('type', 'kg_nachbehandlung')->get();
                foreach ($nachbehandlungEmails as $email) {
                    Mail::to($email->email)
                        ->cc($email->is_cc ? $email->email : [])
                        ->send(new FormanlagCompleteMail($rotekarte));
                }
                break;

            case 'GG':
            case 'HF':
                // Send to QS-Grossguss
                $qsEmails = EmailVerteiler::where('type', 'qs_gg')->get();
                foreach ($qsEmails as $email) {
                    Mail::to($email->email)
                        ->cc($email->is_cc ? $email->email : [])
                        ->send(new FormanlagCompleteMail($rotekarte));
                }

                // Send to GG-Nachbehandlung
                $nachbehandlungEmails = EmailVerteiler::where('type', 'gg_nachbehandlung')->get();
                foreach ($nachbehandlungEmails as $email) {
                    Mail::to($email->email)
                        ->cc($email->is_cc ? $email->email : [])
                        ->send(new FormanlagCompleteMail($rotekarte));
                }
                break;
        }
    }
}
