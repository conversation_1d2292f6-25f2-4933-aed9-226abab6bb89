<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class HBWTableController extends Controller
{
    public function index()
    {
        try {
            $hbwData = DB::table('haerte_werte')->first();
            $materials = $hbwData ? json_decode($hbwData->werte, true) : [];
            
            // Ensure all entries have an ID
            $updated = false;
            foreach ($materials as $key => $item) {
                if (!isset($item['id'])) {
                    $materials[$key]['id'] = uniqid();
                    $updated = true;
                }
            }
            
            // If we added IDs to any entries, update the database
            if ($updated && $hbwData) {
                DB::table('haerte_werte')->where('id', $hbwData->id)->update([
                    'werte' => json_encode($materials),
                    'updated_at' => now()
                ]);
            }

            // Formatiere die Daten für die Frontend-Darstellung
            $formattedMaterials = collect($materials)->groupBy('werkstoff')->map(function ($items, $werkstoff) {
                return [
                    'type' => $werkstoff,
                    'ranges' => $items->map(function ($item) {
                        return [
                            'id' => $item['id'],
                            'thickness' => $item['wanddicke'],
                            'min' => $item['toleranz_min'],
                            'max' => $item['toleranz_max']
                        ];
                    })->values()
                ];
            })->values();

            return Inertia::render('HBWTable/Index', [
                'initialMaterials' => $formattedMaterials
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler beim Laden der HBW-Tabelle: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Laden der Daten'
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'material_type' => 'required|string|max:255',
                'thickness_range' => 'required|string|max:255',
                'min_hbw' => 'required|integer|min:0',
                'max_hbw' => 'required|integer|gt:min_hbw',
            ]);

            $hbwData = DB::table('haerte_werte')->first();
            $materials = $hbwData ? json_decode($hbwData->werte, true) : [];

            // Erstelle den neuen Eintrag
            $newEntry = [
                'id' => uniqid(),
                'werkstoff' => $validated['material_type'],
                'wanddicke' => $validated['thickness_range'],
                'toleranz_min' => $validated['min_hbw'],
                'toleranz_max' => $validated['max_hbw']
            ];

            // Füge den neuen Eintrag zum Array hinzu
            $materials[] = $newEntry;

            // Speichere die aktualisierten Daten
            if ($hbwData) {
                DB::table('haerte_werte')->where('id', $hbwData->id)->update([
                    'werte' => json_encode($materials),
                    'updated_at' => now()
                ]);
            } else {
                DB::table('haerte_werte')->insert([
                    'werte' => json_encode([$newEntry]),
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Eintrag erfolgreich erstellt',
                'data' => $newEntry
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler beim Erstellen des HBW-Eintrags: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Erstellen des Eintrags'
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'material_type' => 'required|string|max:255',
                'thickness_range' => 'required|string|max:255',
                'min_hbw' => 'required|integer|min:0',
                'max_hbw' => 'required|integer|gt:min_hbw',
            ]);

            $hbwData = DB::table('haerte_werte')->first();
            if (!$hbwData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Keine HBW-Daten gefunden'
                ], 404);
            }

            $materials = json_decode($hbwData->werte, true);
            $updated = false;
            
            // Add debug logging
            \Log::info('Updating HBW entry', [
                'id' => $id,
                'validated' => $validated,
                'materials_count' => count($materials),
            ]);

            // Suche und aktualisiere den Eintrag
            foreach ($materials as $key => $item) {
                // Log each item ID for debugging
                \Log::info('Checking item', [
                    'item_id' => $item['id'] ?? 'no_id',
                    'comparing_with' => $id
                ]);
                
                if ((isset($item['id']) && $item['id'] == $id) || 
                    (isset($item['werkstoff']) && $item['werkstoff'] == $validated['material_type'] && 
                     isset($item['wanddicke']) && $item['wanddicke'] == $validated['thickness_range'])) {
                    
                    \Log::info('Found matching item', ['key' => $key]);
                    
                    $materials[$key] = [
                        'id' => $id,
                        'werkstoff' => $validated['material_type'],
                        'wanddicke' => $validated['thickness_range'],
                        'toleranz_min' => $validated['min_hbw'],
                        'toleranz_max' => $validated['max_hbw']
                    ];
                    $updated = true;
                    break;
                }
            }

            // If no existing entry was found but we have all the data needed, create a new one
            if (!$updated) {
                \Log::info('No matching item found, adding new entry');
                // Add a new entry with the given ID
                $materials[] = [
                    'id' => $id,
                    'werkstoff' => $validated['material_type'],
                    'wanddicke' => $validated['thickness_range'],
                    'toleranz_min' => $validated['min_hbw'],
                    'toleranz_max' => $validated['max_hbw']
                ];
                $updated = true;
            }

            DB::table('haerte_werte')->where('id', $hbwData->id)->update([
                'werte' => json_encode($materials),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Eintrag erfolgreich aktualisiert'
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler beim Aktualisieren des HBW-Eintrags: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Aktualisieren des Eintrags'
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $hbwData = DB::table('haerte_werte')->first();
            if (!$hbwData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Keine HBW-Daten gefunden'
                ], 404);
            }

            $materials = json_decode($hbwData->werte, true);
            $deleted = false;

            // Add debug logging
            \Log::info('Deleting HBW entry', [
                'id' => $id,
                'materials_count' => count($materials),
            ]);

            // Suche und lösche den Eintrag
            foreach ($materials as $key => $item) {
                // Log each item ID for debugging
                \Log::info('Checking item', [
                    'item_id' => $item['id'] ?? 'no_id',
                    'comparing_with' => $id
                ]);
                
                if (isset($item['id']) && $item['id'] == $id) {
                    \Log::info('Found matching item to delete', ['key' => $key]);
                    unset($materials[$key]);
                    // Reindexiere das Array
                    $materials = array_values($materials);
                    $deleted = true;
                    break;
                }
            }

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Eintrag nicht gefunden'
                ], 404);
            }

            DB::table('haerte_werte')->where('id', $hbwData->id)->update([
                'werte' => json_encode($materials),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Eintrag erfolgreich gelöscht'
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler beim Löschen des HBW-Eintrags: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Löschen des Eintrags'
            ], 500);
        }
    }

    /**
     * Get all HBW data for the modal component
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllData()
    {
        try {
            $hbwData = DB::table('haerte_werte')->first();
            $materials = $hbwData ? json_decode($hbwData->werte, true) : [];

            // Ensure all entries have an ID
            foreach ($materials as $key => $item) {
                if (!isset($item['id'])) {
                    $materials[$key]['id'] = uniqid();
                }
            }

            // Format data for the frontend
            $formattedMaterials = collect($materials)->groupBy('werkstoff')->map(function ($items, $werkstoff) {
                return [
                    'type' => $werkstoff,
                    'ranges' => $items->map(function ($item) {
                        return [
                            'thickness' => $item['wanddicke'],
                            'min' => $item['toleranz_min'],
                            'max' => $item['toleranz_max']
                        ];
                    })->values()
                ];
            })->values();

            return response()->json($formattedMaterials);
        } catch (\Exception $e) {
            Log::error('Fehler beim Abrufen der HBW-Daten: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
} 