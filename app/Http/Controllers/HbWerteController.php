<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\HbWerte;
use Illuminate\Http\JsonResponse;

class HbWerteController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            $hbWerte = HbWerte::first();
            
            if (!$hbWerte) {
                return response()->json([]);
            }

            $data = json_decode($hbWerte->werte, true);
            
            // Gruppiere die Daten nach Werkstoff
            $groupedData = collect($data)->groupBy('werkstoff')
                ->map(function ($ranges, $type) {
                    return [
                        'type' => $type,
                        'ranges' => $ranges->map(function ($range) {
                            return [
                                'thickness' => $range['wanddicke'],
                                'min' => $range['toleranz_min'],
                                'max' => $range['toleranz_max'],
                            ];
                        })->values()->all(),
                    ];
                })->values()->all();

            return response()->json($groupedData);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
} 