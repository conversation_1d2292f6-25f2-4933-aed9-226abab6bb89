<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SystemInfoController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            // Get username
            $username = getenv('USER')
                ?? getenv('USERNAME')
                ?? exec('whoami');

            // Get hostname
            $hostname = php_uname('n');

            // Log success
            Log::info('System Info Retrieved', [
                'username' => $username,
                'hostname' => $hostname
            ]);

            return response()->json([
                'username' => $username,
                'hostname' => $hostname
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('System Info Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Fehler beim Abrufen der Systeminformationen',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
