<?php

namespace App\Http\Controllers;

use App\Models\Pruefkatalog;
use App\Services\PruefkatalogMigrationService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class PruefkatalogController extends Controller
{
    public function index()
    {
        $pruefkatalogs = Pruefkatalog::orderBy('fehlercode')->get();
        return Inertia::render('Pruefkatalog/Index', [
            'pruefkatalogs' => $pruefkatalogs
        ]);
    }

    public function getFehlercodes()
    {
        $pruefkatalogs = Pruefkatalog::orderBy('fehlercode')->get();
        return response()->json($pruefkatalogs);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'fehlercode' => ['required', 'string', 'unique:pruefkatalogs'],
            'eisenmarke' => ['required', 'string'],
            'element' => ['required', 'string'],
            'absolutwerte' => ['required', 'string'],
            'teilesperren' => ['required', 'boolean'],
            'massnahmen' => ['required', 'string'],
            'bemerkungen' => ['nullable', 'string'],
            'grenzwert_typ' => ['nullable', 'string'],
            'grenzwert_richtung' => ['nullable', 'string'],
            'grenzwert_operator' => ['nullable', 'string'],
            'grenzwert_wert' => ['nullable', 'numeric'],
            'grenzwert_einheit' => ['nullable', 'string'],
        ]);

        $pruefkatalog = Pruefkatalog::create($validated);

        return response()->json([
            'message' => 'Prüfkatalog erfolgreich erstellt',
            'pruefkatalog' => $pruefkatalog
        ]);
    }

    public function update(Request $request, Pruefkatalog $pruefkatalog)
    {
        $validated = $request->validate([
            'fehlercode' => ['required', 'string', 'unique:pruefkatalogs,fehlercode,'.$pruefkatalog->id],
            'eisenmarke' => ['required', 'string'],
            'element' => ['required', 'string'],
            'absolutwerte' => ['required', 'string'],
            'teilesperren' => ['required', 'boolean'],
            'massnahmen' => ['required', 'string'],
            'bemerkungen' => ['nullable', 'string'],
            'grenzwert_typ' => ['nullable', 'string'],
            'grenzwert_richtung' => ['nullable', 'string'],
            'grenzwert_operator' => ['nullable', 'string'],
            'grenzwert_wert' => ['nullable', 'numeric'],
            'grenzwert_einheit' => ['nullable', 'string'],
        ]);

        // Explizit das JSON-Feld aktualisieren, basierend auf den strukturierten Feldern
        $validated['grenzwerte_json'] = json_encode([
            'typ' => $validated['grenzwert_typ'],
            'richtung' => $validated['grenzwert_richtung'],
            'operator' => $validated['grenzwert_operator'],
            'wert' => $validated['grenzwert_wert'],
            'einheit' => $validated['grenzwert_einheit'],
            'text' => $validated['absolutwerte']
        ]);

        $pruefkatalog->update($validated);

        return response()->json([
            'message' => 'Prüfkatalog erfolgreich aktualisiert',
            'pruefkatalog' => $pruefkatalog
        ]);
    }

    public function destroy(Pruefkatalog $pruefkatalog)
    {
        $pruefkatalog->delete();
        
        return redirect()->route('pruefkatalog.index')->with('success', 'Prüfkatalog erfolgreich gelöscht.');
    }
    
    /**
     * Zeigt die Migrationsoberfläche an
     */
    public function showMigration()
    {
        return Inertia::render('Pruefkatalog/Migration');
    }
    
    /**
     * Führt die Datenmigration aus
     */
    public function executeMigration(Request $request)
    {
        // Datenmigration ausführen
        $migrationService = app(PruefkatalogMigrationService::class);
        $result = $migrationService->migrateExistingData();
        
        return response()->json([
            'success' => true,
            'message' => 'Datenmigration abgeschlossen',
            'result' => $result
        ]);
    }
    
    /**
     * Zeigt die JSON-Korrektur-Oberfläche an
     */
    public function showFixJson()
    {
        return Inertia::render('Pruefkatalog/FixJson');
    }
    
    /**
     * Führt die JSON-Korrektur für alle Einträge aus
     */
    public function executeFixJson(Request $request)
    {
        $count = 0;
        $failed = 0;
        $total = Pruefkatalog::count();
        $examples = [];
        
        // Verarbeite alle Einträge in Chunks, um Speicherprobleme zu vermeiden
        Pruefkatalog::chunk(100, function($pruefkatalogs) use (&$count, &$failed, &$examples) {
            foreach ($pruefkatalogs as $pruefkatalog) {
                try {
                    // Erstelle das JSON-Objekt basierend auf den strukturierten Feldern
                    $json = [
                        'typ' => $pruefkatalog->grenzwert_typ ?: 'TEXT',
                        'richtung' => $pruefkatalog->grenzwert_richtung,
                        'operator' => $pruefkatalog->grenzwert_operator,
                        'wert' => $pruefkatalog->grenzwert_wert,
                        'einheit' => $pruefkatalog->grenzwert_einheit,
                        'text' => $pruefkatalog->absolutwerte
                    ];
                    
                    // Aktualisiere das JSON-Feld ohne die setters zu triggern
                    Pruefkatalog::where('id', $pruefkatalog->id)
                        ->update(['grenzwerte_json' => json_encode($json)]);
                    
                    // Speichere Beispiele für die ersten 10 aktualisierten Einträge
                    if (count($examples) < 10) {
                        $examples[] = [
                            'fehlercode' => $pruefkatalog->fehlercode,
                            'absolutwerte' => $pruefkatalog->absolutwerte,
                            'parsed' => $json
                        ];
                    }
                    
                    $count++;
                } catch (\Exception $e) {
                    $failed++;
                    \Illuminate\Support\Facades\Log::error('Fehler beim Korrigieren des JSON-Feldes', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
        
        return response()->json([
            'success' => true,
            'message' => 'JSON-Daten korrigiert',
            'result' => [
                'total' => $total,
                'success' => $count,
                'failed' => $failed,
                'examples' => $examples
            ]
        ]);
    }

    /**
     * Aktualisiert die strukturierten Felder basierend auf dem Absolutwerte-Feld
     */
    public function executeFixStructuredFields(Request $request)
    {
        $count = 0;
        $failed = 0;
        $total = Pruefkatalog::count();
        
        // Verarbeite alle Einträge in Chunks, um Speicherprobleme zu vermeiden
        Pruefkatalog::chunk(100, function($pruefkatalogs) use (&$count, &$failed) {
            foreach ($pruefkatalogs as $pruefkatalog) {
                try {
                    $absolutwerte = $pruefkatalog->absolutwerte;
                    $parsed = $pruefkatalog->parseAbsolutwerte($absolutwerte);
                    
                    if ($parsed) {
                        // Aktualisiere die strukturierten Felder basierend auf dem Parser
                        Pruefkatalog::where('id', $pruefkatalog->id)
                            ->update([
                                'grenzwert_typ' => $parsed['typ'],
                                'grenzwert_richtung' => $parsed['richtung'],
                                'grenzwert_operator' => $parsed['operator'],
                                'grenzwert_wert' => $parsed['wert'],
                                'grenzwert_einheit' => $parsed['einheit'],
                                'grenzwerte_json' => json_encode($parsed)
                            ]);
                        
                        $count++;
                    } else {
                        $failed++;
                        \Illuminate\Support\Facades\Log::warning('Fehler beim Parsen der Absolutwerte', [
                            'fehlercode' => $pruefkatalog->fehlercode,
                            'absolutwerte' => $absolutwerte
                        ]);
                    }
                } catch (\Exception $e) {
                    $failed++;
                    \Illuminate\Support\Facades\Log::error('Fehler beim Aktualisieren der strukturierten Felder', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Strukturierte Felder aktualisiert',
            'result' => [
                'total' => $total,
                'success' => $count,
                'failed' => $failed
            ]
        ]);
    }

    /**
     * Aktualisiert die strukturierten Felder basierend auf dem verbesserten Parser
     */
    public function executeReparseAbsolutwerte(Request $request)
    {
        $count = 0;
        $failed = 0;
        $success = 0;
        $total = Pruefkatalog::count();
        $results = [];
        
        // Verarbeite alle Einträge in Chunks, um Speicherprobleme zu vermeiden
        Pruefkatalog::chunk(100, function($pruefkatalogs) use (&$count, &$failed, &$success, &$results) {
            foreach ($pruefkatalogs as $pruefkatalog) {
                $count++;
                try {
                    $absolutwerte = $pruefkatalog->absolutwerte;
                    // Den verbesserten Parser verwenden
                    $parsed = $pruefkatalog->parseAbsolutwerte($absolutwerte);
                    
                    if ($parsed) {
                        // Nur aktualisieren, wenn der Typ nicht TEXT ist oder wenn wir Werte haben
                        $shouldUpdate = $parsed['typ'] !== 'TEXT' || 
                                       ($parsed['typ'] === 'TEXT' && ($parsed['wert'] !== null || 
                                                                    $parsed['operator'] !== null));
                                       
                        if ($shouldUpdate) {
                            // Aktualisiere die strukturierten Felder basierend auf dem Parser
                            Pruefkatalog::where('id', $pruefkatalog->id)
                                ->update([
                                    'grenzwert_typ' => $parsed['typ'],
                                    'grenzwert_richtung' => $parsed['richtung'],
                                    'grenzwert_operator' => $parsed['operator'],
                                    'grenzwert_wert' => $parsed['wert'],
                                    'grenzwert_einheit' => $parsed['einheit'],
                                    'grenzwerte_json' => json_encode($parsed)
                                ]);
                            
                            $success++;
                            
                            // Speichere die ersten 10 erfolgreichen Updates für die Rückmeldung
                            if (count($results) < 10) {
                                $results[] = [
                                    'fehlercode' => $pruefkatalog->fehlercode,
                                    'absolutwerte' => $absolutwerte,
                                    'parsed' => $parsed
                                ];
                            }
                        } else {
                            $failed++;
                        }
                    } else {
                        $failed++;
                        \Illuminate\Support\Facades\Log::warning('Fehler beim Parsen der Absolutwerte', [
                            'fehlercode' => $pruefkatalog->fehlercode,
                            'absolutwerte' => $absolutwerte
                        ]);
                    }
                } catch (\Exception $e) {
                    $failed++;
                    \Illuminate\Support\Facades\Log::error('Fehler beim Aktualisieren der strukturierten Felder', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Strukturierte Felder mit verbessertem Parser aktualisiert',
            'result' => [
                'total' => $total,
                'success' => $success,
                'failed' => $failed,
                'examples' => $results
            ]
        ]);
    }

    /**
     * Vollständige Neusynchronisierung aller Felder durchführen
     */
    public function executeFullResync(Request $request)
    {
        $count = 0;
        $failed = 0;
        $total = Pruefkatalog::count();
        $examples = [];
        
        // Verarbeite alle Einträge in Chunks, um Speicherprobleme zu vermeiden
        Pruefkatalog::chunk(100, function($pruefkatalogs) use (&$count, &$failed, &$examples) {
            foreach ($pruefkatalogs as $pruefkatalog) {
                try {
                    // 1. Originaltext speichern
                    $absolutwerte = $pruefkatalog->absolutwerte;
                    
                    // 2. Neu parsen
                    $parsed = $pruefkatalog->parseAbsolutwerte($absolutwerte);
                    
                    // 3. Direct-Update ohne Model-Hooks (um Endlosschleifen zu vermeiden)
                    DB::table('pruefkatalogs')
                        ->where('id', $pruefkatalog->id)
                        ->update([
                            'grenzwert_typ' => $parsed['typ'],
                            'grenzwert_richtung' => $parsed['richtung'],
                            'grenzwert_operator' => $parsed['operator'],
                            'grenzwert_wert' => $parsed['wert'],
                            'grenzwert_einheit' => $parsed['einheit'],
                            'grenzwerte_json' => json_encode($parsed)
                        ]);
                    
                    // Beispiele sammeln
                    if (count($examples) < 10) {
                        $examples[] = [
                            'fehlercode' => $pruefkatalog->fehlercode,
                            'absolutwerte' => $absolutwerte,
                            'parsed' => $parsed
                        ];
                    }
                    
                    $count++;
                } catch (\Exception $e) {
                    $failed++;
                    \Illuminate\Support\Facades\Log::error('Fehler bei der vollständigen Resynchronisierung', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Vollständige Resynchronisierung abgeschlossen',
            'result' => [
                'total' => $total,
                'success' => $count,
                'failed' => $failed,
                'examples' => $examples
            ]
        ]);
    }
} 