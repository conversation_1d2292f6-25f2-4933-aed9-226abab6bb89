<?php

namespace App\Http\Controllers;

use App\Models\EmailVerteiler;
use App\Models\Sollvorgaben;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\ExcelData;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ExcelDataImport;

class EinstellungenController extends Controller
{
    public function index()
    {
        $qsKgEmails = EmailVerteiler::where('type', 'qs_kg')->get();
        $qsGgEmails = EmailVerteiler::where('type', 'qs_gg')->get();
        $hfEmails = EmailVerteiler::where('type', 'hf')->get();
        $ggEmails = EmailVerteiler::where('type', 'gg')->get();
        $kgEmails = EmailVerteiler::where('type', 'kg')->get();
        $ggNachbehandlungEmails = EmailVerteiler::where('type', 'gg_nachbehandlung')->get();
        $kgNachbehandlungEmails = EmailVerteiler::where('type', 'kg_nachbehandlung')->get();
        $abnahmebeauftragterEmails = EmailVerteiler::where('type', 'abnahmebeauftragter')->get();
        $abnahmebeauftragterGgEmails = EmailVerteiler::where('type', 'abnahmebeauftragter_gg')->get();
        $statistikEmails = EmailVerteiler::where('type', 'statistik')->get();
        $managementEmails = EmailVerteiler::where('type', 'management')->get();

        $excelData = ExcelData::all();
        $lastSync = ExcelData::max('created_at');
        $sollvorgaben = Sollvorgaben::all();

        return Inertia::render('Einstellungen/Index', [
            'qsKgEmails' => $qsKgEmails,
            'qsGgEmails' => $qsGgEmails,
            'hfEmails' => $hfEmails,
            'ggEmails' => $ggEmails,
            'kgEmails' => $kgEmails,
            'ggNachbehandlungEmails' => $ggNachbehandlungEmails,
            'kgNachbehandlungEmails' => $kgNachbehandlungEmails,
            'abnahmebeauftragterEmails' => $abnahmebeauftragterEmails,
            'abnahmebeauftragterGgEmails' => $abnahmebeauftragterGgEmails,
            'statistikEmails' => $statistikEmails,
            'managementEmails' => $managementEmails,
            'excelData' => $excelData,
            'lastSync' => $lastSync,
            'sollvorgaben' => $sollvorgaben
        ]);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:qs_kg,qs_gg,hf,gg,kg,gg_nachbehandlung,kg_nachbehandlung,abnahmebeauftragter,abnahmebeauftragter_gg,statistik,management',
                'emails' => 'required|array',
                'emails.*.email' => 'required|email',
                'emails.*.is_cc' => 'required|boolean'
            ]);

            // Delete existing entries for this type
            EmailVerteiler::where('type', $validated['type'])->delete();

            // Create new entries
            foreach ($validated['emails'] as $email) {
                EmailVerteiler::create([
                    'type' => $validated['type'],
                    'email' => $email['email'],
                    'is_cc' => $email['is_cc']
                ]);
            }

            return back()->with('success', 'E-Mail-Verteiler wurde erfolgreich aktualisiert');
        } catch (\Exception $e) {
            return back()->with('error', 'Fehler beim Speichern des E-Mail-Verteilers');
        }
    }

    public function importExcel(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        $file = $request->file('excel_file');
        Excel::import(new ExcelDataImport($file->getClientOriginalName()), $file);

        return redirect()->back()->with('message', 'Excel-Datei erfolgreich importiert.');
    }

    public function destroyExcel()
    {
        ExcelData::truncate();
        return redirect()->back()->with('message', 'Alle Excel-Daten wurden erfolgreich gelöscht.');
    }

    public function destroyEmail($id)
    {
        try {
            $email = EmailVerteiler::findOrFail($id);
            $email->delete();
            return back()->with('success', 'E-Mail-Adresse wurde erfolgreich gelöscht');
        } catch (\Exception $e) {
            return back()->with('error', 'Fehler beim Löschen der E-Mail-Adresse');
        }
    }
}
