<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class StatistikController extends Controller
{
    public function index(Request $request)
    {
        // Zeitraum-Filter (Standard: letzte 30 Tage)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Basis-Query mit Zeitraum-Filter
        $baseQuery = Rotekarte::whereBetween('created_at', [$startDate, $endDate]);
        
        // 1. Gesamtstatistiken
        $gesamtStatistiken = $this->getGesamtStatistiken($baseQuery);
        
        // 2. Status-Verteilung
        $statusVerteilung = $this->getStatusVerteilung($baseQuery);
        
        // 3. Abteilungsanalyse
        $abteilungsAnalyse = $this->getAbteilungsAnalyse($baseQuery);
        
        // 4. Zeitbasierte Trends
        $zeitTrends = $this->getZeitTrends($startDate, $endDate);
        
        // 5. Qualitätsmetriken
        $qualitaetsMetriken = $this->getQualitaetsMetriken($baseQuery);
        
        // 6. Durchlaufzeiten (mit Paginierung)
        $durchlaufzeiten = $this->getDurchlaufzeiten($baseQuery, $request);
        
        // 7. Top Problembereiche
        $problembereiche = $this->getProblembereiche($baseQuery);
        
        return Inertia::render('Statistik/Index', [
            'statistiken' => [
                'gesamt' => $gesamtStatistiken,
                'status' => $statusVerteilung,
                'abteilungen' => $abteilungsAnalyse,
                'trends' => $zeitTrends,
                'qualitaet' => $qualitaetsMetriken,
                'durchlaufzeiten' => $durchlaufzeiten,
                'problembereiche' => $problembereiche,
            ],
            'filter' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]
        ]);
    }
    
    private function getGesamtStatistiken($baseQuery)
    {
        $total = $baseQuery->count();
        $heute = Rotekarte::whereDate('created_at', Carbon::today())->count();
        $dieseWoche = Rotekarte::whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ])->count();
        $dieserMonat = Rotekarte::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
            
        return [
            'total' => $total,
            'heute' => $heute,
            'diese_woche' => $dieseWoche,
            'dieser_monat' => $dieserMonat,
            'durchschnitt_pro_tag' => $total > 0 ? round($total / 30, 1) : 0,
        ];
    }
    
    private function getStatusVerteilung($baseQuery)
    {
        // Hole alle Rotekarten und verarbeite sie in PHP
        $rotekarten = $baseQuery->get();

        $statusCounts = [
            'Offen' => 0,
            'In Bearbeitung' => 0,
            'Abgeschlossen' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if (isset($statusCounts[$status])) {
                $statusCounts[$status]++;
            } else {
                $statusCounts['Offen']++;
            }
        }

        // Konvertiere zu dem erwarteten Format
        $result = [];
        foreach ($statusCounts as $status => $anzahl) {
            if ($anzahl > 0) { // Nur Statuses mit Daten anzeigen
                $result[] = [
                    'status' => $status,
                    'anzahl' => $anzahl,
                ];
            }
        }

        return collect($result);
    }
    
    private function getAbteilungsAnalyse($baseQuery)
    {
        // Hole alle Rotekarten und verarbeite sie in PHP
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();

        $abteilungsCounts = [
            'NG' => 0,
            'GG' => 0,
            'HF' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;
            $abteilung = null;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['abteilung'])) {
                $abteilung = $spektrometerDaten['abteilung'];
            } elseif (is_string($spektrometerDaten)) {
                $decoded = json_decode($spektrometerDaten, true);
                if (is_array($decoded) && isset($decoded['abteilung'])) {
                    $abteilung = $decoded['abteilung'];
                }
            }

            if (isset($abteilungsCounts[$abteilung])) {
                $abteilungsCounts[$abteilung]++;
            }
        }

        // Konvertiere zu dem erwarteten Format
        $result = [];
        foreach ($abteilungsCounts as $code => $anzahl) {
            if ($anzahl > 0) { // Nur Abteilungen mit Daten anzeigen
                $abteilungName = match($code) {
                    'NG' => 'Nassgussanlage',
                    'GG' => 'Großguss',
                    'HF' => 'Hochfrequenz',
                    default => $code ?: 'Unbekannt'
                };

                $result[] = [
                    'abteilung' => $abteilungName,
                    'code' => $code,
                    'anzahl' => $anzahl,
                ];
            }
        }

        return collect($result);
    }
    
    private function getZeitTrends($startDate, $endDate)
    {
        $trends = Rotekarte::select(
            DB::raw('DATE(created_at) as datum'),
            DB::raw('COUNT(*) as anzahl')
        )
        ->whereBetween('created_at', [$startDate, $endDate])
        ->groupBy('datum')
        ->orderBy('datum')
        ->get();

        // Fülle fehlende Tage mit 0 auf
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $trendData = [];

        // Erstelle Array für jeden Tag im Zeitraum
        for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $found = $trends->firstWhere('datum', $dateStr);
            $trendData[] = [
                'datum' => $dateStr,
                'anzahl' => $found ? $found->anzahl : 0,
                'datum_formatted' => $date->format('d.m.Y'),
            ];
        }

        return $trendData;
    }
    
    private function getQualitaetsMetriken($baseQuery)
    {
        $rotekarten = $baseQuery->get();
        $total = $rotekarten->count();

        $abgeschlossen = 0;
        $inBearbeitung = 0;
        $bearbeitungszeiten = [];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if ($status === 'Abgeschlossen') {
                $abgeschlossen++;
                // Berechne Bearbeitungszeit
                if ($rotekarte->updated_at && $rotekarte->created_at) {
                    $bearbeitungszeiten[] = $rotekarte->created_at->diffInHours($rotekarte->updated_at);
                }
            } elseif ($status === 'In Bearbeitung') {
                $inBearbeitung++;
            }
        }

        $abschlussRate = $total > 0 ? round(($abgeschlossen / $total) * 100, 1) : 0;
        $avgBearbeitungszeit = count($bearbeitungszeiten) > 0 ? round(array_sum($bearbeitungszeiten) / count($bearbeitungszeiten), 1) : 0;

        return [
            'abschluss_rate' => $abschlussRate,
            'abgeschlossen' => $abgeschlossen,
            'in_bearbeitung' => $inBearbeitung,
            'offen' => $total - $abgeschlossen - $inBearbeitung,
            'avg_bearbeitungszeit_stunden' => $avgBearbeitungszeit,
        ];
    }
    
    private function getDurchlaufzeiten($baseQuery, $request)
    {
        $rotekarten = $baseQuery->whereNotNull('updated_at')->get();
        $durchlaufzeiten = [];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if ($status === 'Abgeschlossen' && $rotekarte->created_at && $rotekarte->updated_at) {
                $durchlaufzeitStunden = $rotekarte->created_at->diffInHours($rotekarte->updated_at);

                $durchlaufzeiten[] = [
                    'rotekarte_id' => $rotekarte->id,
                    'durchlaufzeit_stunden' => $durchlaufzeitStunden,
                    'durchlaufzeit_tage' => round($durchlaufzeitStunden / 24, 1),
                    'erstellt_am' => $rotekarte->created_at->format('d.m.Y H:i'),
                    'abgeschlossen_am' => $rotekarte->updated_at->format('d.m.Y H:i'),
                ];
            }
        }

        // Sortiere nach Durchlaufzeit (absteigend)
        usort($durchlaufzeiten, function($a, $b) {
            return $b['durchlaufzeit_stunden'] <=> $a['durchlaufzeit_stunden'];
        });

        // Paginierung
        $page = $request->get('durchlaufzeit_page', 1);
        $perPage = 10;
        $total = count($durchlaufzeiten);
        $offset = ($page - 1) * $perPage;
        $items = array_slice($durchlaufzeiten, $offset, $perPage);

        return [
            'data' => $items,
            'current_page' => (int) $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total),
        ];
    }
    
    private function getProblembereiche($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('qs_daten')->get();
        $problembereiche = [
            'NG' => 0,
            'GG' => 0,
            'HF' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            // Nur nicht-abgeschlossene Karten sind "Probleme"
            if ($status !== 'Abgeschlossen') {
                $spektrometerDaten = $rotekarte->spektrometer_daten;
                $abteilung = null;

                if (is_array($spektrometerDaten) && isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                } elseif (is_string($spektrometerDaten)) {
                    $decoded = json_decode($spektrometerDaten, true);
                    if (is_array($decoded) && isset($decoded['abteilung'])) {
                        $abteilung = $decoded['abteilung'];
                    }
                }

                if (isset($problembereiche[$abteilung])) {
                    $problembereiche[$abteilung]++;
                }
            }
        }

        // Konvertiere zu dem erwarteten Format und sortiere
        $result = [];
        foreach ($problembereiche as $code => $anzahl) {
            if ($anzahl > 0) {
                $abteilungName = match($code) {
                    'NG' => 'Nassgussanlage',
                    'GG' => 'Großguss',
                    'HF' => 'Hochfrequenz',
                    default => $code ?: 'Unbekannt'
                };

                $result[] = [
                    'bereich' => $abteilungName,
                    'anzahl_probleme' => $anzahl,
                    'code' => $code,
                ];
            }
        }

        // Sortiere nach Anzahl der Probleme (absteigend)
        usort($result, function($a, $b) {
            return $b['anzahl_probleme'] <=> $a['anzahl_probleme'];
        });

        return collect($result);
    }
}
