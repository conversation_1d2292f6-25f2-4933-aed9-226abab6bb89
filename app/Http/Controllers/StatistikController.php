<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use App\Models\EmailVerteiler;
use App\Mail\StatistikReportMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class StatistikController extends Controller
{
    public function index(Request $request)
    {
        // Zeitraum-Filter (Standard: letzte 30 Tage)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Basis-Query mit Zeitraum-Filter
        $baseQuery = Rotekarte::whereBetween('created_at', [$startDate, $endDate]);
        
        // 1. Gesamtstatistiken
        $gesamtStatistiken = $this->getGesamtStatistiken($baseQuery);
        
        // 2. Status-Verteilung
        $statusVerteilung = $this->getStatusVerteilung($baseQuery);
        
        // 3. Abteilungsanalyse
        $abteilungsAnalyse = $this->getAbteilungsAnalyse($baseQuery);
        
        // 4. Zeitbasierte Trends
        $zeitTrends = $this->getZeitTrends($startDate, $endDate);
        
        // 5. Qualitätsmetriken
        $qualitaetsMetriken = $this->getQualitaetsMetriken($baseQuery);
        
        // 6. Durchlaufzeiten (mit Paginierung)
        $durchlaufzeiten = $this->getDurchlaufzeiten($baseQuery, $request);
        
        // 7. Top Problembereiche
        $problembereiche = $this->getProblembereiche($baseQuery);

        // 8. Analyseabweichungen
        $analyseabweichungen = $this->getAnalyseabweichungen($baseQuery);

        // 9. Eisenmarken-Analyse
        $eisenmarkenAnalyse = $this->getEisenmarkenAnalyse($baseQuery);

        // 10. Schicht-Analyse
        $schichtAnalyse = $this->getSchichtAnalyse($baseQuery);

        // 11. Mitarbeiter-Analyse
        $mitarbeiterAnalyse = $this->getMitarbeiterAnalyse($baseQuery);

        // 12. Fehlercode-Analyse
        $fehlercodeAnalyse = $this->getFehlercodeAnalyse($baseQuery);

        return Inertia::render('Statistik/Index', [
            'statistiken' => [
                'gesamt' => $gesamtStatistiken,
                'status' => $statusVerteilung,
                'abteilungen' => $abteilungsAnalyse,
                'trends' => $zeitTrends,
                'qualitaet' => $qualitaetsMetriken,
                'durchlaufzeiten' => $durchlaufzeiten,
                'problembereiche' => $problembereiche,
                'analyseabweichungen' => $analyseabweichungen,
                'eisenmarken' => $eisenmarkenAnalyse,
                'schichten' => $schichtAnalyse,
                'mitarbeiter' => $mitarbeiterAnalyse,
                'fehlercodes' => $fehlercodeAnalyse,
            ],
            'filter' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'sort_field' => $request->get('sort_field', 'durchlaufzeit_stunden'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
            ]
        ]);
    }
    
    private function getGesamtStatistiken($baseQuery)
    {
        $total = $baseQuery->count();
        $heute = Rotekarte::whereDate('created_at', Carbon::today())->count();
        $dieseWoche = Rotekarte::whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ])->count();
        $dieserMonat = Rotekarte::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
            
        return [
            'total' => $total,
            'heute' => $heute,
            'diese_woche' => $dieseWoche,
            'dieser_monat' => $dieserMonat,
            'durchschnitt_pro_tag' => $total > 0 ? round($total / 30, 1) : 0,
        ];
    }
    
    private function getStatusVerteilung($baseQuery)
    {
        // Hole alle Rotekarten und verarbeite sie in PHP
        $rotekarten = $baseQuery->get();

        $statusCounts = [
            'Offen' => 0,
            'In Bearbeitung' => 0,
            'Abgeschlossen' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if (isset($statusCounts[$status])) {
                $statusCounts[$status]++;
            } else {
                $statusCounts['Offen']++;
            }
        }

        // Konvertiere zu dem erwarteten Format
        $result = [];
        foreach ($statusCounts as $status => $anzahl) {
            if ($anzahl > 0) { // Nur Statuses mit Daten anzeigen
                $result[] = [
                    'status' => $status,
                    'anzahl' => $anzahl,
                ];
            }
        }

        return collect($result);
    }
    
    private function getAbteilungsAnalyse($baseQuery)
    {
        // Hole alle Rotekarten und verarbeite sie in PHP
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();

        $abteilungsCounts = [
            'NG' => 0,
            'GG' => 0,
            'HF' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;
            $abteilung = null;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['abteilung'])) {
                $abteilung = $spektrometerDaten['abteilung'];
            } elseif (is_string($spektrometerDaten)) {
                $decoded = json_decode($spektrometerDaten, true);
                if (is_array($decoded) && isset($decoded['abteilung'])) {
                    $abteilung = $decoded['abteilung'];
                }
            }

            if (isset($abteilungsCounts[$abteilung])) {
                $abteilungsCounts[$abteilung]++;
            }
        }

        // Konvertiere zu dem erwarteten Format
        $result = [];
        foreach ($abteilungsCounts as $code => $anzahl) {
            if ($anzahl > 0) { // Nur Abteilungen mit Daten anzeigen
                $abteilungName = match($code) {
                    'NG' => 'Nassgussanlage',
                    'GG' => 'Großguss',
                    'HF' => 'Hochfrequenz',
                    default => $code ?: 'Unbekannt'
                };

                $result[] = [
                    'abteilung' => $abteilungName,
                    'code' => $code,
                    'anzahl' => $anzahl,
                ];
            }
        }

        return collect($result);
    }
    
    private function getZeitTrends($startDate, $endDate)
    {
        $trends = Rotekarte::select(
            DB::raw('DATE(created_at) as datum'),
            DB::raw('COUNT(*) as anzahl')
        )
        ->whereBetween('created_at', [$startDate, $endDate])
        ->groupBy('datum')
        ->orderBy('datum')
        ->get();

        // Fülle fehlende Tage mit 0 auf
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $trendData = [];

        // Erstelle Array für jeden Tag im Zeitraum
        for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $found = $trends->firstWhere('datum', $dateStr);
            $trendData[] = [
                'datum' => $dateStr,
                'anzahl' => $found ? $found->anzahl : 0,
                'datum_formatted' => $date->format('d.m.Y'),
            ];
        }

        return $trendData;
    }
    
    private function getQualitaetsMetriken($baseQuery)
    {
        $rotekarten = $baseQuery->get();
        $total = $rotekarten->count();

        $abgeschlossen = 0;
        $inBearbeitung = 0;
        $bearbeitungszeiten = [];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if ($status === 'Abgeschlossen') {
                $abgeschlossen++;
                // Berechne Bearbeitungszeit
                if ($rotekarte->updated_at && $rotekarte->created_at) {
                    $bearbeitungszeiten[] = $rotekarte->created_at->diffInHours($rotekarte->updated_at);
                }
            } elseif ($status === 'In Bearbeitung') {
                $inBearbeitung++;
            }
        }

        $abschlussRate = $total > 0 ? round(($abgeschlossen / $total) * 100, 1) : 0;
        $avgBearbeitungszeit = count($bearbeitungszeiten) > 0 ? round(array_sum($bearbeitungszeiten) / count($bearbeitungszeiten), 1) : 0;

        return [
            'abschluss_rate' => $abschlussRate,
            'abgeschlossen' => $abgeschlossen,
            'in_bearbeitung' => $inBearbeitung,
            'offen' => $total - $abgeschlossen - $inBearbeitung,
            'avg_bearbeitungszeit_stunden' => $avgBearbeitungszeit,
        ];
    }
    
    private function getDurchlaufzeiten($baseQuery, $request)
    {
        $rotekarten = $baseQuery->whereNotNull('updated_at')->get();
        $durchlaufzeiten = [];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            if ($status === 'Abgeschlossen' && $rotekarte->created_at && $rotekarte->updated_at) {
                $durchlaufzeitStunden = $rotekarte->created_at->diffInHours($rotekarte->updated_at);

                $durchlaufzeiten[] = [
                    'rotekarte_id' => $rotekarte->id,
                    'durchlaufzeit_stunden' => $durchlaufzeitStunden,
                    'durchlaufzeit_tage' => round($durchlaufzeitStunden / 24, 1),
                    'erstellt_am' => $rotekarte->created_at->format('d.m.Y H:i'),
                    'abgeschlossen_am' => $rotekarte->updated_at->format('d.m.Y H:i'),
                ];
            }
        }

        // Sortierung anwenden
        $sortField = $request->get('sort_field', 'durchlaufzeit_stunden');
        $sortDirection = $request->get('sort_direction', 'desc');

        usort($durchlaufzeiten, function($a, $b) use ($sortField, $sortDirection) {
            $valueA = $a[$sortField] ?? 0;
            $valueB = $b[$sortField] ?? 0;

            // Spezielle Behandlung für Datum-Strings
            if ($sortField === 'created_at' || $sortField === 'updated_at') {
                $valueA = strtotime($a['erstellt_am'] ?? '');
                $valueB = strtotime($b['erstellt_am'] ?? '');
                if ($sortField === 'updated_at') {
                    $valueA = strtotime($a['abgeschlossen_am'] ?? '');
                    $valueB = strtotime($b['abgeschlossen_am'] ?? '');
                }
            }

            $comparison = $valueA <=> $valueB;
            return $sortDirection === 'desc' ? -$comparison : $comparison;
        });

        // Paginierung
        $page = $request->get('durchlaufzeit_page', 1);
        $perPage = 10;
        $total = count($durchlaufzeiten);
        $offset = ($page - 1) * $perPage;
        $items = array_slice($durchlaufzeiten, $offset, $perPage);

        return [
            'data' => $items,
            'current_page' => (int) $page,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total),
        ];
    }
    
    private function getProblembereiche($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('qs_daten')->get();
        $problembereiche = [
            'NG' => 0,
            'GG' => 0,
            'HF' => 0,
        ];

        foreach ($rotekarten as $rotekarte) {
            $qsDaten = $rotekarte->qs_daten;
            $status = 'Offen'; // Default

            if (is_array($qsDaten) && isset($qsDaten['status'])) {
                $status = $qsDaten['status'];
            } elseif (is_string($qsDaten)) {
                $decoded = json_decode($qsDaten, true);
                if (is_array($decoded) && isset($decoded['status'])) {
                    $status = $decoded['status'];
                }
            }

            // Nur nicht-abgeschlossene Karten sind "Probleme"
            if ($status !== 'Abgeschlossen') {
                $spektrometerDaten = $rotekarte->spektrometer_daten;
                $abteilung = null;

                if (is_array($spektrometerDaten) && isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                } elseif (is_string($spektrometerDaten)) {
                    $decoded = json_decode($spektrometerDaten, true);
                    if (is_array($decoded) && isset($decoded['abteilung'])) {
                        $abteilung = $decoded['abteilung'];
                    }
                }

                if (isset($problembereiche[$abteilung])) {
                    $problembereiche[$abteilung]++;
                }
            }
        }

        // Konvertiere zu dem erwarteten Format und sortiere
        $result = [];
        foreach ($problembereiche as $code => $anzahl) {
            if ($anzahl > 0) {
                $abteilungName = match($code) {
                    'NG' => 'Nassgussanlage',
                    'GG' => 'Großguss',
                    'HF' => 'Hochfrequenz',
                    default => $code ?: 'Unbekannt'
                };

                $result[] = [
                    'bereich' => $abteilungName,
                    'anzahl_probleme' => $anzahl,
                    'code' => $code,
                ];
            }
        }

        // Sortiere nach Anzahl der Probleme (absteigend)
        usort($result, function($a, $b) {
            return $b['anzahl_probleme'] <=> $a['anzahl_probleme'];
        });

        return collect($result);
    }

    private function getAnalyseabweichungen($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();
        $abweichungen = [];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['analysewerte'])) {
                $analysewerte = $spektrometerDaten['analysewerte'];

                foreach ($analysewerte as $analyse) {
                    if (isset($analyse['element'], $analyse['istWert'], $analyse['sollWert'])) {
                        $istWert = (float) $analyse['istWert'];
                        $sollWert = (float) $analyse['sollWert'];
                        $abweichung = abs($istWert - $sollWert);

                        // Nur signifikante Abweichungen (> 5% oder > 0.1 absolute Differenz)
                        $prozentAbweichung = $sollWert != 0 ? ($abweichung / $sollWert) * 100 : 0;

                        if ($prozentAbweichung > 5 || $abweichung > 0.1) {
                            $element = $analyse['element'];

                            if (!isset($abweichungen[$element])) {
                                $abweichungen[$element] = [
                                    'element' => $element,
                                    'anzahl_abweichungen' => 0,
                                    'durchschnitt_abweichung' => 0,
                                    'max_abweichung' => 0,
                                    'rotekarten_ids' => []
                                ];
                            }

                            $abweichungen[$element]['anzahl_abweichungen']++;
                            $abweichungen[$element]['max_abweichung'] = max($abweichungen[$element]['max_abweichung'], $prozentAbweichung);
                            $abweichungen[$element]['rotekarten_ids'][] = $rotekarte->id;
                        }
                    }
                }
            }
        }

        // Berechne Durchschnittswerte und sortiere
        foreach ($abweichungen as &$abweichung) {
            $abweichung['durchschnitt_abweichung'] = round($abweichung['max_abweichung'] / $abweichung['anzahl_abweichungen'], 2);
        }

        // Sortiere nach Anzahl der Abweichungen
        uasort($abweichungen, function($a, $b) {
            return $b['anzahl_abweichungen'] <=> $a['anzahl_abweichungen'];
        });

        return collect(array_values($abweichungen));
    }

    private function getEisenmarkenAnalyse($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();
        $eisenmarken = [];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['eisenmarke'])) {
                $eisenmarke = $spektrometerDaten['eisenmarke'];

                if (!isset($eisenmarken[$eisenmarke])) {
                    $eisenmarken[$eisenmarke] = [
                        'eisenmarke' => $eisenmarke,
                        'anzahl_rotekarten' => 0,
                        'abteilungen' => [],
                        'letzte_rotekarte' => null
                    ];
                }

                $eisenmarken[$eisenmarke]['anzahl_rotekarten']++;
                $eisenmarken[$eisenmarke]['letzte_rotekarte'] = $rotekarte->created_at->format('d.m.Y');

                // Abteilung hinzufügen
                if (isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                    if (!isset($eisenmarken[$eisenmarke]['abteilungen'][$abteilung])) {
                        $eisenmarken[$eisenmarke]['abteilungen'][$abteilung] = 0;
                    }
                    $eisenmarken[$eisenmarke]['abteilungen'][$abteilung]++;
                }
            }
        }

        // Sortiere nach Anzahl der Rotekarten
        uasort($eisenmarken, function($a, $b) {
            return $b['anzahl_rotekarten'] <=> $a['anzahl_rotekarten'];
        });

        return collect(array_values($eisenmarken));
    }

    private function getSchichtAnalyse($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();
        $schichten = [];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['uhrzeit'])) {
                $uhrzeit = $spektrometerDaten['uhrzeit'];
                $stunde = (int) substr($uhrzeit, 0, 2);

                // Schichtzeiten definieren
                $schicht = '';
                if ($stunde >= 6 && $stunde < 14) {
                    $schicht = 'Frühschicht (06:00-14:00)';
                } elseif ($stunde >= 14 && $stunde < 22) {
                    $schicht = 'Spätschicht (14:00-22:00)';
                } else {
                    $schicht = 'Nachtschicht (22:00-06:00)';
                }

                if (!isset($schichten[$schicht])) {
                    $schichten[$schicht] = [
                        'schicht' => $schicht,
                        'anzahl_rotekarten' => 0,
                        'abteilungen' => [],
                        'mitarbeiter' => []
                    ];
                }

                $schichten[$schicht]['anzahl_rotekarten']++;

                // Abteilung hinzufügen
                if (isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                    if (!isset($schichten[$schicht]['abteilungen'][$abteilung])) {
                        $schichten[$schicht]['abteilungen'][$abteilung] = 0;
                    }
                    $schichten[$schicht]['abteilungen'][$abteilung]++;
                }

                // Mitarbeiter hinzufügen
                if (isset($spektrometerDaten['name'])) {
                    $mitarbeiter = $spektrometerDaten['name'];
                    if (!isset($schichten[$schicht]['mitarbeiter'][$mitarbeiter])) {
                        $schichten[$schicht]['mitarbeiter'][$mitarbeiter] = 0;
                    }
                    $schichten[$schicht]['mitarbeiter'][$mitarbeiter]++;
                }
            }
        }

        // Sortiere nach Anzahl der Rotekarten
        uasort($schichten, function($a, $b) {
            return $b['anzahl_rotekarten'] <=> $a['anzahl_rotekarten'];
        });

        return collect(array_values($schichten));
    }

    private function getMitarbeiterAnalyse($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();
        $mitarbeiter = [];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['name'])) {
                $name = $spektrometerDaten['name'];

                if (!isset($mitarbeiter[$name])) {
                    $mitarbeiter[$name] = [
                        'name' => $name,
                        'anzahl_rotekarten' => 0,
                        'abteilungen' => [],
                        'eisenmarken' => [],
                        'letzte_aktivitaet' => null
                    ];
                }

                $mitarbeiter[$name]['anzahl_rotekarten']++;
                $mitarbeiter[$name]['letzte_aktivitaet'] = $rotekarte->created_at->format('d.m.Y H:i');

                // Abteilung hinzufügen
                if (isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                    if (!isset($mitarbeiter[$name]['abteilungen'][$abteilung])) {
                        $mitarbeiter[$name]['abteilungen'][$abteilung] = 0;
                    }
                    $mitarbeiter[$name]['abteilungen'][$abteilung]++;
                }

                // Eisenmarke hinzufügen
                if (isset($spektrometerDaten['eisenmarke'])) {
                    $eisenmarke = $spektrometerDaten['eisenmarke'];
                    if (!isset($mitarbeiter[$name]['eisenmarken'][$eisenmarke])) {
                        $mitarbeiter[$name]['eisenmarken'][$eisenmarke] = 0;
                    }
                    $mitarbeiter[$name]['eisenmarken'][$eisenmarke]++;
                }
            }
        }

        // Sortiere nach Anzahl der Rotekarten
        uasort($mitarbeiter, function($a, $b) {
            return $b['anzahl_rotekarten'] <=> $a['anzahl_rotekarten'];
        });

        return collect(array_values($mitarbeiter));
    }

    private function getFehlercodeAnalyse($baseQuery)
    {
        $rotekarten = $baseQuery->whereNotNull('spektrometer_daten')->get();
        $fehlercodes = [];

        foreach ($rotekarten as $rotekarte) {
            $spektrometerDaten = $rotekarte->spektrometer_daten;

            if (is_array($spektrometerDaten) && isset($spektrometerDaten['fehlercode']) && !empty($spektrometerDaten['fehlercode'])) {
                $fehlercode = $spektrometerDaten['fehlercode'];

                if (!isset($fehlercodes[$fehlercode])) {
                    $fehlercodes[$fehlercode] = [
                        'fehlercode' => $fehlercode,
                        'anzahl_rotekarten' => 0,
                        'abteilungen' => [],
                        'eisenmarken' => [],
                        'letzte_rotekarte' => null
                    ];
                }

                $fehlercodes[$fehlercode]['anzahl_rotekarten']++;
                $fehlercodes[$fehlercode]['letzte_rotekarte'] = $rotekarte->created_at->format('d.m.Y');

                // Abteilung hinzufügen
                if (isset($spektrometerDaten['abteilung'])) {
                    $abteilung = $spektrometerDaten['abteilung'];
                    if (!isset($fehlercodes[$fehlercode]['abteilungen'][$abteilung])) {
                        $fehlercodes[$fehlercode]['abteilungen'][$abteilung] = 0;
                    }
                    $fehlercodes[$fehlercode]['abteilungen'][$abteilung]++;
                }

                // Eisenmarke hinzufügen
                if (isset($spektrometerDaten['eisenmarke'])) {
                    $eisenmarke = $spektrometerDaten['eisenmarke'];
                    if (!isset($fehlercodes[$fehlercode]['eisenmarken'][$eisenmarke])) {
                        $fehlercodes[$fehlercode]['eisenmarken'][$eisenmarke] = 0;
                    }
                    $fehlercodes[$fehlercode]['eisenmarken'][$eisenmarke]++;
                }
            }
        }

        // Sortiere nach Anzahl der Rotekarten
        uasort($fehlercodes, function($a, $b) {
            return $b['anzahl_rotekarten'] <=> $a['anzahl_rotekarten'];
        });

        return collect(array_values($fehlercodes));
    }

    public function exportPdf(Request $request)
    {
        // Sammle alle Statistik-Daten
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        $baseQuery = Rotekarte::whereBetween('created_at', [$startDate, $endDate]);

        $statistiken = [
            'gesamt' => $this->getGesamtStatistiken($baseQuery),
            'status' => $this->getStatusVerteilung($baseQuery),
            'abteilungen' => $this->getAbteilungsAnalyse($baseQuery),
            'trends' => $this->getZeitTrends($startDate, $endDate),
            'qualitaet' => $this->getQualitaetsMetriken($baseQuery),
            'durchlaufzeiten' => $this->getDurchlaufzeiten($baseQuery, $request),
            'problembereiche' => $this->getProblembereiche($baseQuery),
            'analyseabweichungen' => $this->getAnalyseabweichungen($baseQuery),
            'eisenmarken' => $this->getEisenmarkenAnalyse($baseQuery),
            'schichten' => $this->getSchichtAnalyse($baseQuery),
            'mitarbeiter' => $this->getMitarbeiterAnalyse($baseQuery),
            'fehlercodes' => $this->getFehlercodeAnalyse($baseQuery),
        ];

        $filter = [
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];

        // PDF generieren
        $pdf = PDF::loadView('pdf.statistik-report', [
            'statistiken' => $statistiken,
            'filter' => $filter
        ]);

        $pdf->setPaper('A4');
        $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

        // Dateiname generieren
        $zeitraum = str_replace(['-', ' '], ['', '_'], $startDate . '_bis_' . $endDate);
        $filename = 'Statistik_Report_' . $zeitraum . '.pdf';

        return $pdf->download($filename);
    }

    public function sendEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'include_pdf' => 'boolean'
        ]);

        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $includePdf = $request->get('include_pdf', false);

        $baseQuery = Rotekarte::whereBetween('created_at', [$startDate, $endDate]);

        $statistiken = [
            'gesamt' => $this->getGesamtStatistiken($baseQuery),
            'status' => $this->getStatusVerteilung($baseQuery),
            'abteilungen' => $this->getAbteilungsAnalyse($baseQuery),
            'trends' => $this->getZeitTrends($startDate, $endDate),
            'qualitaet' => $this->getQualitaetsMetriken($baseQuery),
            'durchlaufzeiten' => $this->getDurchlaufzeiten($baseQuery, $request),
            'problembereiche' => $this->getProblembereiche($baseQuery),
            'analyseabweichungen' => $this->getAnalyseabweichungen($baseQuery),
            'eisenmarken' => $this->getEisenmarkenAnalyse($baseQuery),
            'schichten' => $this->getSchichtAnalyse($baseQuery),
            'mitarbeiter' => $this->getMitarbeiterAnalyse($baseQuery),
            'fehlercodes' => $this->getFehlercodeAnalyse($baseQuery),
        ];

        $filter = [
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];

        $pdfPath = null;

        if ($includePdf) {
            // PDF generieren und temporär speichern
            $pdf = PDF::loadView('pdf.statistik-report', [
                'statistiken' => $statistiken,
                'filter' => $filter
            ]);

            $pdf->setPaper('A4');
            $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

            $zeitraum = str_replace(['-', ' '], ['', '_'], $startDate . '_bis_' . $endDate);
            $filename = 'Statistik_Report_' . $zeitraum . '.pdf';
            $pdfPath = storage_path('app/temp/' . $filename);

            // Temp-Verzeichnis erstellen falls nicht vorhanden
            if (!file_exists(dirname($pdfPath))) {
                mkdir(dirname($pdfPath), 0755, true);
            }

            $pdf->save($pdfPath);
        }

        try {
            // E-Mail senden
            Mail::to($request->email)->send(new StatistikReportMail($statistiken, $filter, $pdfPath));

            // Temporäre PDF-Datei löschen
            if ($pdfPath && file_exists($pdfPath)) {
                unlink($pdfPath);
            }

            return response()->json([
                'success' => true,
                'message' => 'Statistik-Report wurde erfolgreich per E-Mail versendet.'
            ]);

        } catch (\Exception $e) {
            // Temporäre PDF-Datei löschen bei Fehler
            if ($pdfPath && file_exists($pdfPath)) {
                unlink($pdfPath);
            }

            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Versenden der E-Mail: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getEmailVerteiler()
    {
        $verteiler = EmailVerteiler::where('type', 'statistik')
            ->orWhere('type', 'management')
            ->get()
            ->map(function ($email) {
                // Da die Tabelle keine name-Spalte hat, verwenden wir die E-Mail als Name
                $displayName = $email->email;

                // Versuche einen schöneren Namen basierend auf der E-Mail zu erstellen
                if (str_contains($email->email, 'management')) {
                    $displayName = 'Management (' . $email->email . ')';
                } elseif (str_contains($email->email, 'qualitaet')) {
                    $displayName = 'Qualitätsmanagement (' . $email->email . ')';
                } elseif (str_contains($email->email, 'geschaeftsfuehrung')) {
                    $displayName = 'Geschäftsführung (' . $email->email . ')';
                } elseif (str_contains($email->email, 'controlling')) {
                    $displayName = 'Controlling (' . $email->email . ')';
                }

                return [
                    'email' => $email->email,
                    'name' => $displayName,
                    'type' => $email->type
                ];
            });

        return response()->json($verteiler);
    }
}
