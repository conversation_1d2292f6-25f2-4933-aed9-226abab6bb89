<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class StatistikController extends Controller
{
    public function index(Request $request)
    {
        // Zeitraum-Filter (Standard: letzte 30 Tage)
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        // Basis-Query mit Zeitraum-Filter
        $baseQuery = Rotekarte::whereBetween('created_at', [$startDate, $endDate]);
        
        // 1. Gesamtstatistiken
        $gesamtStatistiken = $this->getGesamtStatistiken($baseQuery);
        
        // 2. Status-Verteilung
        $statusVerteilung = $this->getStatusVerteilung($baseQuery);
        
        // 3. Abteilungsanalyse
        $abteilungsAnalyse = $this->getAbteilungsAnalyse($baseQuery);
        
        // 4. Zeitbasierte Trends
        $zeitTrends = $this->getZeitTrends($startDate, $endDate);
        
        // 5. Qualitätsmetriken
        $qualitaetsMetriken = $this->getQualitaetsMetriken($baseQuery);
        
        // 6. Durchlaufzeiten
        $durchlaufzeiten = $this->getDurchlaufzeiten($baseQuery);
        
        // 7. Top Problembereiche
        $problembereiche = $this->getProblembereiche($baseQuery);
        
        return Inertia::render('Statistik/Index', [
            'statistiken' => [
                'gesamt' => $gesamtStatistiken,
                'status' => $statusVerteilung,
                'abteilungen' => $abteilungsAnalyse,
                'trends' => $zeitTrends,
                'qualitaet' => $qualitaetsMetriken,
                'durchlaufzeiten' => $durchlaufzeiten,
                'problembereiche' => $problembereiche,
            ],
            'filter' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]
        ]);
    }
    
    private function getGesamtStatistiken($baseQuery)
    {
        $total = $baseQuery->count();
        $heute = Rotekarte::whereDate('created_at', Carbon::today())->count();
        $dieseWoche = Rotekarte::whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ])->count();
        $dieserMonat = Rotekarte::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
            
        return [
            'total' => $total,
            'heute' => $heute,
            'diese_woche' => $dieseWoche,
            'dieser_monat' => $dieserMonat,
            'durchschnitt_pro_tag' => $total > 0 ? round($total / 30, 1) : 0,
        ];
    }
    
    private function getStatusVerteilung($baseQuery)
    {
        return $baseQuery->select(
            DB::raw("
                CASE 
                    WHEN qs_daten IS NULL OR JSON_EXTRACT(qs_daten, '$.status') IS NULL THEN 'Offen'
                    ELSE JSON_UNQUOTE(JSON_EXTRACT(qs_daten, '$.status'))
                END as status
            "),
            DB::raw('COUNT(*) as anzahl')
        )
        ->groupBy('status')
        ->get()
        ->map(function ($item) {
            return [
                'status' => $item->status ?: 'Offen',
                'anzahl' => $item->anzahl,
            ];
        });
    }
    
    private function getAbteilungsAnalyse($baseQuery)
    {
        return $baseQuery->select(
            DB::raw("JSON_UNQUOTE(JSON_EXTRACT(spektrometer_daten, '$.abteilung')) as abteilung"),
            DB::raw('COUNT(*) as anzahl')
        )
        ->whereNotNull('spektrometer_daten')
        ->groupBy('abteilung')
        ->get()
        ->map(function ($item) {
            $abteilungName = match($item->abteilung) {
                'NG' => 'Nassgussanlage',
                'GG' => 'Großguss',
                'HF' => 'Hochfrequenz',
                default => $item->abteilung ?: 'Unbekannt'
            };
            
            return [
                'abteilung' => $abteilungName,
                'code' => $item->abteilung,
                'anzahl' => $item->anzahl,
            ];
        });
    }
    
    private function getZeitTrends($startDate, $endDate)
    {
        $trends = Rotekarte::select(
            DB::raw('DATE(created_at) as datum'),
            DB::raw('COUNT(*) as anzahl')
        )
        ->whereBetween('created_at', [$startDate, $endDate])
        ->groupBy('datum')
        ->orderBy('datum')
        ->get();

        // Fülle fehlende Tage mit 0 auf
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $trendData = [];

        // Erstelle Array für jeden Tag im Zeitraum
        for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $found = $trends->firstWhere('datum', $dateStr);
            $trendData[] = [
                'datum' => $dateStr,
                'anzahl' => $found ? $found->anzahl : 0,
                'datum_formatted' => $date->format('d.m.Y'),
            ];
        }

        return $trendData;
    }
    
    private function getQualitaetsMetriken($baseQuery)
    {
        $abgeschlossen = $baseQuery->where('qs_daten->status', 'Abgeschlossen')->count();
        $total = $baseQuery->count();
        $abschlussRate = $total > 0 ? round(($abgeschlossen / $total) * 100, 1) : 0;
        
        // Durchschnittliche Bearbeitungszeit (vereinfacht)
        $avgBearbeitungszeit = $baseQuery
            ->whereNotNull('updated_at')
            ->select(DB::raw('AVG(TIMESTAMPDIFF(HOUR, created_at, updated_at)) as avg_hours'))
            ->first();
            
        return [
            'abschluss_rate' => $abschlussRate,
            'abgeschlossen' => $abgeschlossen,
            'in_bearbeitung' => $baseQuery->where('qs_daten->status', 'In Bearbeitung')->count(),
            'offen' => $total - $abgeschlossen,
            'avg_bearbeitungszeit_stunden' => round($avgBearbeitungszeit->avg_hours ?? 0, 1),
        ];
    }
    
    private function getDurchlaufzeiten($baseQuery)
    {
        return $baseQuery
            ->select(
                'id',
                'created_at',
                'updated_at',
                DB::raw('TIMESTAMPDIFF(HOUR, created_at, updated_at) as durchlaufzeit_stunden')
            )
            ->whereNotNull('updated_at')
            ->where('qs_daten->status', 'Abgeschlossen')
            ->orderBy('durchlaufzeit_stunden', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'rotekarte_id' => $item->id,
                    'durchlaufzeit_stunden' => $item->durchlaufzeit_stunden,
                    'durchlaufzeit_tage' => round($item->durchlaufzeit_stunden / 24, 1),
                    'erstellt_am' => $item->created_at->format('d.m.Y H:i'),
                    'abgeschlossen_am' => $item->updated_at->format('d.m.Y H:i'),
                ];
            });
    }
    
    private function getProblembereiche($baseQuery)
    {
        // Analysiere häufige Probleme basierend auf QS-Daten
        $probleme = $baseQuery
            ->whereNotNull('qs_daten')
            ->where('qs_daten->status', '!=', 'Abgeschlossen')
            ->select(
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(spektrometer_daten, '$.abteilung')) as abteilung"),
                DB::raw('COUNT(*) as anzahl_probleme')
            )
            ->groupBy('abteilung')
            ->orderBy('anzahl_probleme', 'desc')
            ->get();
            
        return $probleme->map(function ($item) {
            $abteilungName = match($item->abteilung) {
                'NG' => 'Nassgussanlage',
                'GG' => 'Großguss', 
                'HF' => 'Hochfrequenz',
                default => $item->abteilung ?: 'Unbekannt'
            };
            
            return [
                'bereich' => $abteilungName,
                'anzahl_probleme' => $item->anzahl_probleme,
                'code' => $item->abteilung,
            ];
        });
    }
}
