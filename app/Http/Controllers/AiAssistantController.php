<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use OpenAI;

class AiAssistantController extends Controller
{
    /**
     * OpenAI Client-Instanz
     */
    protected $openai;

    /**
     * Create a new controller instance.
     * 
     * @return void
     */
    public function __construct()
    {
        // OpenAI-Client initialisieren
        $this->openai = OpenAI::client(config('services.openai.api_key'));
    }

    /**
     * Process an AI assistant query request.
     *
     * @param Request $request The request containing the prompt
     * @return JsonResponse The JSON response with the AI's response
     */
    public function query(Request $request): JsonResponse
    {
        try {
            // Validate the incoming request
            $validated = $request->validate([
                'prompt' => 'required|string|max:4000',
                'analyseId' => 'sometimes|integer',  // Wird als rotekarten_id verwendet
                'forceNew' => 'sometimes|boolean',   // Optional: Force new AI response instead of using stored one
            ]);

            $prompt = $validated['prompt'];
            $forceNew = $validated['forceNew'] ?? false;
            $rotekarteId = $validated['analyseId'] ?? null;  // Umbenannt für Klarheit
            
            // Log the raw request for debugging
            Log::debug('AI Assistant raw request', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'content' => $request->getContent(),
                'prompt' => $prompt,
                'forceNew' => $forceNew,
                'rotekarte_id' => $rotekarteId  // Umbenennung im Log
            ]);
            
            // Kontextanreicherung entfernen - direkt den Prompt verwenden
            
            $cacheKey = 'ai_response_' . md5($prompt);

            // Check database for stored response
            $storedResponse = $this->getStoredResponse($prompt, $rotekarteId);
            
            // Return stored response if it exists and we're not forcing a new response
            if (!$forceNew && $storedResponse) {
                return response()->json([
                    'response' => $storedResponse->response,
                    'metadata' => [
                        'source' => 'database',
                        'created_at' => $storedResponse->created_at,
                        'usage_count' => $storedResponse->usage_count,
                        'can_refresh' => true
                    ]
                ]);
            }

            // Check if we have a cached response for this exact prompt (and not forcing new)
            if (!$forceNew && Cache::has($cacheKey)) {
                $cachedResponse = Cache::get($cacheKey);
                return response()->json([
                    'response' => $cachedResponse,
                    'metadata' => [
                        'source' => 'cache',
                        'can_refresh' => true
                    ]
                ]);
            }

            // Log the request
            Log::info('AI Assistant query received', [
                'prompt_length' => strlen($prompt),
                'has_rotekarte_id' => isset($rotekarteId),
                'force_new' => $forceNew
            ]);

            // Call OpenAI API
            $response = $this->callOpenAI($prompt, $rotekarteId);
            
            // Cache the response for future identical queries (1 hour)
            Cache::put($cacheKey, $response, 3600);
            
            // Store the response in the database
            $this->storeResponse($prompt, $response, $rotekarteId);

            return response()->json([
                'response' => $response,
                'metadata' => [
                    'source' => 'openai_api',
                    'generated_at' => now()->toIso8601String(),
                    'can_refresh' => true
                ]
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation error in AI Assistant query', [
                'errors' => $e->errors(),
            ]);
            
            return response()->json([
                'error' => 'Validierungsfehler: ' . implode(', ', array_map(function($errors) {
                    return implode(', ', $errors);
                }, $e->errors())),
                'validation_errors' => $e->errors(),
                'api_error' => false
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error in AI Assistant query', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'class' => get_class($e)
            ]);

            // Bestimme, ob es ein Datenformat-Fehler oder API-Fehler ist
            $isFormatError = strpos($e->getMessage(), 'format') !== false || 
                             strpos($e->getMessage(), 'Analyse-Daten') !== false;
            
            if ($isFormatError) {
                return response()->json([
                    'error' => $e->getMessage(),
                    'format_error' => true,
                    'api_error' => false,
                    'response' => 'Fehler: ' . $e->getMessage()
                ], 400); // 400 Bad Request für Formatfehler
            } else {
                // Klare Fehlermeldung zurückgeben statt simulierter Antwort
                return response()->json([
                    'error' => 'Die KI-API ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.',
                    'message' => $e->getMessage(),
                    'api_error' => true,
                    'response' => 'Fehler: Die KI-API ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.'
                ], 503); // 503 Service Unavailable
            }
        }
    }

    /**
     * Call OpenAI API and get response.
     * 
     * @param string $prompt The user's prompt
     * @param int|null $rotekarteId Optional ID of the Rotekarte
     * @return string The AI's response
     */
    private function callOpenAI(string $prompt, ?int $rotekarteId = null): string
    {
        try {
            // Extrahieren von Element-Informationen aus dem Prompt
            $elementAbweichungen = [];
            
            // Log zur Diagnose des Original-Prompts
            Log::debug('Prompt zur Analyse:', ['prompt' => $prompt]);
            
            // Direktes Extrahieren der Werte aus dem konkreten Format der Tabelle
            // Format: Element [Tab] IST-WERT [Tab] SOLL-WERT [Tab] TOLERANZ
            $elementMatches = [];
            
            // Detailliertes Logging für den exakten Prompt-Text
            Log::debug('Exakter Text für Regex-Matching:', [
                'prompt_length' => strlen($prompt),
                'prompt_encoded' => json_encode($prompt),
                'prompt_hex' => bin2hex($prompt)
            ]);
            
            // Spezifischerer regulärer Ausdruck speziell für den Fall "≤ 0.05"
            if (preg_match_all('/([A-Za-z][A-Za-z_]*)\s+([\d,.]+)\s+(≤\s+[\d,.]+|[<≤][\s]*[\d,.]+|[\d,.]+(?:\s*-\s*[\d,.]+)?)\s+([+-][\d,.]+)/i', $prompt, $matches, PREG_SET_ORDER)) {
                Log::debug('Regex-Match erfolgreich mit Anzahl:', ['matches' => count($matches)]);
                
                foreach ($matches as $match) {
                    $element = trim($match[1]);
                    $istWert = trim($match[2]);
                    $sollWert = trim($match[3]);
                    $toleranz = trim($match[4]);
                    
                    Log::debug('Match-Details:', [
                        'original' => $match[0],
                        'element' => $element,
                        'istWert' => $istWert,
                        'sollWert' => $sollWert,
                        'toleranz' => $toleranz
                    ]);
                    
                    $elementMatches[] = [
                        'element' => $element,
                        'istWert' => $istWert,
                        'sollWert' => $sollWert,
                        'toleranz' => $toleranz
                    ];
                }
            } else {
                Log::warning('Kein Match mit dem Hauptmuster gefunden. Versuche alternatives Muster.');
                
                // Versuch mit einem sehr spezifischen Muster für das Cr-Beispiel
                if (preg_match('/Cr\s+([\d,.]+)\s+≤\s+([\d,.]+)\s+([+-][\d,.]+)/i', $prompt, $crMatch)) {
                    Log::debug('Cr-spezifisches Match gefunden:', ['match' => $crMatch]);
                    
                    $element = 'Cr';
                    $istWert = trim($crMatch[1]);
                    $sollWert = '≤ ' . trim($crMatch[2]);
                    $toleranz = trim($crMatch[3]);
                    
                    $elementMatches[] = [
                        'element' => $element,
                        'istWert' => $istWert,
                        'sollWert' => $sollWert,
                        'toleranz' => $toleranz
                    ];
                }
            }
            
            // Auch die speziellen Ranges für Cu (1-1.1) und Si (2-2.2) prüfen
            if (preg_match_all('/ELEMENT\s+IST-WERT\s+SOLL-WERT\s+TOLERANZ\s*\n\s*([A-Za-z]+)\s+([\d,.]+)\s+([\d](?:\s*-\s*[\d,.]+)?)\s+([+-][\d,.]+)/si', $prompt, $rangeMatches, PREG_SET_ORDER)) {
                foreach ($rangeMatches as $match) {
                    $element = trim($match[1]);
                    $istWert = trim($match[2]);
                    $sollWert = trim($match[3]);
                    $toleranz = trim($match[4]);
                    
                    // Prüfen, ob wir dieses Element bereits extrahiert haben
                    $exists = false;
                    foreach ($elementMatches as $existingMatch) {
                        if ($existingMatch['element'] === $element) {
                            $exists = true;
                            break;
                        }
                    }
                    
                    // Nur hinzufügen, wenn es noch nicht existiert
                    if (!$exists) {
                        $elementMatches[] = [
                            'element' => $element,
                            'istWert' => $istWert,
                            'sollWert' => $sollWert,
                            'toleranz' => $toleranz
                        ];
                    }
                }
            }
            
            // Formatiere die elementMatches in elementAbweichungen um
            foreach ($elementMatches as $match) {
                $element = $match['element'];
                $istWert = $match['istWert'];
                $sollWert = $match['sollWert'];
                $toleranz = $match['toleranz'];
                
                $sollWertText = $sollWert;
                if (strpos($sollWert, '-') !== false) {
                    list($min, $max) = explode('-', $sollWert);
                    $min = trim($min);
                    $max = trim($max);
                    $sollWertText = "$min - $max";
                }
                
                $elementAbweichungen[] = "$element: IST-Wert $istWert, SOLL-Wert $sollWertText (Toleranz: $toleranz)";
                Log::debug('Extraktion mit exaktem Muster erfolgreich', [
                    'element' => $element,
                    'istWert' => $istWert,
                    'sollWert' => $sollWertText,
                    'toleranz' => $toleranz
                ]);
            }
            
            // Log der extrahierten Abweichungen
            Log::debug('Extrahierte Elementabweichungen:', ['abweichungen' => $elementAbweichungen]);
            
            // Wenn noch keine Elemente extrahiert wurden, Fallback-Methoden verwenden
            if (empty($elementAbweichungen)) {
                Log::warning('Keine Elementabweichungen - Verwende Fallback-Methoden');
                
                // Format aus Show.vue: "Mg: Ist-Wert 0,074, Soll-Wert 0.035 - 0.06"
                if (preg_match_all('/([A-Za-z][A-Za-z_]*):\s+Ist-Wert\s+([\d,.]+),\s+Soll-Wert\s+(≤\s+[\d,.]+|[<≤][\s]*[\d,.]+|[\d,.]+(?:\s*-\s*[\d,.]+)?)/i', $prompt, $analysewerte, PREG_SET_ORDER)) {
                    Log::debug('Extracting from "Analysewerte" format:', ['matches' => count($analysewerte)]);
                    
                    foreach ($analysewerte as $match) {
                        $element = trim($match[1]);
                        $istWert = trim($match[2]);
                        $sollWert = trim($match[3]);
                        
                        // Berechne Toleranz
                        $toleranz = '';
                        if (strpos($sollWert, '-') !== false) {
                            list($min, $max) = explode('-', $sollWert);
                            $min = trim($min);
                            $max = trim($max);
                            
                            // Convert comma to dot for calculations
                            $istWertNum = (float)str_replace(',', '.', $istWert);
                            $maxNum = (float)str_replace(',', '.', $max);
                            
                            if ($istWertNum > $maxNum) {
                                $toleranz = '+' . number_format($istWertNum - $maxNum, 3, ',', '');
                            }
                        }
                        
                        $elementAbweichungen[] = "$element: IST-Wert $istWert, SOLL-Wert $sollWert" . ($toleranz ? " (Toleranz: $toleranz)" : "");
                        
                        Log::debug('Extracted element from Analysewerte', [
                            'element' => $element,
                            'istWert' => $istWert,
                            'sollWert' => $sollWert,
                            'toleranz' => $toleranz
                        ]);
                    }
                }
                
                // Direktes Extrahieren für den speziellen Cr ≤ 0.05 +0,002 Fall
                if (preg_match('/Cr\s+([\d,.]+)\s+≤\s+([\d,.]+)\s+([+-][\d,.]+)/i', $prompt, $crMatch)) {
                    Log::debug('Cr-spezifischer Fallback-Match gefunden:', ['match' => $crMatch]);
                    
                    $element = 'Cr';
                    $istWert = trim($crMatch[1]);
                    $sollWert = '≤ ' . trim($crMatch[2]);
                    $toleranz = trim($crMatch[3]);
                    
                    $elementAbweichungen[] = "$element: IST-Wert $istWert, SOLL-Wert $sollWert (Toleranz: $toleranz)";
                }
                
                // Allgemeines Muster für Element + Werte mit ≤-Symbol
                if (empty($elementAbweichungen) && preg_match_all('/([A-Za-z][A-Za-z_]*)\s+([\d,.]+)\s+(≤\s+[\d,.]+|[<≤][\s]*[\d,.]+)\s+([+-][\d,.]+)/i', $prompt, $matches, PREG_SET_ORDER)) {
                    Log::debug('Allgemeiner Fallback-Match für ≤-Symbol gefunden:', ['matches' => count($matches)]);
                    
                    foreach ($matches as $match) {
                        $element = trim($match[1]);
                        $istWert = trim($match[2]);
                        $sollWert = trim($match[3]);
                        $toleranz = trim($match[4]);
                        
                        $elementAbweichungen[] = "$element: IST-Wert $istWert, SOLL-Wert $sollWert (Toleranz: $toleranz)";
                    }
                }
            }
            
            // Eisenmarke extrahieren
            $eisenmarke = "nicht angegeben";
            if (preg_match('/Eisenmarke:\s+([^\n]+)/', $prompt, $matches)) {
                $eisenmarke = trim($matches[1]);
            }
            
            // Generiere einen strukturierten Prompt
            $strukturierterPrompt = "Welche konkreten Qualitätsprobleme hat das Gussteil bei der Eisenmarke $eisenmarke mit folgenden Analyseabweichungen:\n";
            foreach ($elementAbweichungen as $abweichung) {
                $strukturierterPrompt .= "- $abweichung\n";
            }
            
            // Wenn keine Elementabweichungen gefunden wurden, gebe einen Fehler zurück
            if (empty($elementAbweichungen)) {
                Log::warning('Keine Elementabweichungen extrahiert', [
                    'prompt' => $prompt,
                    'eisenmarke' => $eisenmarke
                ]);

                // Statt einen Fehler zu werfen, generieren wir eine Standardempfehlung für Prüfungen
                $strukturierterPrompt = "Für ein Gussteil mit der Eisenmarke $eisenmarke wurden keine Analyse-Daten im erwarteten Format erkannt. Bitte gib eine umfassende Übersicht über empfohlene Standardprüfungen für Gussteile:

1. Liste die wichtigsten zerstörungsfreien und zerstörenden Prüfverfahren für Gussteile auf, sortiert nach Relevanz.
2. Erkläre, welche Prüfmethoden für welche Qualitätsmerkmale am besten geeignet sind.
3. Gib einen strukturierten Prüfplan für Standard-Qualitätssicherung von Gussteilen der Eisenmarke $eisenmarke.
4. Verweise auf relevante Normen (DIN, EN, ISO) und Industriestandards für jede Prüfmethode.
5. Füge eine Tabelle mit typischen Sollwerten und Toleranzen für Schlüsselelemente in der Eisenmarke $eisenmarke hinzu.

Beginne mit einer Einleitung über die Wichtigkeit systematischer Qualitätsprüfungen, wenn keine spezifischen Analysedaten verfügbar sind.";

                $systemMessage = "Du bist Experte für Metallurgie und Gusstechnik mit Schwerpunkt auf Qualitätssicherung und Normenvorgaben. Du sollst eine umfassende Anleitung für Standardprüfungen von Gussteilen erstellen, wenn keine spezifischen Analysedaten vorliegen.

1. Sei präzise und praxisorientiert mit konkreten Handlungsanweisungen.
2. Beziehe dich auf spezifische Fachliteratur und aktuelle Industriestandards der Gussbranche.
3. Strukturiere deine Antwort klar mit Überschriften, Aufzählungen und einer Tabelle mit typischen Werten.
4. Berücksichtige sowohl mechanische, chemische als auch visuelle Prüfmethoden.
5. Verweise auf relevante Normvorgaben (DIN, EN, ISO) mit genauen Bezeichnungen.

Deine Antwort soll als vollständige Standardreferenz für Qualitätssicherungsteams dienen, wenn detaillierte Analysen fehlen oder nicht erkannt werden können.";

                try {
                    // Versuchen, den OpenAI-Client zu verwenden
                    $result = $this->openai->chat()->create([
                        'model' => 'gpt-4o', 
                        'messages' => [
                            ['role' => 'system', 'content' => $systemMessage],
                            ['role' => 'user', 'content' => $strukturierterPrompt]
                        ],
                        'temperature' => 0.2,
                        'max_tokens' => 1200,
                        'top_p' => 0.9,
                    ]);

                    // Extract and return the AI's response
                    return $result->choices[0]->message->content;
                } catch (\Exception $fallbackError) {
                    // Wenn auch der Standard-Empfehlungsversuch fehlschlägt, dann werfen wir die ursprüngliche Fehlermeldung
                    Log::error('Auch Standardempfehlungs-Anfrage fehlgeschlagen', [
                        'error' => $fallbackError->getMessage()
                    ]);
                    throw new \Exception('Keine Analyse-Daten im angegebenen Format gefunden. Bitte überprüfen Sie die Eingabe.');
                }
            }
            
            $strukturierterPrompt .= "\nBeurteile die Toleranzwerte konkret und gib präzise Empfehlungen:
1. Gib an, ob mit den vorliegenden Toleranzwerten einfache Prüfungen (wie Härteprüfung oder Zugprüfung) ausreichend sind.
2. Falls die Toleranzabweichungen kritisch sind, empfehle konkrete, ausführliche Prüfverfahren unter Berücksichtigung relevanter Normen.
3. Wenn ein Toleranzwert gemäß Fachliteratur oder Normen nicht akzeptabel ist, gib klar an, ob die Teile entsorgt werden müssen.
4. Füge bei jeder Empfehlung den konkreten Grenzwert aus der relevanten Norm oder Fachliteratur an (z.B. 'laut DIN EN XXX ist der Grenzwert...').

Beginne mit einer eindeutigen Gesamtbewertung (z.B. 'unbedenklich', 'bedingt verwendbar' oder 'kritisch') und begründe diese.";
            
            // Spezifischerer System-Prompt
            $systemMessage = "Du bist Experte für Metallurgie und Gusswerkstoffe mit Schwerpunkt auf Qualitätssicherung und Normenvorgaben. Beurteile die folgenden chemischen Analyseabweichungen anhand relevanter Normen (DIN, EN, ISO) sowie spezifischer Fachliteratur der Gussbranche.

1. Beurteile jede Abweichung einzeln im Hinblick auf:
   - Grenzwerte: Ist die Abweichung noch innerhalb üblicher Messtoleranzen oder überschreitet sie einen kritischen Grenzwert laut Norm?
   - Materialeigenschaften: Könnte diese Abweichung die mechanischen Eigenschaften (Festigkeit, Duktilität, Härte etc.) oder die Gießbarkeit des Werkstoffs beeinflussen?
   - Funktionale Auswirkungen: Hat die Abweichung potenziell negative Effekte auf Korrosionsbeständigkeit, Wärmebehandlung oder andere relevante Eigenschaften?

2. Empfehle gezielt notwendige Prüfungen für jede Abweichung:
   - Falls die Abweichung gering ist: Sind einfache Prüfungen (z.B. Härteprüfung, Zugprüfung) ausreichend?
   - Falls die Abweichung bedeutend ist: Welche zusätzlichen Prüfungen (z.B. Metallographie, Spektralanalyse) sollten durchgeführt werden?
   - Falls notwendig: Ist eine weitere chemische Analyse sinnvoll, um Messfehler auszuschließen?

3. Beurteile, ob eine Entsorgung erforderlich ist oder ob Alternativen bestehen:
   - Falls die Abweichung laut Normen oder Fachliteratur kritisch ist: Welche Maßnahmen sind möglich? (z.B. Wärmebehandlung, Nachbehandlung, Mischchargenbildung).
   - Falls eine Entsorgung empfohlen wird: Nenne den spezifischen Grenzwert, der überschritten wurde (z.B. 'laut DIN EN XXX beträgt der Maximalwert für Element YYY ...').

Formuliere eine praxisnahe, technische Antwort mit quantifizierbaren Aussagen. Vermeide pauschale Begriffe wie 'kritisch' oder 'ungeeignet', sondern differenziere je nach Einfluss auf die Materialeigenschaften.

WICHTIG: Bei kleineren Abweichungen (< 0,005) gehe davon aus, dass diese in der Regel vernachlässigbar sind und oft im Rahmen normaler Messtoleranzen liegen. Formuliere hier besonders zurückhaltende Bewertungen.";

            // Log the request to help with debugging
            Log::info('Calling OpenAI API', [
                'model' => 'gpt-4o',
                'prompt' => $strukturierterPrompt
            ]);

            try {
                // Versuchen, den OpenAI-Client zu verwenden
                $result = $this->openai->chat()->create([
                    'model' => 'gpt-4o', 
                    'messages' => [
                        ['role' => 'system', 'content' => $systemMessage],
                        ['role' => 'user', 'content' => $strukturierterPrompt]
                    ],
                    'temperature' => 0.2,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ]);

                // Extract and return the AI's response
                return $result->choices[0]->message->content;
            } catch (\Exception $clientError) {
                // Log the client error
                Log::error('OpenAI client error - trying direct HTTP request', [
                    'error' => $clientError->getMessage()
                ]);

                // Als Fallback versuchen wir einen direkten HTTP-Request
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . config('services.openai.api_key'),
                    'Content-Type' => 'application/json',
                ])->post('https://api.openai.com/v1/chat/completions', [
                    'model' => 'gpt-4o',
                    'messages' => [
                        ['role' => 'system', 'content' => $systemMessage],
                        ['role' => 'user', 'content' => $strukturierterPrompt]
                    ],
                    'temperature' => 0.2,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['choices'][0]['message']['content'];
                } else {
                    // Wenn auch der direkte HTTP-Request fehlschlägt, werfen wir die Ausnahme
                    throw new \Exception('Direct HTTP request failed: ' . $response->body());
                }
            }
        } catch (\Exception $e) {
            Log::error('OpenAI API call failed', [
                'error' => $e->getMessage(),
                'prompt' => $prompt,
                'error_details' => $e
            ]);
            
            // Keine simulierte Antwort mehr, stattdessen Exception weiterwerfen
            throw $e;
        }
    }

    /**
     * Holt die Analyse-Daten für den KI-Kontext
     * 
     * @param int $analyseId Die ID der Analyse
     * @return array|null Die Soll- und Ist-Werte mit Toleranzen oder null wenn nicht gefunden
     */
    private function getAnalyseDaten(int $analyseId): ?array
    {
        try {
            // Spektrometer-Model importieren
            $spektrometerModel = app(\App\Models\Spektrometer::class);
            
            // Daten aus der Datenbank holen
            $analyse = $spektrometerModel->find($analyseId);
            
            if (!$analyse || empty($analyse->data['analysewerte'])) {
                return null;
            }
            
            // Soll- und Ist-Werte formatieren
            $sollWerte = [];
            $istWerte = [];
            $abweichungen = [];
            
            foreach ($analyse->data['analysewerte'] as $wert) {
                $element = $wert['element'] ?? 'Unbekannt';
                
                if (isset($wert['sollWert'])) {
                    $tolerance = '';
                    if (isset($wert['minWert']) && isset($wert['maxWert'])) {
                        $tolerance = " (Toleranz: {$wert['minWert']} - {$wert['maxWert']})";
                    }
                    $sollWerte[] = "{$element}: {$wert['sollWert']}{$tolerance}";
                }
                
                if (isset($wert['istWert'])) {
                    $istWerte[] = "{$element}: {$wert['istWert']}";
                }
                
                // Abweichungen berechnen
                if (isset($wert['istWert']) && isset($wert['sollWert'])) {
                    $abweichung = $wert['istWert'] - $wert['sollWert'];
                    $prozent = 0;
                    
                    if ($wert['sollWert'] != 0) {
                        $prozent = round(($abweichung / $wert['sollWert']) * 100, 2);
                    }
                    
                    $abweichungText = sprintf(
                        "%s: %+.4f (%+.2f%%)", 
                        $element, 
                        $abweichung, 
                        $prozent
                    );
                    
                    // Status-Information (innerhalb/außerhalb Toleranz)
                    $withinTolerance = true;
                    if (isset($wert['minWert']) && isset($wert['maxWert'])) {
                        $withinTolerance = $wert['istWert'] >= $wert['minWert'] && $wert['istWert'] <= $wert['maxWert'];
                    }
                    
                    $abweichungText .= $withinTolerance ? " (innerhalb Toleranz)" : " (außerhalb Toleranz)";
                    $abweichungen[] = $abweichungText;
                }
            }
            
            // Eisenmarke hinzufügen, falls vorhanden
            $eisenmarke = $analyse->data['eisenmarke'] ?? 'nicht angegeben';
            
            return [
                'sollWerte' => implode("\n", $sollWerte),
                'istWerte' => implode("\n", $istWerte),
                'abweichungen' => implode("\n", $abweichungen),
                'eisenmarke' => $eisenmarke
            ];
            
        } catch (\Exception $e) {
            Log::error('Fehler beim Abrufen der Analysedaten', [
                'error' => $e->getMessage(),
                'analyseId' => $analyseId
            ]);
            return null;
        }
    }

    /**
     * Get a stored response from the database
     * 
     * @param string $prompt The prompt to look for
     * @param int|null $rotekarteId Optional ID of the Rotekarte
     * @return object|null The stored response or null if not found
     */
    private function getStoredResponse(string $prompt, ?int $rotekarteId = null): ?object
    {
        try {
            // Hash the prompt for lookup
            $promptHash = md5($prompt);
            
            // Basisabfrage
            $query = \DB::table('ai_responses')
                ->where('prompt_hash', $promptHash);
            
            // Wenn eine rotekarteId vorhanden ist, diese in der Abfrage berücksichtigen
            if ($rotekarteId) {
                $query->where(function($q) use ($rotekarteId, $promptHash) {
                    $q->where('rotekarte_id', $rotekarteId)
                      ->orWhereNull('rotekarte_id');
                });
                
                // Zuerst nach der spezifischen rotekarteId suchen
                $specificResponse = clone $query;
                $specificResponse = $specificResponse->where('rotekarte_id', $rotekarteId)->first();
                
                // Wenn ein spezifischer Eintrag gefunden wurde, diesen zurückgeben
                if ($specificResponse) {
                    // Nutzungszähler inkrementieren
                    \DB::table('ai_responses')
                        ->where('id', $specificResponse->id)
                        ->update([
                            'usage_count' => $specificResponse->usage_count + 1,
                            'updated_at' => now(),
                        ]);
                    
                    return $specificResponse;
                }
            }
            
            // Sonst nach einem generischen Eintrag suchen (ohne rotekarteId)
            $response = $query->whereNull('rotekarte_id')->first();
                
            // Increment usage count if found
            if ($response) {
                \DB::table('ai_responses')
                    ->where('id', $response->id)
                    ->update([
                        'usage_count' => $response->usage_count + 1,
                        'updated_at' => now(),
                    ]);
            }
            
            return $response;
        } catch (\Exception $e) {
            Log::error('Error retrieving stored AI response', [
                'error' => $e->getMessage(),
                'rotekarte_id' => $rotekarteId
            ]);
            return null;
        }
    }
    
    /**
     * Store an AI response in the database
     * 
     * @param string $prompt The original prompt
     * @param string $response The AI's response
     * @param int|null $rotekarteId Optional ID of the Rotekarte
     * @return bool Success status
     */
    private function storeResponse(string $prompt, string $response, ?int $rotekarteId = null): bool
    {
        try {
            // Hash the prompt for efficient lookups
            $promptHash = md5($prompt);
            
            // Abfrage vorbereiten
            $query = \DB::table('ai_responses')
                ->where('prompt_hash', $promptHash);
                
            // Wenn eine rotekarteId vorhanden ist, danach filtern
            if ($rotekarteId) {
                $query->where('rotekarte_id', $rotekarteId);
            } else {
                $query->whereNull('rotekarte_id');
            }
            
            // Prüfen ob ein Eintrag existiert
            $existing = $query->first();
                
            if ($existing) {
                // Update the existing response
                $query->update([
                    'response' => $response,
                    'updated_at' => now(),
                ]);
            } else {
                // Insert a new response
                \DB::table('ai_responses')->insert([
                    'prompt' => $prompt,
                    'prompt_hash' => $promptHash,
                    'response' => $response,
                    'rotekarte_id' => $rotekarteId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Error storing AI response', [
                'error' => $e->getMessage(),
                'rotekarte_id' => $rotekarteId
            ]);
            return false;
        }
    }
} 