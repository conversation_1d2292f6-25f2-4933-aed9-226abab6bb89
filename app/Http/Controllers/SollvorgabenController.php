<?php

namespace App\Http\Controllers;

use App\Models\Sollvorgaben;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Validator;

class SollvorgabenController extends Controller
{
    public function index()
    {
        $sollvorgaben = Sollvorgaben::latest()->get();
        return Inertia::render('Sollvorgaben/Index', [
            'sollvorgaben' => $sollvorgaben
        ]);
    }

    private function cleanData($data)
    {
        // Ensure we're working with an array
        $data = is_string($data) ? json_decode($data, true) : $data;
        
        // Remove empty EM_HF if EM is set and EM_HF is empty
        if (isset($data['EM']) && empty($data['EM_HF'])) {
            unset($data['EM_HF']);
        }
        
        // Remove empty EM if EM_HF is set and EM is empty
        if (isset($data['EM_HF']) && empty($data['EM'])) {
            unset($data['EM']);
        }
        
        return $data;
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => ['required']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Clean and prepare data
        $data = $this->cleanData($request->data);
        
        // Create with properly encoded JSON
        $sollvorgabe = Sollvorgaben::create([
            'data' => $data
        ]);

        return response()->json([
            'message' => 'Sollvorgabe erfolgreich erstellt',
            'sollvorgabe' => $sollvorgabe
        ]);
    }

    public function update(Request $request, Sollvorgaben $sollvorgabe)
    {
        $validator = Validator::make($request->all(), [
            'data' => ['required']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Clean and prepare data
        $data = $this->cleanData($request->data);
        
        // Update with properly encoded JSON
        $sollvorgabe->update([
            'data' => $data
        ]);

        return response()->json([
            'message' => 'Sollvorgabe erfolgreich aktualisiert',
            'sollvorgabe' => $sollvorgabe
        ]);
    }

    public function destroy(Sollvorgaben $sollvorgabe)
    {
        $sollvorgabe->delete();
        return response()->json([
            'message' => 'Sollvorgabe erfolgreich gelöscht'
        ]);
    }
}
