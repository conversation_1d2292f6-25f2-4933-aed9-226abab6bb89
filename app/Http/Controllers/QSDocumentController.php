<?php

namespace App\Http\Controllers;

use App\Models\QSDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class QSDocumentController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,json,xml,zip,rar,7z,tar,gz,bz2,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,json,xml,zip,rar,7z,tar,gz,bz2', // 10MB max
            'rotekarte_id' => 'required|exists:rotekarten,id',
            'description' => 'nullable|string|max:1000',
        ]);

        $file = $request->file('file');
        $originalFilename = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $filename = Str::uuid() . '.' . $extension;

        // Determine file type
        $fileType = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'pptx']) ? 'image' : 'document';

        // Store file
        $path = Storage::disk('public')->putFileAs(
            'qs-documents/' . $request->rotekarte_id,
            $file,
            $filename
        );

        // Create document record
        $document = QSDocument::create([
            'rotekarte_id' => $request->rotekarte_id,
            'filename' => $filename,
            'original_filename' => $originalFilename,
            'path' => $path,
            'mime_type' => $file->getMimeType(),
            'file_type' => $fileType,
            'file_size' => $file->getSize(),
            'description' => $request->description,
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'document' => $document,
                'url' => asset('storage/' . $document->path),
            ]);
        }

        return Inertia::render('QS/Index', [
            'success' => true,
            'document' => array_merge($document->toArray(), [
                'url' => asset('storage/' . $document->path)
            ])
        ]);
    }

    public function destroy(QSDocument $document)
    {
        try {
            // Delete file from storage
            Storage::disk('public')->delete($document->path);

            // Delete record
            $document->delete();

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Löschen der Datei.'
            ], 500);
        }
    }

    public function index(Request $request)
    {
        $request->validate([
            'rotekarte_id' => 'required|exists:rotekarten,id',
        ]);

        $documents = QSDocument::where('rotekarte_id', $request->rotekarte_id)
            ->latest()
            ->get()
            ->map(function ($document) {
                return [
                    'id' => $document->id,
                    'filename' => $document->original_filename,
                    'file_type' => $document->file_type,
                    'file_size' => $document->file_size,
                    'created_at' => $document->created_at->format('d.m.Y H:i'),
                    'url' => asset('storage/' . $document->path),
                    'description' => $document->description
                ];
            });

        return response()->json($documents);
    }
}
