<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\TeilNummer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

class TeilNummerController extends Controller
{
    /**
     * Display the teil_nummern page with data.
     */
    public function index(Request $request)
    {
        $query = TeilNummer::query();
        
        // Suche
        if ($request->has('search') && !empty($request->search)) {
            $query->where('teil_nummer', 'like', "%{$request->search}%");
        }
        
        // Sortierung
        $sortField = $request->sort_field ?? 'teil_nummer';
        $sortDirection = $request->sort_direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);
        
        // Paginierung
        $teilnummern = $query->paginate(10)->withQueryString();
        
        // Unterscheidung zwischen Inertia-Anfragen und AJAX/API-Anfragen
        if ($request->ajax() && !$request->header('X-Inertia')) {
            return response()->json([
                'teilnummern' => $teilnummern
            ]);
        }
        
        // Normale Anfrage mit Inertia View (oder über Inertia AJAX)
        return Inertia::render('TeilNummer/Index', [
            'teilnummern' => $teilnummern,
        ]);
    }
    
    /**
     * Store a new teil_nummer record.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'teil_nummer' => 'required|string|max:255|unique:teil_nummern,teil_nummer',
            'preis' => 'nullable|numeric|min:0',
            'gewicht' => 'nullable|numeric|min:0',
            'schrott_preis' => 'nullable|numeric|min:0',
        ]);
        
        $teilnummer = TeilNummer::create($validated);
        
        $message = 'Teilenummer wurde erfolgreich gespeichert.';
        
        // Bei AJAX/Axios-Anfragen JSON zurückgeben
        if ($request->ajax() && !$request->header('X-Inertia')) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $teilnummer
            ], 201);
        }
        
        return redirect()->back()->with('success', $message);
    }
    
    /**
     * Update an existing teil_nummer record.
     */
    public function update(Request $request, $id)
    {
        // Manuell das Model finden
        $teilnummer = TeilNummer::findOrFail($id);
        
        $validated = $request->validate([
            'teil_nummer' => "required|string|max:255|unique:teil_nummern,teil_nummer,{$teilnummer->id}",
            'preis' => 'nullable|numeric|min:0',
            'gewicht' => 'nullable|numeric|min:0',
            'schrott_preis' => 'nullable|numeric|min:0',
        ]);
        
        $teilnummer->update($validated);
        
        $message = 'Teilenummer wurde erfolgreich aktualisiert.';
        
        // Bei AJAX/Axios-Anfragen JSON zurückgeben
        if ($request->ajax() && !$request->header('X-Inertia')) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $teilnummer
            ]);
        }
        
        return redirect()->back()->with('success', $message);
    }
    
    /**
     * Delete a teil_nummer record.
     */
    public function destroy(Request $request, $id)
    {
        // Manuell das Model finden, anstatt Model-Binding zu verwenden
        $teilnummer = TeilNummer::findOrFail($id);
        $teilnummer->delete();
        
        $message = 'Teilenummer wurde erfolgreich gelöscht.';
        
        // Bei AJAX/Axios-Anfragen JSON zurückgeben
        if ($request->ajax() && !$request->header('X-Inertia')) {
            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        }
        
        return redirect()->back()->with('success', $message);
    }

    /**
     * Import part numbers from the rotekarten table.
     */
    public function importFromRotekarten(Request $request)
    {
        try {
            // Get data from rotekarten table ordered by formanlage_daten
            $rotekarten = DB::table('rotekarten')
                ->orderBy('formanlage_daten')
                ->get();

            \Log::info('TeilNummer Import: ' . $rotekarten->count() . ' Rotekarten gefunden.');
            
            if ($rotekarten->isEmpty()) {
                $message = "Import fehlgeschlagen: Keine Rotekarten in der Datenbank gefunden.";
                
                if ($request->ajax() && !$request->header('X-Inertia')) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ]);
                }
                
                return redirect()->back()->with('error', $message);
            }

            $imported = 0;
            $skipped = 0;
            $missing = 0;
            $importedTeilNummern = [];

            foreach ($rotekarten as $rotekarte) {
                // Extract formanlage_daten as JSON
                $formanlageDaten = json_decode($rotekarte->formanlage_daten ?? '{}', true);
                
                // Die Teile-Array aus den Formanlage-Daten extrahieren
                $teile = $formanlageDaten['teile'] ?? [];
                
                if (empty($teile)) {
                    $missing++;
                    \Log::info("Keine Teile in Rotekarte ID: " . ($rotekarte->id ?? 'unknown'));
                    continue;
                }
                
                // Über alle Teile in der Rotekarte iterieren
                foreach ($teile as $teil) {
                    // Teilenummer aus dem Teil-Array extrahieren
                    $teilNummer = $teil['teilenummer'] ?? null;
                    
                    if ($teilNummer) {
                        // Check if this teil_nummer already exists
                        $exists = TeilNummer::where('teil_nummer', $teilNummer)->exists();
                        
                        if (!$exists) {
                            // Create new TeilNummer record
                            $newTeilNummer = TeilNummer::create([
                                'teil_nummer' => $teilNummer,
                                // Add default values for other fields
                                'preis' => 0,
                                'gewicht' => 0,
                                'schrott_preis' => 0,
                            ]);
                            $imported++;
                            $importedTeilNummern[] = $teilNummer;
                            \Log::info("TeilNummer importiert: {$teilNummer}");
                        } else {
                            $skipped++;
                            \Log::info("TeilNummer übersprungen (existiert bereits): {$teilNummer}");
                        }
                    } else {
                        $missing++;
                        \Log::info("Keine Teilenummer im Teil-Objekt in Rotekarte ID: " . ($rotekarte->id ?? 'unknown'));
                    }
                }
            }

            $detailMessage = "Import abgeschlossen. {$imported} Teilenummern importiert, {$skipped} übersprungen, {$missing} ohne Teilenummer.";
            $importDetails = [
                'imported' => $imported,
                'skipped' => $skipped,
                'missing' => $missing,
                'importedTeilNummern' => $importedTeilNummern
            ];
            
            \Log::info($detailMessage);
            
            // Bei AJAX/Axios-Anfragen JSON zurückgeben
            if ($request->ajax() && !$request->header('X-Inertia')) {
                return response()->json([
                    'success' => true,
                    'message' => $detailMessage,
                    'details' => $importDetails
                ]);
            }
            
            return redirect()->back()->with('success', $detailMessage);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Import von Teilenummern: ' . $e->getMessage());
            
            $message = 'Fehler beim Import: ' . $e->getMessage();
            
            // Bei AJAX/Axios-Anfragen JSON zurückgeben
            if ($request->ajax() && !$request->header('X-Inertia')) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'error' => $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()->with('error', $message);
        }
    }

    /**
     * Get current scrap metal price from BDSV.
     */
    public function getSchrottpreis(Request $request)
    {
        try {
            // Versuche, aus dem Cache zu laden (1 Tag gültig)
            $cachedPrice = Cache::get('schrottpreis');
            
            if ($cachedPrice) {
                return response()->json([
                    'success' => true,
                    'preis' => $cachedPrice['preis'],
                    'source' => $cachedPrice['source'],
                    'cached' => true
                ]);
            }
            
            // Von BDSV-Website abrufen
            try {
                $response = Http::get('https://www.bdsv.org/');
                
                if ($response->successful()) {
                    $html = $response->body();
                    
                    // Preise mit regulären Ausdrücken extrahieren
                    // Wir suchen nach dem Preis für Sorte 2/8, die für allgemeine Schrottpreise am repräsentativsten ist
                    preg_match('/Sorte 2\/8\s*<\/.*?>\s*<.*?>\s*(\d+[\.,]\d+)\s*€\/t/s', $html, $matches);
                    
                    if (isset($matches[1])) {
                        // Preis in Euro pro Tonne zu Euro pro Kilogramm umrechnen
                        $pricePerTon = floatval(str_replace(',', '.', $matches[1]));
                        $pricePerKg = round($pricePerTon / 1000, 2);
                        
                        // Im Cache speichern (für 1 Tag)
                        Cache::put('schrottpreis', [
                            'preis' => $pricePerKg,
                            'source' => 'BDSV - Bundesvereinigung Deutscher Stahlrecycling'
                        ], 86400); // 86400 Sekunden = 24 Stunden
                        
                        return response()->json([
                            'success' => true,
                            'preis' => $pricePerKg,
                            'source' => 'BDSV - Bundesvereinigung Deutscher Stahlrecycling'
                        ]);
                    }
                }
                
                // Falls die Extraktion fehlschlägt, verwenden wir den aktuellen BDSV-Preis manuell
                // Aktueller Preis (02/2025): 314,8 €/t für Sorte 2/8
                $manualPricePerTon = 314.8;
                $manualPricePerKg = round($manualPricePerTon / 1000, 2); // 0,31 €/kg
                
                Cache::put('schrottpreis', [
                    'preis' => $manualPricePerKg,
                    'source' => 'BDSV - Bundesvereinigung Deutscher Stahlrecycling (manuell)'
                ], 86400);
                
                return response()->json([
                    'success' => true,
                    'preis' => $manualPricePerKg,
                    'source' => 'BDSV - Bundesvereinigung Deutscher Stahlrecycling (manuell)'
                ]);
                
            } catch (\Exception $e) {
                \Log::warning('Fehler beim Abrufen des Schrottpreises von BDSV: ' . $e->getMessage());
            }
            
            // Fallback auf realistischen Wert, wenn alles fehlschlägt
            $fallbackPrice = 0.31; // Basierend auf BDSV-Durchschnittspreis (02/2025)
            
            Cache::put('schrottpreis', [
                'preis' => $fallbackPrice,
                'source' => 'BDSV-Durchschnittspreis (Fallback)'
            ], 86400);
            
            return response()->json([
                'success' => true,
                'preis' => $fallbackPrice,
                'source' => 'BDSV-Durchschnittspreis (Fallback)'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Fehler beim Abrufen des Schrottpreises: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Ein unerwarteter Fehler ist aufgetreten.',
            ], 500);
        }
    }
    
    /**
     * Update scrap metal price for all or selected teil_nummer.
     */
    public function updateSchrottpreis(Request $request)
    {
        try {
            $validated = $request->validate([
                'preis' => 'required|numeric|min:0',
                'apply_to' => 'required|in:all,selected',
                'teil_nummer_id' => 'nullable|required_if:apply_to,selected|exists:teil_nummern,id',
            ]);
            
            $preis = $validated['preis'];
            $applyTo = $validated['apply_to'];
            $teilNummerId = $validated['teil_nummer_id'] ?? null;
            
            $query = TeilNummer::query();
            
            // Filter anwenden
            if ($applyTo === 'selected' && $teilNummerId) {
                $query->where('id', $teilNummerId);
            }
            
            // Aktualisieren
            $updated = $query->update([
                'schrott_preis' => $preis
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Schrottpreis erfolgreich aktualisiert.',
                'updated' => $updated
            ]);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Aktualisieren des Schrottpreises: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Aktualisieren des Schrottpreises: ' . $e->getMessage(),
            ], 500);
        }
    }
}
