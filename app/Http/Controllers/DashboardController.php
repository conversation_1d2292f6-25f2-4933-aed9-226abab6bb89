<?php

namespace App\Http\Controllers;

use App\Models\Rotekarte;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use PDF;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $query = Rotekarte::query()
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->search) {
            $search = strtolower($request->search);
            // Remove RT- prefix if present for ID search (case insensitive)
            $searchId = preg_replace('/^rt-/i', '', $search);

            $query->where(function ($q) use ($search, $searchId) {
                $q->where('id', $searchId)  // Exact match for ID
                    ->orWhere('id', 'like', "%{$searchId}%")  // Partial match for ID
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(spektrometer_daten, "$.name"))) LIKE ?', ["%{$search}%"])
                    ->orWhereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(spektrometer_daten, "$.chargennummer"))) LIKE ?', ["%{$search}%"])
                    // Suche nach Teilenummern im JSON-Feld formanlage_daten.teile
                    ->orWhereRaw('JSON_SEARCH(LOWER(JSON_EXTRACT(formanlage_daten, "$.teile")), "one", ?) IS NOT NULL', ["%{$search}%"])
                    // Alternative Suche für Teilenummern im Teile-Array
                    ->orWhereRaw('EXISTS (
                        SELECT 1 
                        FROM JSON_TABLE(
                            JSON_EXTRACT(formanlage_daten, "$.teile"),
                            "$[*]" COLUMNS(
                                teilenummer VARCHAR(255) PATH "$.teilenummer"
                            )
                        ) as teile_table
                        WHERE LOWER(teile_table.teilenummer) LIKE ?
                    )', ["%{$search}%"]);
            });
        }

        // Status filter
        if ($request->status) {
            if ($request->status === 'Offen') {
                $query->where(function ($q) {
                    $q->whereNull('qs_daten')
                      ->orWhere('qs_daten->status', 'Offen')
                      ->orWhereRaw("JSON_TYPE(qs_daten) = 'NULL'");
                });
            } else {
                $query->where('qs_daten->status', $request->status);
            }
        }

        // Abteilung filter
        if ($request->abteilung) {
            $query->where('spektrometer_daten->abteilung', $request->abteilung);
        }

        // Date range filter
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $rotekarten = $query->paginate(10)
            ->withQueryString();

        // Get total counts for all categories
        $totalCounts = [
            'total' => Rotekarte::count(),
            'offen' => Rotekarte::where(function ($q) {
                $q->whereNull('qs_daten')
                  ->orWhere('qs_daten->status', 'Offen')
                  ->orWhereRaw("JSON_TYPE(qs_daten) = 'NULL'");
            })->count(),
            'inBearbeitung' => Rotekarte::where('qs_daten->status', 'In Bearbeitung')->count(),
            'abgeschlossen' => Rotekarte::where('qs_daten->status', 'Abgeschlossen')->count(),
            'kleinguss' => Rotekarte::where('spektrometer_daten->abteilung', 'NG')->count(),
            'grossguss' => Rotekarte::whereIn('spektrometer_daten->abteilung', ['GG', 'HF'])->count()
        ];

        // Get QS inspector statistics
        $qsPrueferStats = Rotekarte::whereNotNull('qs_daten->pruefer')
            ->whereNotNull('qs_daten->status')
            ->select(
                DB::raw('JSON_UNQUOTE(JSON_EXTRACT(qs_daten, "$.pruefer[0].name")) as pruefer_name'),
                DB::raw('COUNT(*) as anzahl')
            )
            ->groupBy('pruefer_name')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->pruefer_name,
                    'anzahl' => $item->anzahl
                ];
            });

        return Inertia::render('Dashboard', [
            'rotekarten' => $rotekarten,
            'filters' => $request->only(['search', 'status', 'abteilung', 'date_from', 'date_to']),
            'totalCounts' => $totalCounts,
            'qsPrueferStats' => $qsPrueferStats
        ]);
    }

    /**
     * Zeigt die offenen Rotekarten in einer druckbaren Übersicht an.
     */
    public function offeneKartenDruck()
    {
        // Suche nach allen offenen Rotekarten
        $offeneKarten = Rotekarte::where(function ($query) {
                $query->whereNull('qs_daten')
                    ->orWhere('qs_daten->status', 'Offen')
                    ->orWhereRaw("JSON_TYPE(qs_daten) = 'NULL'");
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Rotekarte/OffeneKartenDruck', [
            'offeneKarten' => $offeneKarten
        ]);
    }

    /**
     * Generiert eine PDF der offenen Rotekarten und bietet sie zum Download an.
     */
    public function offeneKartenPdf()
    {
        // Suche nach allen offenen Rotekarten
        $offeneKarten = Rotekarte::where(function ($query) {
                $query->whereNull('qs_daten')
                    ->orWhere('qs_daten->status', 'Offen')
                    ->orWhereRaw("JSON_TYPE(qs_daten) = 'NULL'");
            })
            ->orderBy('created_at', 'desc')
            ->get();

        $pdf = \PDF::loadView('pdf.offene-karten', [
            'offeneKarten' => $offeneKarten,
            'datum' => now()->format('d.m.Y'),
            'zeit' => now()->format('H:i')
        ]);

        // Set PDF options
        $pdf->setPaper('A4');
        $pdf->setOption(['dpi' => 150, 'defaultFont' => 'sans-serif']);

        // Create a filename with the current date
        $filename = 'Offene_Rotekarten_' . now()->format('Y-m-d_H-i') . '.pdf';

        // Return PDF for download
        return $pdf->download($filename);
    }

    /**
     * Zeigt die Übersichtsseite für "In Bearbeitung" Rotekarten an
     * 
     * @return \Inertia\Response
     */
    public function inBearbeitungKartenDruck()
    {
        // Hole alle Rotekarten mit Status "In Bearbeitung" und sortiere sie nach Erstellungsdatum absteigend
        $inBearbeitungKarten = Rotekarte::where('qs_daten->status', 'In Bearbeitung')
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Rotekarte/InBearbeitungKartenDruck', [
            'inBearbeitungKarten' => $inBearbeitungKarten,
        ]);
    }

    /**
     * Generiert ein PDF mit allen "In Bearbeitung" Rotekarten
     * 
     * @return \Illuminate\Http\Response
     */
    public function inBearbeitungKartenPdf()
    {
        // Hole alle Rotekarten mit Status "In Bearbeitung" und sortiere sie nach Erstellungsdatum absteigend
        $inBearbeitungKarten = Rotekarte::where('qs_daten->status', 'In Bearbeitung')
            ->orderBy('created_at', 'desc')
            ->get();

        // Aktuelles Datum und Uhrzeit für den PDF-Header
        $datum = now()->format('d.m.Y');
        $zeit = now()->format('H:i');

        // PDF generieren
        $pdf = PDF::loadView('pdf.in-bearbeitung-karten', [
            'inBearbeitungKarten' => $inBearbeitungKarten,
            'datum' => $datum,
            'zeit' => $zeit
        ]);

        // Aktuelles Datum für den Dateinamen
        $dateiname = 'In_Bearbeitung_Rotekarten_' . now()->format('Y-m-d') . '.pdf';

        // PDF zum Download zurückgeben
        return $pdf->download($dateiname);
    }
}
