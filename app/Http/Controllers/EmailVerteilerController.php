<?php

namespace App\Http\Controllers;

use App\Models\EmailVerteiler;
use Illuminate\Http\Request;
use Inertia\Inertia;

class EmailVerteilerController extends Controller
{
    public function index()
    {
        $verteiler = EmailVerteiler::orderBy('type')->orderBy('name')->get();
        
        return Inertia::render('EmailVerteiler/Index', [
            'verteiler' => $verteiler
        ]);
    }
    
    public function store(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:email_verteiler,email',
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:statistik,management,kg,hf,qs_gg,formanlage,spektrometer,gussnachbehandlung',
            'is_cc' => 'boolean'
        ]);
        
        EmailVerteiler::create($request->all());
        
        return redirect()->back()->with('success', 'E-Mail-Adresse wurde hinzugefügt.');
    }
    
    public function update(Request $request, EmailVerteiler $emailVerteiler)
    {
        $request->validate([
            'email' => 'required|email|unique:email_verteiler,email,' . $emailVerteiler->id,
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:statistik,management,kg,hf,qs_gg,formanlage,spektrometer,gussnachbehandlung',
            'is_cc' => 'boolean'
        ]);
        
        $emailVerteiler->update($request->all());
        
        return redirect()->back()->with('success', 'E-Mail-Adresse wurde aktualisiert.');
    }
    
    public function destroy(EmailVerteiler $emailVerteiler)
    {
        $emailVerteiler->delete();
        
        return redirect()->back()->with('success', 'E-Mail-Adresse wurde gelöscht.');
    }
}
