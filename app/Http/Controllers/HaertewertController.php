<?php

namespace App\Http\Controllers;

use App\Models\Haertewert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class HaertewertController extends Controller
{
    /**
     * Zeigt eine Liste aller Härtewerte.
     */
    public function index()
    {
        $haertewerte = Haertewert::orderBy('created_at', 'desc')->get();

        // Nur für reine API-Anfragen JSON zurückgeben, nicht für Inertia-Anfragen
        if ((request()->wantsJson() || request()->ajax()) && !request()->hasHeader('X-Inertia')) {
            return response()->json($haertewerte);
        }

        // Lade Eisenmarken aus der haerte_werte Tabelle
        try {
            $hbwData = DB::table('haerte_werte')->first();
            $materials = $hbwData ? json_decode($hbwData->werte, true) : [];
            
            // Extrahiere die verschiedenen Werkstofftypen (Eisenmarken)
            $eisenmarken = [];
            foreach ($materials as $material) {
                if (isset($material['werkstoff']) && !in_array($material['werkstoff'], $eisenmarken)) {
                    $eisenmarken[] = $material['werkstoff'];
                }
            }
            
            // Fallback-Liste, falls keine Daten in der Datenbank gefunden wurden
            if (empty($eisenmarken)) {
                $eisenmarken = ['GJS400', 'GJS500', 'GJS600', 'GJL250', 'GJL300', 'GJL350', 'EN-GJS-400-15', 'EN-GJS-500-7', 'EN-GJS-600-3'];
            }
        } catch (\Exception $e) {
            // Fallback bei Fehlern
            \Log::error('Fehler beim Laden der Eisenmarken: ' . $e->getMessage());
            $eisenmarken = ['GJS400', 'GJS500', 'GJS600', 'GJL250', 'GJL300', 'GJL350', 'EN-GJS-400-15', 'EN-GJS-500-7', 'EN-GJS-600-3'];
        }
        
        // Sortiere die Eisenmarken
        sort($eisenmarken);
        
        return Inertia::render('Haertewerte/Index', [
            'haertewerte' => $haertewerte,
            'eisenmarken' => $eisenmarken
        ]);
    }

    /**
     * Speichert neue Härtewerte.
     */
    public function store(Request $request)
    {
        try {
            // Logging zur Fehlersuche
            \Log::info('Haertewert-Daten erhalten:', $request->all());
            
            // Angepasste Validierung mit weniger strengen Regeln
            $validated = $request->validate([
                'teilenummer' => 'required|string',
                'auftragsnummer' => 'required|string',
                'chargenummer' => 'required|string',
                'eisenmarke' => 'required|string',
                'soll_von' => 'required|numeric',
                'soll_bis' => 'required|numeric|gte:soll_von',
                'geprueft_von' => 'required|string',
                'wanddicke' => 'nullable|string', // Sicherstellen, dass wanddicke validiert wird
                'haertewerte' => 'required|array',
                'haertewerte.*.ist_wert' => 'required|numeric',
                'haertewerte.*.pruefposition' => 'required|string',
                'haertewerte.*.pruefverfahren' => 'required|string',
                'haertewerte.*.nestnummer' => 'nullable|string',
                'haertewerte.*.bemerkung' => 'nullable|string',
                'haertewerte.*.wanddicke' => 'nullable|string', // Validierung für individuelle Wanddicken
            ], [
                'teilenummer.required' => 'Die Teilenummer ist erforderlich.',
                'auftragsnummer.required' => 'Die Auftragsnummer ist erforderlich.',
                'chargenummer.required' => 'Die Chargennummer ist erforderlich.',
                'eisenmarke.required' => 'Die Eisenmarke ist erforderlich.',
                'soll_von.required' => 'Der Soll-von-Wert ist erforderlich.',
                'soll_von.numeric' => 'Der Soll-von-Wert muss eine Zahl sein.',
                'soll_bis.required' => 'Der Soll-bis-Wert ist erforderlich.',
                'soll_bis.numeric' => 'Der Soll-bis-Wert muss eine Zahl sein.',
                'soll_bis.gte' => 'Der Soll-bis-Wert muss größer oder gleich dem Soll-von-Wert sein.',
                'geprueft_von.required' => 'Der Name des Prüfers ist erforderlich.',
                'wanddicke.string' => 'Die Wanddicke muss eine Zeichenkette sein.',
                'haertewerte.required' => 'Es müssen Härtewerte vorhanden sind.',
                'haertewerte.min' => 'Es muss mindestens ein Härtewert eingegeben werden.',
                'haertewerte.*.ist_wert.required' => 'Der IST-Wert ist erforderlich.',
                'haertewerte.*.ist_wert.numeric' => 'Der IST-Wert muss eine Zahl sein.',
                'haertewerte.*.pruefposition.required' => 'Die Prüfposition ist erforderlich.',
                'haertewerte.*.pruefverfahren.required' => 'Das Prüfverfahren ist erforderlich.',
                'haertewerte.*.nestnummer.string' => 'Die Nestnummer muss eine Zeichenkette sein.',
            ]);

            DB::beginTransaction();

            // Einen Auftragsdatensatz erstellen (falls benötigt für Gruppierung)
            // Hier können wir ein auftragsbezogenes Modell verwenden, falls vorhanden
            
            $haertewerte = [];
            foreach ($validated['haertewerte'] as $haertewertData) {
                // Log der einzelnen Härtewert-Daten vor dem Einfügen
                \Log::info('Verarbeite Härtewert:', $haertewertData);
                
                $haertewert = Haertewert::create([
                    'teilenummer' => $validated['teilenummer'],
                    'auftragsnummer' => $validated['auftragsnummer'],
                    'chargenummer' => $validated['chargenummer'],
                    'eisenmarke' => $validated['eisenmarke'],
                    'soll_von' => $validated['soll_von'],
                    'soll_bis' => $validated['soll_bis'],
                    'geprueft_von' => $validated['geprueft_von'],
                    'wanddicke' => $haertewertData['wanddicke'] ?? $validated['wanddicke'] ?? null,
                    'ist_wert' => $haertewertData['ist_wert'],
                    'pruefposition' => $haertewertData['pruefposition'],
                    'pruefverfahren' => $haertewertData['pruefverfahren'],
                    'nestnummer' => $haertewertData['nestnummer'] ?? null,
                    'bemerkung' => $haertewertData['bemerkung'] ?? null,
                ]);
                $haertewerte[] = $haertewert;
                
                // Log des eingefügten Härtewerts
                \Log::info('Härtewert gespeichert:', $haertewert->toArray());
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Härtewerte erfolgreich gespeichert',
                'data' => $haertewerte
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();
            Log::error('Validierungsfehler beim Speichern der Härtewerte:', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Validierungsfehler beim Speichern der Härtewerte',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Fehler beim Speichern der Härtewerte: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Speichern der Härtewerte: ' . $e->getMessage()
            ], 500);
        }
    }

    public function search(Request $request)
    {
        try {
            $query = Haertewert::query();

            if ($request->filled('teilenummer')) {
                $query->where('teilenummer', 'like', '%' . $request->teilenummer . '%');
            }

            if ($request->filled('auftragsnummer')) {
                $query->where('auftragsnummer', 'like', '%' . $request->auftragsnummer . '%');
            }

            if ($request->filled('chargenummer')) {
                $query->where('chargenummer', 'like', '%' . $request->chargenummer . '%');
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', Carbon::parse($request->date_from));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', Carbon::parse($request->date_to));
            }

            $haertewerte = $query->orderBy('created_at', 'desc')->paginate(10);

            return response()->json([
                'success' => true,
                'data' => $haertewerte
            ]);
        } catch (\Exception $e) {
            Log::error('Fehler bei der Suche nach Härtewerten: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler bei der Suche nach Härtewerten'
            ], 500);
        }
    }

    /**
     * Entfernt den angegebenen Härtewert oder mehrere Härtewerte aus der Datenbank.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Haertewert|null  $haertewert
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Haertewert $haertewert = null)
    {
        try {
            // Wenn IDs im Request-Body übergeben wurden
            if ($request->has('ids')) {
                $ids = $request->input('ids');
                Haertewert::whereIn('id', $ids)->delete();
                return response()->json([
                    'success' => true, 
                    'message' => count($ids) > 1 ? 'Härtewerte erfolgreich gelöscht.' : 'Härtewert erfolgreich gelöscht.'
                ]);
            }
            
            // Wenn ein einzelner Härtewert-Parameter in der Route übergeben wurde
            if ($haertewert) {
                $haertewert->delete();
                return response()->json(['success' => true, 'message' => 'Härtewert erfolgreich gelöscht.']);
            }
            
            return response()->json(['success' => false, 'message' => 'Kein Härtewert zum Löschen angegeben.'], 400);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Löschen des Härtewerts: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Beim Löschen ist ein Fehler aufgetreten: ' . $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            Log::info('Update request data:', $request->all());

            $validated = $request->validate([
                'teilenummer' => 'required|string',
                'auftragsnummer' => 'required|string',
                'chargenummer' => 'required|string',
                'soll_von' => 'required|numeric',
                'soll_bis' => 'required|numeric|gt:soll_von',
                'geprueft_von' => 'required|string',
                'haertewerte' => 'required|array|min:1',
                'haertewerte.*.id' => 'nullable|integer',  // Removed exists check
                'haertewerte.*.ist_wert' => 'required|numeric|min:0',
                'haertewerte.*.pruefposition' => 'required|string|in:UT,OT,Stehend,Zugstrebe,Zahnkranz,Andere',
                'haertewerte.*.pruefverfahren' => 'required|string|in:Brinellhärte,Scherkraft-Härteprüfer,Optisches Auslesen',
                'haertewerte.*.bemerkung' => 'nullable|string'
            ], [
                'haertewerte.*.ist_wert.required' => 'Der Ist-Wert ist erforderlich',
                'haertewerte.*.ist_wert.numeric' => 'Der Ist-Wert muss eine Zahl sein',
                'haertewerte.*.ist_wert.min' => 'Der Ist-Wert muss größer als 0 sein',
                'haertewerte.*.pruefposition.required' => 'Die Prüfposition ist erforderlich',
                'haertewerte.*.pruefposition.in' => 'Die Prüfposition muss einer der folgenden Werte sein: UT, OT, Stehend, Zugstrebe, Zahnkranz, Andere',
                'haertewerte.*.pruefverfahren.required' => 'Das Prüfverfahren ist erforderlich',
                'haertewerte.*.pruefverfahren.in' => 'Das Prüfverfahren muss einer der folgenden Werte sein: Brinellhärte, Scherkraft-Härteprüfer, Optisches Auslesen'
            ]);

            DB::beginTransaction();

            // Finde den Auftrag
            $auftrag = HaertewertAuftrag::findOrFail($id);
            
            // Aktualisiere die Auftragsdaten
            $auftrag->update([
                'teilenummer' => $validated['teilenummer'],
                'auftragsnummer' => $validated['auftragsnummer'],
                'chargenummer' => $validated['chargenummer'],
                'soll_von' => $validated['soll_von'],
                'soll_bis' => $validated['soll_bis'],
                'geprueft_von' => $validated['geprueft_von']
            ]);

            // Sammle alle IDs der existierenden Härtewerte
            $existingIds = $auftrag->haertewerte()->pluck('id')->toArray();
            
            // Sammle alle IDs der gesendeten Härtewerte
            $updatedIds = collect($validated['haertewerte'])
                ->filter(function ($hw) {
                    return isset($hw['id']);
                })
                ->pluck('id')
                ->toArray();

            // Lösche Härtewerte, die nicht mehr in der Liste sind
            $auftrag->haertewerte()
                ->whereIn('id', array_diff($existingIds, $updatedIds))
                ->delete();

            // Aktualisiere oder erstelle Härtewerte
            foreach ($validated['haertewerte'] as $haertewert) {
                if (isset($haertewert['id'])) {
                    // Aktualisiere existierenden Härtewert
                    $auftrag->haertewerte()
                        ->where('id', $haertewert['id'])
                        ->update([
                            'ist_wert' => $haertewert['ist_wert'],
                            'pruefposition' => $haertewert['pruefposition'],
                            'pruefverfahren' => $haertewert['pruefverfahren'],
                            'bemerkung' => $haertewert['bemerkung'] ?? null
                        ]);
                } else {
                    // Erstelle neuen Härtewert
                    $auftrag->haertewerte()->create([
                        'ist_wert' => $haertewert['ist_wert'],
                        'pruefposition' => $haertewert['pruefposition'],
                        'pruefverfahren' => $haertewert['pruefverfahren'],
                        'bemerkung' => $haertewert['bemerkung'] ?? null
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Härtewerte wurden erfolgreich aktualisiert',
                'data' => $auftrag->load('haertewerte')
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            Log::error('Validation error:', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validierungsfehler',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating haertewerte:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Beim Aktualisieren der Härtewerte ist ein Fehler aufgetreten'
            ], 500);
        }
    }

    public function getSollwerteByEisenmarke(Request $request)
    {
        try {
            $eisenmarke = $request->input('eisenmarke');
            
            if (!$eisenmarke) {
                return response()->json([
                    'success' => false,
                    'message' => 'Keine Eisenmarke angegeben.'
                ]);
            }
            
            // Daten aus der haerte_werte Tabelle abrufen
            $hbwData = DB::table('haerte_werte')->first();
            if (!$hbwData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Keine Härtewerttabelle in der Datenbank gefunden.'
                ]);
            }
            
            $materials = json_decode($hbwData->werte, true);
            
            // Filtern nach der ausgewählten Eisenmarke
            $filteredMaterials = array_filter($materials, function($material) use ($eisenmarke) {
                return isset($material['werkstoff']) && $material['werkstoff'] === $eisenmarke;
            });
            
            if (empty($filteredMaterials)) {
                \Log::warning("Keine Sollwerte für Eisenmarke '{$eisenmarke}' gefunden");
                
                // Fallback auf das hardcodierte Array, falls keine Daten gefunden wurden
                // (Optional: Diese Fallback-Logik kann später entfernt werden, wenn alle Daten in der DB sind)
                $hardcodedSollwerte = $this->getHardcodedSollwerte();
                if (isset($hardcodedSollwerte[$eisenmarke])) {
                    return response()->json([
                        'success' => true,
                        'data' => $hardcodedSollwerte[$eisenmarke],
                        'source' => 'fallback'
                    ]);
                }
                
                return response()->json([
                    'success' => false,
                    'message' => 'Keine Daten für diese Eisenmarke gefunden.'
                ]);
            }
            
            // Daten für die Anzeige aufbereiten
            $wanddicken = [];
            $werte = [];
            
            foreach ($filteredMaterials as $material) {
                if (isset($material['wanddicke'])) {
                    $wanddicke = $material['wanddicke'];
                    $wanddicken[] = $wanddicke;
                    
                    if (isset($material['toleranz_min']) && isset($material['toleranz_max'])) {
                        $werte[$wanddicke] = [
                            'von' => (int)$material['toleranz_min'],
                            'bis' => (int)$material['toleranz_max']
                        ];
                    }
                }
            }
            
            // Eindeutige Wanddicken und aufsteigend sortieren
            $wanddicken = array_unique($wanddicken);
            sort($wanddicken);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'wanddicken' => $wanddicken,
                    'werte' => $werte
                ],
                'source' => 'database'
            ]);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Abrufen der Sollwerte: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Fehler beim Abrufen der Sollwerte: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Gibt die hardcodierten Sollwerte zurück (als Fallback)
     */
    private function getHardcodedSollwerte()
    {
        return [
            'GJS400' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 170, 'bis' => 210],
                    '15-30' => ['von' => 160, 'bis' => 200],
                    '30-60' => ['von' => 150, 'bis' => 190],
                    '60-100' => ['von' => 145, 'bis' => 185],
                    '> 100' => ['von' => 140, 'bis' => 180],
                ]
            ],
            'GJS500' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 185, 'bis' => 225],
                    '15-30' => ['von' => 175, 'bis' => 215],
                    '30-60' => ['von' => 165, 'bis' => 205],
                    '60-100' => ['von' => 160, 'bis' => 200],
                    '> 100' => ['von' => 155, 'bis' => 195],
                ]
            ],
            'GJS600' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 200, 'bis' => 240],
                    '15-30' => ['von' => 190, 'bis' => 230],
                    '30-60' => ['von' => 180, 'bis' => 220],
                    '60-100' => ['von' => 175, 'bis' => 215],
                    '> 100' => ['von' => 170, 'bis' => 210],
                ]
            ],
            'GJL250' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 160, 'bis' => 200],
                    '15-30' => ['von' => 150, 'bis' => 190],
                    '30-60' => ['von' => 140, 'bis' => 180],
                    '60-100' => ['von' => 135, 'bis' => 175],
                    '> 100' => ['von' => 130, 'bis' => 170],
                ]
            ],
            'GJL300' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 175, 'bis' => 215],
                    '15-30' => ['von' => 165, 'bis' => 205],
                    '30-60' => ['von' => 155, 'bis' => 195],
                    '60-100' => ['von' => 150, 'bis' => 190],
                    '> 100' => ['von' => 145, 'bis' => 185],
                ]
            ],
            'GJL350' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 190, 'bis' => 230],
                    '15-30' => ['von' => 180, 'bis' => 220],
                    '30-60' => ['von' => 170, 'bis' => 210],
                    '60-100' => ['von' => 165, 'bis' => 205],
                    '> 100' => ['von' => 160, 'bis' => 200],
                ]
            ],
            'EN-GJS-400-15' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 170, 'bis' => 210],
                    '15-30' => ['von' => 160, 'bis' => 200],
                    '30-60' => ['von' => 150, 'bis' => 190],
                    '60-100' => ['von' => 145, 'bis' => 185],
                    '> 100' => ['von' => 140, 'bis' => 180],
                ]
            ],
            'EN-GJS-500-7' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 185, 'bis' => 225],
                    '15-30' => ['von' => 175, 'bis' => 215],
                    '30-60' => ['von' => 165, 'bis' => 205],
                    '60-100' => ['von' => 160, 'bis' => 200],
                    '> 100' => ['von' => 155, 'bis' => 195],
                ]
            ],
            'EN-GJS-600-3' => [
                'wanddicken' => ['5-15', '15-30', '30-60', '60-100', '> 100'],
                'werte' => [
                    '5-15' => ['von' => 200, 'bis' => 240],
                    '15-30' => ['von' => 190, 'bis' => 230],
                    '30-60' => ['von' => 180, 'bis' => 220],
                    '60-100' => ['von' => 175, 'bis' => 215],
                    '> 100' => ['von' => 170, 'bis' => 210],
                ]
            ],
        ];
    }

    /**
     * Speichert einen Kommentar für eine Gruppe von Härtewerten.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function saveKommentar(Request $request)
    {
        try {
            $validated = $request->validate([
                'auftragsnummer' => 'required|string',
                'teilenummer' => 'required|string',
                'chargenummer' => 'required|string',
                'kommentar' => 'nullable|string'
            ]);
            
            // Finde alle Härtewerte mit der gegebenen Auftragsnummer, Teilenummer und Chargenummer
            $haertewerte = Haertewert::where('auftragsnummer', $validated['auftragsnummer'])
                ->where('teilenummer', $validated['teilenummer'])
                ->where('chargenummer', $validated['chargenummer'])
                ->get();
            
            // Aktualisiere den Kommentar für alle gefundenen Härtewerte
            foreach ($haertewerte as $haertewert) {
                $haertewert->bemerkung = $validated['kommentar'];
                $haertewert->save();
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Kommentar erfolgreich gespeichert.',
                'count' => $haertewerte->count()
            ]);
        } catch (\Exception $e) {
            \Log::error('Fehler beim Speichern des Kommentars: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Beim Speichern des Kommentars ist ein Fehler aufgetreten: ' . $e->getMessage()
            ], 500);
        }
    }
}
