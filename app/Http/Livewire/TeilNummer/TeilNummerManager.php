<?php

declare(strict_types=1);

namespace App\Http\Livewire\TeilNummer;

use App\Models\TeilNummer;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class TeilNummerManager extends Component
{
    use WithPagination;

    // Form state properties
    public $teilNummer = '';
    public $preis = 0;
    public $gewicht = 0;
    public $schrottPreis = 0;
    
    // Edit mode
    public $editMode = false;
    public $editId = null;
    
    // Search and filtering
    public $search = '';
    public $sortField = 'id';
    public $sortDirection = 'asc';
    
    // Validation rules
    protected $rules = [
        'teilNummer' => 'required|string|max:255|unique:teil_nummern,teil_nummer',
        'preis' => 'nullable|numeric|min:0',
        'gewicht' => 'nullable|numeric|min:0',
        'schrottPreis' => 'nullable|numeric|min:0',
    ];

    // Update validation rules when editing
    public function updatedEditMode()
    {
        if ($this->editMode && $this->editId) {
            $this->rules['teilNummer'] = 'required|string|max:255|unique:teil_nummern,teil_nummer,' . $this->editId;
        } else {
            $this->rules['teilNummer'] = 'required|string|max:255|unique:teil_nummern,teil_nummer';
        }
    }
    
    // Render the component
    public function render()
    {
        return view('livewire.teil-nummer.teil-nummer-manager', [
            'teilNummern' => $this->getTeilNummern(),
        ]);
    }
    
    // Get Teil Nummern with search and sort
    private function getTeilNummern()
    {
        return TeilNummer::where('teil_nummer', 'like', "%{$this->search}%")
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(10);
    }
    
    // Sort by field
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }
    
    // Save a new Teil Nummer
    public function save()
    {
        $this->validate();
        
        TeilNummer::create([
            'teil_nummer' => $this->teilNummer,
            'preis' => $this->preis,
            'gewicht' => $this->gewicht,
            'schrott_preis' => $this->schrottPreis,
        ]);
        
        $this->reset(['teilNummer', 'preis', 'gewicht', 'schrottPreis']);
        $this->dispatchBrowserEvent('teil-nummer-saved', ['message' => 'Teilenummer wurde erfolgreich gespeichert.']);
    }
    
    // Edit a Teil Nummer
    public function edit($id)
    {
        $this->editMode = true;
        $this->editId = $id;
        
        $teilNummer = TeilNummer::findOrFail($id);
        $this->teilNummer = $teilNummer->teil_nummer;
        $this->preis = $teilNummer->preis;
        $this->gewicht = $teilNummer->gewicht;
        $this->schrottPreis = $teilNummer->schrott_preis;
        
        $this->updatedEditMode();
    }
    
    // Update a Teil Nummer
    public function update()
    {
        $this->validate();
        
        $teilNummer = TeilNummer::findOrFail($this->editId);
        $teilNummer->update([
            'teil_nummer' => $this->teilNummer,
            'preis' => $this->preis,
            'gewicht' => $this->gewicht,
            'schrott_preis' => $this->schrottPreis,
        ]);
        
        $this->reset(['teilNummer', 'preis', 'gewicht', 'schrottPreis', 'editMode', 'editId']);
        $this->dispatchBrowserEvent('teil-nummer-updated', ['message' => 'Teilenummer wurde erfolgreich aktualisiert.']);
    }
    
    // Delete a Teil Nummer
    public function delete($id)
    {
        TeilNummer::destroy($id);
        $this->dispatchBrowserEvent('teil-nummer-deleted', ['message' => 'Teilenummer wurde erfolgreich gelöscht.']);
    }
    
    // Cancel edit mode
    public function cancelEdit()
    {
        $this->reset(['teilNummer', 'preis', 'gewicht', 'schrottPreis', 'editMode', 'editId']);
    }
    
    // Import from Rotekarten
    public function importFromRotekarten()
    {
        try {
            // Get data from rotekarten table ordered by formanlage_daten
            $rotekarten = DB::table('rotekarten')
                ->orderBy('formanlage_daten')
                ->get();

            $imported = 0;
            $skipped = 0;

            foreach ($rotekarten as $rotekarte) {
                // Extract formanlage_daten as JSON
                $formanlageDaten = json_decode($rotekarte->formanlage_daten ?? '{}', true);
                
                // Extract the teil_nummer if available
                $teilNummer = $formanlageDaten['teil_nummer'] ?? null;
                
                if ($teilNummer) {
                    // Check if this teil_nummer already exists
                    $exists = TeilNummer::where('teil_nummer', $teilNummer)->exists();
                    
                    if (!$exists) {
                        // Create new TeilNummer record
                        TeilNummer::create([
                            'teil_nummer' => $teilNummer,
                            // Add default values for other fields
                            'preis' => 0,
                            'gewicht' => 0,
                            'schrott_preis' => 0,
                        ]);
                        $imported++;
                    } else {
                        $skipped++;
                    }
                }
            }

            $this->dispatchBrowserEvent('teil-nummer-imported', [
                'message' => "Import abgeschlossen. {$imported} Teilenummern importiert, {$skipped} übersprungen.",
            ]);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('teil-nummer-import-error', [
                'message' => 'Fehler beim Import: ' . $e->getMessage(),
            ]);
        }
    }
} 