<?php

declare(strict_types=1);

namespace App\Http\Livewire;

use App\Models\Pruefkatalog;
use App\Models\Rotekarte;
use Livewire\Component;

class SpektrometerFehlercode extends Component
{
    /** @var string|null */
    public $fehlercode = null;
    
    /** @var array */
    public $availableFehlercodes = [];
    
    /** @var Pruefkatalog|null */
    public $selectedFehlercode = null;
    
    /** @var bool */
    public $showModal = false;
    
    /** @var string|null */
    public $eisenmarke = null;
    
    /** @var array */
    public $analysewerte = [];
    
    /** @var bool */
    public $autoAssigned = false;
    
    /** @var string|null */
    public $autoAssignmentError = null;
    
    /** @var string */
    public $search = '';

    protected $listeners = [
        'eisenmarkeUpdated' => 'setEisenmarke',
        'analysewerteUpdated' => 'setAnalysewerte',
        'tryAutoAssignment' => 'tryAutoAssignFehlercode'
    ];

    public function mount(string $initialFehlercode = null)
    {
        $this->fehlercode = $initialFehlercode;
        $this->loadFehlercodes();
        
        if ($this->fehlercode) {
            $this->selectedFehlercode = Pruefkatalog::where('fehlercode', $this->fehlercode)->first();
        }
    }

    public function render()
    {
        $filteredFehlercodes = $this->availableFehlercodes;
        
        if (!empty($this->search)) {
            $search = strtolower($this->search);
            $filteredFehlercodes = array_filter($this->availableFehlercodes, function($fehlercode) use ($search) {
                return strpos(strtolower($fehlercode['fehlercode']), $search) !== false ||
                       (isset($fehlercode['eisenmarke']) && strpos(strtolower($fehlercode['eisenmarke']), $search) !== false) ||
                       (isset($fehlercode['element']) && strpos(strtolower($fehlercode['element']), $search) !== false) ||
                       (isset($fehlercode['massnahmen']) && strpos(strtolower($fehlercode['massnahmen']), $search) !== false);
            });
        }
        
        return view('livewire.spektrometer-fehlercode', [
            'availableFehlercodes' => $filteredFehlercodes
        ]);
    }

    public function loadFehlercodes()
    {
        $this->availableFehlercodes = Pruefkatalog::orderBy('fehlercode')->get()->toArray();
    }

    public function openModal()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function selectFehlercode($fehlercodeId)
    {
        $fehlercode = Pruefkatalog::find($fehlercodeId);
        if ($fehlercode) {
            $this->fehlercode = $fehlercode->fehlercode;
            $this->selectedFehlercode = $fehlercode;
            $this->autoAssigned = false;
            $this->emit('fehlercodeSelected', $this->fehlercode);
        }
        $this->closeModal();
    }

    public function setEisenmarke($eisenmarke)
    {
        $this->eisenmarke = $eisenmarke;
        $this->tryAutoAssignFehlercode();
    }

    public function setAnalysewerte($analysewerte)
    {
        $this->analysewerte = $analysewerte;
        $this->tryAutoAssignFehlercode();
    }

    public function tryAutoAssignFehlercode()
    {
        $this->autoAssignmentError = null;
        
        if (empty($this->eisenmarke) || empty($this->analysewerte)) {
            return;
        }

        try {
            // Get recent rotekarten with spektrometer data for this iron grade
            $recentRotekarten = Rotekarte::whereNotNull('spektrometer_daten')
                ->orderBy('spektrometer_daten->created_at', 'desc')
                ->get();

            // Filter rotekarten that match the current iron grade
            $matchingRotekarten = $recentRotekarten->filter(function ($rotekarte) {
                $spektrometerDaten = $rotekarte->spektrometer_daten;
                return isset($spektrometerDaten['eisenmarke']) && 
                       $spektrometerDaten['eisenmarke'] === $this->eisenmarke;
            });

            if ($matchingRotekarten->isEmpty()) {
                return;
            }

            // Find tolerance values for the same element
            foreach ($this->analysewerte as $analyse) {
                if (!isset($analyse['element']) || !isset($analyse['istWert'])) {
                    continue;
                }

                $element = $analyse['element'];
                $istWert = (float) str_replace(',', '.', $analyse['istWert']);

                // Find pruefkatalogs matching the element and eisenmarke
                $matchingPruefkatalogs = Pruefkatalog::where('eisenmarke', $this->eisenmarke)
                    ->where('element', $element)
                    ->where('absolutwerte', '!=', null)
                    ->get();

                foreach ($matchingPruefkatalogs as $pruefkatalog) {
                    $absolutwerte = $pruefkatalog->absolutwerte;
                    
                    // Check if the absolutwerte is a range (min-max)
                    if (strpos($absolutwerte, '-') !== false) {
                        list($min, $max) = array_map('trim', explode('-', $absolutwerte));
                        $min = (float) str_replace(',', '.', $min);
                        $max = (float) str_replace(',', '.', $max);
                        
                        if ($istWert >= $min && $istWert <= $max) {
                            $this->fehlercode = $pruefkatalog->fehlercode;
                            $this->selectedFehlercode = $pruefkatalog;
                            $this->autoAssigned = true;
                            $this->emit('fehlercodeSelected', $this->fehlercode);
                            return;
                        }
                    } 
                    // Simple comparison with a single value
                    else {
                        $threshold = (float) str_replace(',', '.', $absolutwerte);
                        
                        // If the value is higher than the threshold, it's a match
                        if ($istWert > $threshold) {
                            $this->fehlercode = $pruefkatalog->fehlercode;
                            $this->selectedFehlercode = $pruefkatalog;
                            $this->autoAssigned = true;
                            $this->emit('fehlercodeSelected', $this->fehlercode);
                            return;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $this->autoAssignmentError = 'Fehler bei der automatischen Zuordnung: ' . $e->getMessage();
        }
    }

    public function clearFehlercode()
    {
        $this->fehlercode = null;
        $this->selectedFehlercode = null;
        $this->autoAssigned = false;
        $this->emit('fehlercodeSelected', null);
    }
} 