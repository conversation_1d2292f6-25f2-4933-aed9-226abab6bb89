<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Pruefkatalog;
use App\Models\Rotekarte;
use Illuminate\Support\Facades\Log;

class FehlercodeSuggestService
{
    private $nichtAnalysierbarMappings = [
        '401' => 'CA-40100',
        '402' => 'CA-40200',
        '403' => 'CA-40300',
        '405' => 'CA-40600',
        '408' => 'CA-40600',
        '411' => 'CA-41100',
        '412' => 'CA-41100',
        '413' => 'CA-41100',
        '420' => 'CA-41100',
        '430' => 'CA-43000',
        '501' => 'CA-50100',
        '511' => 'CA-51100',
        '512' => 'CA-51100',
        '555' => 'CA-55500',
        '570' => 'CA-55500',
        '611' => 'CA-61100',
        '690' => 'CA-61100',
        '666' => 'CA-66600',
        '680' => 'CA-68001',
        '670' => 'CA-68001',
        '701' => 'CA-70100',
        '703' => 'CA-70300',
        '707' => 'CA-70700',
        '777' => 'CA-70700',
        '778' => 'CA-70700',
        '711' => 'CA-71100',
        '790' => 'CA-71100',
        '801' => 'CA-80100',
        '803' => 'CA-80100',
        '601' => 'CA-80300',
        '602' => 'CA-80300',
        '603' => 'CA-80300',
        '604' => 'CA-80300',
        '605' => 'CA-80300',
        '811' => 'CA-81100',
        '890' => 'CA-81100',
        '825' => 'CA-82500'
    ];

    /**
     * Suggest a fehlercode based on eisenmarke and analysewerte
     * Unterstützt sowohl einzelne Eisenmarken als auch durch Kommas getrennte Listen in der Datenbank
     * 
     * @param string $eisenmarke
     * @param array $analysewerte
     * @return array
     */
    public function suggestFehlercode(string $eisenmarke, array $analysewerte): array
    {
        Log::info('FehlercodeSuggestService::suggestFehlercode aufgerufen', [
            'eisenmarke' => $eisenmarke,
            'analysewerte' => $analysewerte
        ]);
        
        if (empty($eisenmarke) || empty($analysewerte)) {
            Log::warning('Eisenmarke oder Analysewerte leer', [
                'eisenmarke' => $eisenmarke,
                'analysewerte' => $analysewerte
            ]);
            
            return [
                'success' => false,
                'message' => 'Eisenmarke und Analysewerte werden benötigt',
                'fehlercode' => null,
                'details' => null
            ];
        }

        // Überprüfe zuerst auf "Nicht analysierbar"
        if (count($analysewerte) === 1 && 
            isset($analysewerte[0]['element']) && 
            $analysewerte[0]['element'] === 'Nicht analysierbar') {
            
            return $this->handleNichtAnalysierbar($eisenmarke);
        }

        try {
            // Universeller Algorithmus für alle Eisenmarken und Elemente
            foreach ($analysewerte as $analyse) {
                if (!isset($analyse['element']) || !isset($analyse['istWert']) || !isset($analyse['sollWert'])) {
                    continue;
                }

                $element = $analyse['element'];
                $istWert = (float) str_replace(',', '.', (string) $analyse['istWert']);
                $sollWert = $analyse['sollWert'];
                
                Log::info('Verarbeite Element ' . $element . ' für Eisenmarke ' . $eisenmarke, [
                    'istWert' => $istWert,
                    'sollWert' => $sollWert
                ]);
                
                // Direkte Zuordnung für Liq und UK_r Elemente - diese sind wirklich spezielle Fälle
                $specialElementResult = $this->handleSpecialElements($element, $istWert, $sollWert);
                if ($specialElementResult !== null) {
                    Log::info('Spezialfall-Zuordnung für Element ' . $element, [
                        'istWert' => $istWert,
                        'sollWert' => $sollWert,
                        'fehlercode' => $specialElementResult['fehlercode']
                    ]);
                    return $specialElementResult;
                }
                
                // Wenn der Wert nicht numerisch ist, überspringen
                if (!is_numeric($istWert)) {
                    Log::warning('Ungültiger Analysewert, überspringe', [
                        'element' => $element,
                        'istWert' => $analyse['istWert']
                    ]);
                    continue;
                }
                
                // 1. Schritt: Hole alle passenden Pruefkatalog-Einträge für diese Eisenmarke und dieses Element
                $pruefkatalogEintraege = $this->findMatchingPruefkatalogs($element, $eisenmarke);
                
                if ($pruefkatalogEintraege->isEmpty()) {
                    Log::info('Keine Pruefkatalog-Einträge für Eisenmarke ' . $eisenmarke . ' und Element ' . $element . ' gefunden');
                    continue;
                }
                
                Log::info('Pruefkatalog-Einträge gefunden', [
                    'count' => $pruefkatalogEintraege->count(),
                    'fehlercodes' => $pruefkatalogEintraege->pluck('fehlercode')->toArray(),
                    'eisenmarken' => $pruefkatalogEintraege->pluck('eisenmarke')->toArray(),
                    'elemente' => $pruefkatalogEintraege->pluck('element')->toArray()
                ]);
                
                // 2. Schritt: Berechne Abweichung vom Sollwert
                $abweichungInfo = $this->berechneAbweichung($istWert, $sollWert);
                
                if (!$abweichungInfo['hatAbweichung']) {
                    Log::info('Keine Abweichung vom Sollwert-Bereich festgestellt', [
                        'istWert' => $istWert,
                        'sollWert' => $sollWert
                    ]);
                    continue;
                }

                Log::info('Abweichung berechnet', $abweichungInfo);
                
                // 3. Schritt: Wähle passenden Fehlercode basierend auf Abweichungstyp
                $fehlercode = null;
                $pruefkatalog = null;
                
                // Liste aller gefundenen Fehlercodes
                $kandidaten = $pruefkatalogEintraege;
                
                // Protokolliere alle Kandidaten
                Log::info('Alle Kandidaten für ' . $element, [
                    'count' => $kandidaten->count(),
                    'fehlercodes' => $kandidaten->pluck('fehlercode')->toArray(),
                    'absolutwerte' => $kandidaten->pluck('absolutwerte')->toArray()
                ]);
                
                // Filtere nach Abweichungstyp, falls möglich
                if ($abweichungInfo['istOGWUeberschreitung']) {
                    // Universelle Suche nach OGW/Überschreitungs-Fehlercodes
                    $typKandidaten = $pruefkatalogEintraege->filter(function($item) {
                        $absolutwerte = strtolower($item->absolutwerte ?? '');
                        return strpos($absolutwerte, 'ogw') !== false || 
                               strpos($absolutwerte, '>') !== false ||
                               strpos($absolutwerte, 'überschreitung') !== false ||
                               strpos($absolutwerte, 'ueberschreitung') !== false;
                    });
                    
                    if ($typKandidaten->isNotEmpty()) {
                        $kandidaten = $typKandidaten;
                    }
                    
                    Log::info('OGW/Überschreitungs-Kandidaten', [
                        'count' => $kandidaten->count(),
                        'fehlercodes' => $kandidaten->pluck('fehlercode')->toArray()
                    ]);
                } 
                else if ($abweichungInfo['istUGWUnterschreitung']) {
                    // Universelle Suche nach UGW/Unterschreitungs-Fehlercodes
                    $typKandidaten = $pruefkatalogEintraege->filter(function($item) {
                        $absolutwerte = strtolower($item->absolutwerte ?? '');
                        return strpos($absolutwerte, 'ugw') !== false || 
                               strpos($absolutwerte, '<') !== false ||
                               strpos($absolutwerte, 'unter') !== false ||
                               strpos($absolutwerte, 'unterschreitung') !== false;
                    });
                    
                    if ($typKandidaten->isNotEmpty()) {
                        $kandidaten = $typKandidaten;
                    }
                    
                    Log::info('UGW/Unterschreitungs-Kandidaten', [
                        'count' => $kandidaten->count(),
                        'fehlercodes' => $kandidaten->pluck('fehlercode')->toArray()
                    ]);
                }
                
                // Verbesserte Fehlercode-Auswahl basierend auf der Position im Soll-Wert-Bereich
                if ($abweichungInfo['hatAbweichung']) {
                    Log::info('Verbesserte Fehlercode-Auswahl basierend auf der Position:', [
                        'Element' => $element,
                        'istOGWUeberschreitung' => $abweichungInfo['istOGWUeberschreitung'],
                        'istUGWUnterschreitung' => $abweichungInfo['istUGWUnterschreitung'],
                        'Grenzwert' => $abweichungInfo['grenzwert'],
                        'Abweichung' => $abweichungInfo['abweichung']
                    ]);
                    
                    // Priorität 1: Filterung nach Schwellenwerten, wenn Abweichung groß genug ist
                    if ($abweichungInfo['abweichung'] >= 0.02) {
                        $schwellenwertKandidaten = $kandidaten->filter(function($item) {
                            $absolutwerte = $item->absolutwerte ?? '';
                            
                            // Suche nach allen möglichen Schwellenwertformaten im Text
                            return strpos($absolutwerte, '≥0,02') !== false || 
                                   strpos($absolutwerte, '≥ 0,02') !== false ||
                                   strpos($absolutwerte, '≥0.02') !== false ||
                                   strpos($absolutwerte, '≥ 0.02') !== false ||
                                   strpos($absolutwerte, '>0,02') !== false ||
                                   strpos($absolutwerte, '> 0,02') !== false ||
                                   strpos($absolutwerte, '>0.02') !== false ||
                                   strpos($absolutwerte, '> 0.02') !== false ||
                                   strpos($absolutwerte, '+ ≥ 0,02') !== false ||
                                   strpos($absolutwerte, '+≥0,02') !== false ||
                                   strpos($absolutwerte, '- ≥ 0,02') !== false ||
                                   strpos($absolutwerte, '-≥0,02') !== false ||
                                   strpos($absolutwerte, '+ >= 0,02') !== false ||
                                   strpos($absolutwerte, '- >= 0,02') !== false;
                        });
                        
                        if ($schwellenwertKandidaten->isNotEmpty()) {
                            Log::info('Gefundene Schwellenwert-Kandidaten (≥0,02)', [
                                'count' => $schwellenwertKandidaten->count(),
                                'fehlercodes' => $schwellenwertKandidaten->pluck('fehlercode')->toArray(),
                                'absolutwerte' => $schwellenwertKandidaten->pluck('absolutwerte')->toArray()
                            ]);
                            $kandidaten = $schwellenwertKandidaten;
                        }
                    }
                    
                    // Priorität 2: Spezifischere Filterung nach Abweichungstyp
                    if ($kandidaten->count() > 1) {
                        if ($abweichungInfo['istOGWUeberschreitung']) {
                            // Hier filtern wir nach OGW/MAX und suchen explizit nach Ausdrücken wie "> x.xx"
                            $ueberschreitungKandidaten = $kandidaten->filter(function($item) {
                                $absolutwerte = strtolower($item->absolutwerte ?? '');
                                // Sehr spezifischer Filter für Überschreitungen
                                return preg_match('/>\s*[0-9,.]+/', $absolutwerte) ||
                                       preg_match('/>\s*[0-9,.]+\s*%/', $absolutwerte) ||
                                       (strpos($absolutwerte, '>') !== false && strpos($absolutwerte, '<') === false) ||
                                       ((strpos($absolutwerte, 'ogw') !== false || strpos($absolutwerte, 'max') !== false) && 
                                         strpos($absolutwerte, 'ugw') === false && strpos($absolutwerte, 'min') === false);
                            });
                            
                            if ($ueberschreitungKandidaten->isNotEmpty()) {
                                Log::info('Spezifische OGW-Überschreitungskandidaten gefunden', [
                                    'count' => $ueberschreitungKandidaten->count(),
                                    'fehlercodes' => $ueberschreitungKandidaten->pluck('fehlercode')->toArray()
                                ]);
                                $kandidaten = $ueberschreitungKandidaten;
                            }
                        } else if ($abweichungInfo['istUGWUnterschreitung']) {
                            // Hier filtern wir nach UGW/MIN und suchen explizit nach Ausdrücken wie "< x.xx" oder "Unter x.xx"
                            $unterschreitungKandidaten = $kandidaten->filter(function($item) {
                                $absolutwerte = strtolower($item->absolutwerte ?? '');
                                // Sehr spezifischer Filter für Unterschreitungen
                                return preg_match('/<\s*[0-9,.]+/', $absolutwerte) ||
                                       preg_match('/<\s*[0-9,.]+\s*%/', $absolutwerte) ||
                                       preg_match('/unter\s*[0-9,.]+/', $absolutwerte) ||
                                       preg_match('/unter\s*[0-9,.]+\s*%/', $absolutwerte) ||
                                       (strpos($absolutwerte, '<') !== false && strpos($absolutwerte, '>') === false) ||
                                       ((strpos($absolutwerte, 'ugw') !== false || strpos($absolutwerte, 'min') !== false) && 
                                         strpos($absolutwerte, 'ogw') === false && strpos($absolutwerte, 'max') === false);
                            });
                            
                            if ($unterschreitungKandidaten->isNotEmpty()) {
                                Log::info('Spezifische UGW-Unterschreitungskandidaten gefunden', [
                                    'count' => $unterschreitungKandidaten->count(),
                                    'fehlercodes' => $unterschreitungKandidaten->pluck('fehlercode')->toArray()
                                ]);
                                $kandidaten = $unterschreitungKandidaten;
                            }
                        }
                    }
                    
                    // Priorisiere Fehlercodes mit spezifischen Formaten wie "OGW + ≥ 0,02%" oder "UGW - ≥ 0,02%"
                    if ($kandidaten->count() > 1) {
                        $spezifischeFormatKandidaten = $kandidaten->filter(function($item) use ($abweichungInfo) {
                            $absolutwerte = strtolower($item->absolutwerte ?? '');
                            
                            // Für OGW-Überschreitungen
                            if ($abweichungInfo['istOGWUeberschreitung']) {
                                return (strpos($absolutwerte, 'ogw +') !== false || 
                                       strpos($absolutwerte, 'ogw+') !== false);
                            } 
                            // Für UGW-Unterschreitungen
                            else if ($abweichungInfo['istUGWUnterschreitung']) {
                                return (strpos($absolutwerte, 'ugw -') !== false || 
                                       strpos($absolutwerte, 'ugw-') !== false);
                            }
                            
                            return false;
                        });
                        
                        if ($spezifischeFormatKandidaten->isNotEmpty()) {
                            Log::info('Fehlercodes mit spezifischen Formaten gefunden', [
                                'count' => $spezifischeFormatKandidaten->count(),
                                'fehlercodes' => $spezifischeFormatKandidaten->pluck('fehlercode')->toArray()
                            ]);
                            $kandidaten = $spezifischeFormatKandidaten;
                        }
                    }
                }
                
                // Verwende den ersten passenden Fehlercode
                if ($kandidaten->isNotEmpty()) {
                    $pruefkatalog = $kandidaten->first();
                    $fehlercode = $pruefkatalog->fehlercode;
                    
                    Log::info('Passenden Fehlercode gefunden', [
                        'element' => $element,
                        'fehlercode' => $fehlercode,
                        'abweichungsTyp' => $abweichungInfo['istOGWUeberschreitung'] ? 'OGW' : 'UGW',
                        'abweichung' => $abweichungInfo['abweichung']
                    ]);
                    
                    // 4. Schritt: Rückgabe des gefundenen Fehlercodes
                    $abweichungsText = $abweichungInfo['istOGWUeberschreitung'] 
                        ? "überschreitet Obergrenzwert " . $abweichungInfo['grenzwert'] . " um " . round($abweichungInfo['abweichung'], 3)
                        : "unterschreitet Untergrenzwert " . $abweichungInfo['grenzwert'] . " um " . round($abweichungInfo['abweichung'], 3);
                    
                    return [
                        'success' => true,
                        'message' => "Fehlercode automatisch zugeordnet: $element-Wert $istWert $abweichungsText",
                        'fehlercode' => $fehlercode,
                        'details' => $pruefkatalog
                    ];
                }
            }
            
            // Sammle alle Analyseelemente für die erweiterte Suche
            $analyseElemente = collect($analysewerte)->pluck('element')->filter()->unique()->toArray();
            
            // Stelle sicher, dass am Ende immer ein Array zurückgegeben wird
            Log::warning('Kein passender Fehlercode gefunden für Eisenmarke und Analysewerte', [
                'eisenmarke' => $eisenmarke,
                'elemente' => $analyseElemente
            ]);
            
            return [
                'success' => false,
                'message' => 'Kein passender Fehlercode gefunden',
                'fehlercode' => null,
                'details' => null
            ];
        } catch (\Exception $e) {
            Log::error('Fehler bei der Fehlercode-Zuordnung', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => 'Fehler bei der automatischen Zuordnung: ' . $e->getMessage(),
                'fehlercode' => null,
                'details' => null
            ];
        }
    }

    /**
     * Berechnet die Abweichung eines Ist-Werts vom Soll-Wert Bereich
     * 
     * @param float $istWert Der aktuelle Wert
     * @param string $sollWert Der Soll-Wert (kann ein Bereich sein)
     * @return array Informationen zur Abweichung
     */
    private function berechneAbweichung(float $istWert, string $sollWert): array
    {
        $result = [
            'hatAbweichung' => false,
            'istOGWUeberschreitung' => false,
            'istUGWUnterschreitung' => false,
            'abweichung' => 0,
            'grenzwert' => null
        ];
        
        // Logge die Eingangswerte für die Fehlersuche
        Log::info('Berechne Abweichung für Werte', [
            'istWert' => $istWert,
            'sollWert' => $sollWert
        ]);
        
        // Prüfe, ob der Sollwert ein Bereich ist (z.B. "0.23 - 0.28" oder "1140 - 1160")
        if (strpos($sollWert, '-') !== false) {
            $sollWertTeile = array_map('trim', explode('-', $sollWert));
            
            if (count($sollWertTeile) === 2) {
                $min = (float) str_replace(',', '.', $sollWertTeile[0]);
                $max = (float) str_replace(',', '.', $sollWertTeile[1]);
                
                Log::info('Bereichsvergleich', [
                    'min' => $min,
                    'max' => $max,
                    'istWert' => $istWert
                ]);
                
                // Prüfe, ob der Wert über dem Maximalwert liegt
                if ($istWert > $max) {
                    $result['hatAbweichung'] = true;
                    $result['istOGWUeberschreitung'] = true;
                    $result['abweichung'] = $istWert - $max;
                    $result['grenzwert'] = $max;
                }
                // Prüfe, ob der Wert unter dem Minimalwert liegt
                else if ($istWert < $min) {
                    $result['hatAbweichung'] = true;
                    $result['istUGWUnterschreitung'] = true;
                    $result['abweichung'] = $min - $istWert;
                    $result['grenzwert'] = $min;
                }
            }
        }
        // Prüfe auf einzelnen Grenzwert mit Operator (z.B. ">= 1.0")
        else if (preg_match('/([<>]=?|≤|≥)\s*([\d.,]+)/', $sollWert, $matches)) {
            $operator = $matches[1];
            $grenzwert = (float) str_replace(',', '.', $matches[2]);
            
            switch ($operator) {
                case '>':
                case '>=':
                case '≥':
                    if ($istWert < $grenzwert) {
                        $result['hatAbweichung'] = true;
                        $result['istUGWUnterschreitung'] = true;
                        $result['abweichung'] = $grenzwert - $istWert;
                        $result['grenzwert'] = $grenzwert;
                    }
                    break;
                case '<':
                case '<=':
                case '≤':
                    if ($istWert > $grenzwert) {
                        $result['hatAbweichung'] = true;
                        $result['istOGWUeberschreitung'] = true;
                        $result['abweichung'] = $istWert - $grenzwert;
                        $result['grenzwert'] = $grenzwert;
                    }
                    break;
            }
        }
        // Einfacher Grenzwert ohne Operator
        else if (is_numeric(str_replace(',', '.', $sollWert))) {
            $grenzwert = (float) str_replace(',', '.', $sollWert);
            $abweichung = abs($istWert - $grenzwert);
            
            if ($abweichung > 0) {
                $result['hatAbweichung'] = true;
                if ($istWert > $grenzwert) {
                    $result['istOGWUeberschreitung'] = true;
                    $result['abweichung'] = $istWert - $grenzwert;
                } else {
                    $result['istUGWUnterschreitung'] = true;
                    $result['abweichung'] = $grenzwert - $istWert;
                }
                $result['grenzwert'] = $grenzwert;
            }
        }
        
        return $result;
    }

    /**
     * Findet passende Prüfkatalog-Einträge für ein Element und eine Eisenmarke,
     * unter Berücksichtigung von komma-getrennten Eisenmarken-Listen.
     * 
     * @param string $element Das Element, nach dem gesucht wird
     * @param string $eisenmarke Die Eisenmarke, nach der gesucht wird
     * @return \Illuminate\Database\Eloquent\Collection Die gefundenen Prüfkatalog-Einträge
     */
    private function findMatchingPruefkatalogs(string $element, string $eisenmarke)
    {
        Log::info('Suche nach Pruefkatalog-Einträgen', [
            'element' => $element,
            'eisenmarke' => $eisenmarke
        ]);
        
        // Schritt 0: Spezielle Elemente (Liq_r, UK_r) - ignoriere die Eisenmarke komplett für diese
        $spezielleElemente = ['Liq_r', 'UK_r', 'Liq', 'UK'];
        if (in_array($element, $spezielleElemente)) {
            Log::info('Spezielles Element erkannt, das für alle Eisenmarken gilt', [
                'element' => $element
            ]);
            
            // Suche direkt nach Einträgen für dieses Element ohne Berücksichtigung der Eisenmarke
            $universalElementCodes = Pruefkatalog::where('element', $element)
                ->where(function($query) {
                    $query->where('eisenmarke', 'siehe Bem.')
                          ->orWhere('eisenmarke', 'LIKE', 'siehe%')
                          ->orWhere('eisenmarke', 'alle')
                          ->orWhere('eisenmarke', 'LIKE', '%all%')
                          ->orWhere('eisenmarke', '');
                })
                ->get();
                
            if ($universalElementCodes->isNotEmpty()) {
                Log::info('Universelle Einträge für Element gefunden', [
                    'element' => $element,
                    'count' => $universalElementCodes->count(),
                    'fehlercodes' => $universalElementCodes->pluck('fehlercode')->toArray()
                ]);
                return $universalElementCodes;
            }
            
            // Als Fallback: Hole einfach alle Einträge für dieses Element
            $allElementCodes = Pruefkatalog::where('element', $element)->get();
            if ($allElementCodes->isNotEmpty()) {
                Log::info('Alle Einträge für Element zurückgegeben', [
                    'element' => $element,
                    'count' => $allElementCodes->count()
                ]);
                return $allElementCodes;
            }
        }
        
        // Methode 1: Exakte Übereinstimmung mit Eisenmarke
        $pruefkatalogs = Pruefkatalog::where('element', $element)
            ->where('eisenmarke', $eisenmarke)
            ->get();
            
        // Methode 2: Verbesserte Suche für Eisenmarken in Komma-getrennten Listen
        if ($pruefkatalogs->isEmpty()) {
            // Verwende LIKE-Abfragen für alle möglichen Positionen der Eisenmarke in der Liste
            $pruefkatalogs = Pruefkatalog::where('element', $element)
                ->where(function($query) use ($eisenmarke) {
                    // Prüfe verschiedene Muster, wie die Eisenmarke in einer Liste erscheinen könnte
                    $query->where('eisenmarke', 'LIKE', $eisenmarke . ',%')    // Am Anfang der Liste
                          ->orWhere('eisenmarke', 'LIKE', '%, ' . $eisenmarke . ',%')  // In der Mitte der Liste
                          ->orWhere('eisenmarke', 'LIKE', '%, ' . $eisenmarke)  // Am Ende der Liste
                          ->orWhere('eisenmarke', 'LIKE', '%/' . $eisenmarke . '%')  // Im Format "XXX / YYY"
                          ->orWhere('eisenmarke', 'LIKE', '%' . $eisenmarke . '/%');  // Im Format "XXX/YYY"
                })
                ->get();
                
            Log::info('Prüfkatalog-Einträge über erweiterte LIKE-Suche gefunden', [
                'count' => $pruefkatalogs->count(),
                'pattern' => '[' . $eisenmarke . ' am Anfang, in der Mitte oder am Ende einer Liste]'
            ]);
            
            // Wenn immer noch leer, erweitere die Suche mit noch einer Methode
            if ($pruefkatalogs->isEmpty()) {
                $pruefkatalogs = Pruefkatalog::where('element', $element)
                    ->where('eisenmarke', 'LIKE', '%' . $eisenmarke . '%')
                    ->get();
                    
                Log::info('Prüfkatalog-Einträge über einfache LIKE-Suche gefunden', [
                    'count' => $pruefkatalogs->count()
                ]);
            }
        }
        
        // Methode 3: Manuelle Überprüfung auf Eisenmarke in Komma-getrennte Liste
        // Falls die LIKE-Abfrage nicht alle Fälle abdeckt
        if ($pruefkatalogs->isEmpty()) {
            $allCandidates = Pruefkatalog::where('element', $element)->get();
            $matchingPruefkatalogs = collect();
            
            foreach ($allCandidates as $candidate) {
                // Extrahiere alle Eisenmarken aus dem String (unterstützt verschiedene Trennzeichen)
                $eisenmarken = preg_split('/[,\/\s]+/', $candidate->eisenmarke);
                $eisenmarken = array_map('trim', $eisenmarken);
                
                if (in_array($eisenmarke, $eisenmarken)) {
                    $matchingPruefkatalogs->push($candidate);
                }
                
                // Prüfe auf zusammengehörige Nummernbereiche wie "411 - 420"
                foreach ($eisenmarken as $marke) {
                    if (strpos($marke, '-') !== false) {
                        list($start, $end) = array_map('trim', explode('-', $marke));
                        if (is_numeric($start) && is_numeric($end) && is_numeric($eisenmarke)) {
                            if ((int)$eisenmarke >= (int)$start && (int)$eisenmarke <= (int)$end) {
                                $matchingPruefkatalogs->push($candidate);
                                break;
                            }
                        }
                    }
                }
            }
            
            if ($matchingPruefkatalogs->isNotEmpty()) {
                Log::info('Prüfkatalog-Einträge über manuelle Eisenmarke-Prüfung gefunden', [
                    'count' => $matchingPruefkatalogs->count()
                ]);
                $pruefkatalogs = $matchingPruefkatalogs;
            }
        }
        
        // Methode 4: Suche nach universellen Fehlercodes, die für alle Eisenmarken gelten
        // Erkennbar an "siehe Bem." oder ähnlichen allgemeinen Hinweisen in der Eisenmarken-Spalte
        if ($pruefkatalogs->isEmpty()) {
            $universalCodes = Pruefkatalog::where('element', $element)
                ->where(function($query) {
                    $query->where('eisenmarke', 'siehe Bem.')
                          ->orWhere('eisenmarke', 'LIKE', 'siehe%')
                          ->orWhere('eisenmarke', 'alle')
                          ->orWhere('eisenmarke', 'LIKE', '%all%')
                          ->orWhere('eisenmarke', '');
                })
                ->get();
                
            if ($universalCodes->isNotEmpty()) {
                Log::info('Universelle Prüfkatalog-Einträge gefunden, die für alle Eisenmarken gelten', [
                    'count' => $universalCodes->count(),
                    'fehlercodes' => $universalCodes->pluck('fehlercode')->toArray()
                ]);
                
                // Füge universelle Codes zu bestehenden Ergebnissen hinzu oder verwende sie als neue Ergebnisse
                if ($pruefkatalogs->isEmpty()) {
                    $pruefkatalogs = $universalCodes;
                } else {
                    // Merge collections without duplicates
                    $universalCodes->each(function($code) use ($pruefkatalogs) {
                        if (!$pruefkatalogs->contains('fehlercode', $code->fehlercode)) {
                            $pruefkatalogs->push($code);
                        }
                    });
                }
            }
        }
        
        // Protokolliere die gefundenen Einträge
        Log::info('Gefundene Prüfkatalog-Einträge', [
            'total' => $pruefkatalogs->count(),
            'fehlercodes' => $pruefkatalogs->pluck('fehlercode')->toArray()
        ]);
        
        return $pruefkatalogs;
    }

    /**
     * Verarbeitet den "Nicht analysierbar" Fall
     * 
     * @param string $eisenmarke
     * @return array
     */
    private function handleNichtAnalysierbar(string $eisenmarke): array
    {
        // Versuche zuerst Prüfkatalog nach Eisenmarke und "Nicht analysierbar" zu filtern
        $pruefkatalogEntries = \App\Models\Pruefkatalog::where('eisenmarke', $eisenmarke)
            ->where(function($query) {
                $query->where('element', 'Nicht analysierbar')
                    ->orWhere('bemerkungen', 'like', '%nicht analysierbar%');
            })
            ->get();

        if ($pruefkatalogEntries->isNotEmpty()) {
            $pruefkatalog = $pruefkatalogEntries->first();
            
            Log::info('Fehlercode für nicht analysierbare Probe aus Prüfkatalog gefunden', [
                'eisenmarke' => $eisenmarke,
                'fehlercode' => $pruefkatalog->fehlercode
            ]);
            
            return [
                'success' => true,
                'message' => 'Fehlercode für nicht analysierbare Probe automatisch zugeordnet.',
                'fehlercode' => $pruefkatalog->fehlercode,
                'details' => $pruefkatalog
            ];
        }
        
        // Fallback auf die statischen Mappings, wenn im Prüfkatalog nichts gefunden wurde
        if (isset($this->nichtAnalysierbarMappings[$eisenmarke])) {
            $fehlercode = $this->nichtAnalysierbarMappings[$eisenmarke];
            $pruefkatalog = \App\Models\Pruefkatalog::where('fehlercode', $fehlercode)->first();
            
            if ($pruefkatalog) {
                Log::info('Fehlercode für nicht analysierbare Probe aus Mapping gefunden', [
                    'eisenmarke' => $eisenmarke,
                    'fehlercode' => $fehlercode
                ]);
                
                return [
                    'success' => true,
                    'message' => 'Fehlercode für nicht analysierbare Probe automatisch zugeordnet.',
                    'fehlercode' => $fehlercode,
                    'details' => $pruefkatalog
                ];
            }
        }
        
        // Keine Zuordnung gefunden
        Log::warning('Kein Fehlercode für nicht analysierbare Probe gefunden', [
            'eisenmarke' => $eisenmarke
        ]);
        
        return [
            'success' => false,
            'message' => 'Kein passender Fehlercode für nicht analysierbare Probe gefunden.',
            'fehlercode' => null,
            'details' => null
        ];
    }

    /**
     * Behandelt spezielle Elemente wie Liq und UK_r direkt mit hardcodierten Regeln,
     * wenn keine passenden Einträge im Prüfkatalog gefunden wurden.
     * 
     * @param string $element Das Element
     * @param float $istWert Der aktuelle IST-Wert
     * @param string $sollWert Der SOLL-Wert Bereich
     * @return array|null Fehlercode-Rückgabe oder null, wenn keine direkte Zuordnung möglich ist
     */
    private function handleSpecialElements(string $element, float $istWert, string $sollWert): ?array
    {
        $spezielleElemente = ['Liq_r', 'Liq', 'UK_r', 'UK'];
        
        if (!in_array($element, $spezielleElemente)) {
            return null;
        }
        
        Log::info('Verarbeite spezielles Element direkt', [
            'element' => $element,
                            'istWert' => $istWert,
            'sollWert' => $sollWert
        ]);
        
        // Für Liquidustemperatur (Liq, Liq_r)
        if ($element === 'Liq' || $element === 'Liq_r') {
            // Extrahiere min und max aus dem Sollwert-Bereich
            if (strpos($sollWert, '-') !== false) {
                $sollWertTeile = array_map('trim', explode('-', $sollWert));
                if (count($sollWertTeile) === 2) {
                    $min = (float) str_replace(',', '.', $sollWertTeile[0]);
                    $max = (float) str_replace(',', '.', $sollWertTeile[1]);
                    
                    // Prüfe auf Überschreitung des Maximalwerts (> 1160°C)
                    if ($istWert > $max) {
                        // Direkter Fallback ohne Datenbankabfrage, um SQL-Fehler zu vermeiden
                        Log::info('Direkter TA-11001 Fallback für Liq > ' . $max);
                        $message = "Fehlercode für Liquidustemperatur > " . $max . "°C automatisch zugeordnet";
                        return [
                            'success' => true,
                            'message' => $message,
                            'fehlercode' => 'TA-11001',
                            'details' => null
                        ];
                    }
                    // Prüfe auf Unterschreitung des Minimalwerts
                    else if ($istWert < $min) {
                        // Unterscheide zwischen starker Unterschreitung (- >= 3°C)
                        $abweichung = $min - $istWert;
                        
                        if ($abweichung >= 3) {
                            // Direkter Fallback für starke Unterschreitung
                            Log::info('Direkter TA-11003 Fallback für Liq stark unter Min (' . $abweichung . '°C)');
                            $message = "Fehlercode für Liquidustemperatur stark unter Minimalwert (" . $abweichung . "°C) automatisch zugeordnet";
                            return [
                                'success' => true,
                                'message' => $message,
                                'fehlercode' => 'TA-11003',
                                'details' => null
                            ];
                        } else {
                            // Direkter Fallback für leichte Unterschreitung
                            Log::info('Direkter TA-11002 Fallback für Liq leicht unter Min (' . $abweichung . '°C)');
                            $message = "Fehlercode für Liquidustemperatur leicht unter Minimalwert (" . $abweichung . "°C) automatisch zugeordnet";
                            return [
                                'success' => true,
                                'message' => $message,
                                'fehlercode' => 'TA-11002',
                                'details' => null
                            ];
                        }
                    }
                }
            }
        }
        
        // Für Unterkühlungstemperatur (UK, UK_r)
        if ($element === 'UK' || $element === 'UK_r') {
            // Unterkühlungstemperatur <= 5°C
            if ($istWert <= 5) {
                // Direkter Fallback für niedrige Unterkühlungstemperatur
                Log::info('Direkter TA-11004 Fallback für UK <= 5°C');
                return [
                    'success' => true,
                    'message' => "Fehlercode für Unterkühlungstemperatur <= 5°C automatisch zugeordnet",
                    'fehlercode' => 'TA-11004',
                    'details' => null
                ];
            }
            // Unterkühlungstemperatur > 18°C
            else if ($istWert > 18) {
                // Direkter Fallback für hohe Unterkühlungstemperatur
                Log::info('Direkter TA-11005 Fallback für UK > 18°C');
                return [
                    'success' => true,
                    'message' => "Fehlercode für Unterkühlungstemperatur > 18°C automatisch zugeordnet",
                    'fehlercode' => 'TA-11005',
                    'details' => null
                ];
            }
        }
        
        return null;
    }
} 