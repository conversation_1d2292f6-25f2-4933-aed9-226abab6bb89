<?php

namespace App\Services;

use App\Mail\SpektrometerDatenMail;
use App\Models\EmailVerteiler;
use Illuminate\Support\Facades\Mail;

class EmailService
{
    public function sendSpektrometerEmails(array $spektrometerDaten, string $abteilung, int $rotekarteId): void
    {
        $emailType = match($abteilung) {
            'NG' => 'kg',     // Kleinguss
            'HF' => 'hf',     // Handformerei
            'GG' => 'qs_gg',  // Grossguss
            default => null,
        };

        if (!$emailType) {
            return;
        }

        $emails = EmailVerteiler::where('type', $emailType)->get();

        if ($emails->isEmpty()) {
            return;
        }

        $mail = new SpektrometerDatenMail($spektrometerDaten, $abteilung, $rotekarteId);

        // Separate TO and CC recipients
        $toRecipients = $emails->where('is_cc', false)->pluck('email')->toArray();
        $ccRecipients = $emails->where('is_cc', true)->pluck('email')->toArray();

        if (!empty($toRecipients)) {
            Mail::to($toRecipients)
                ->cc($ccRecipients)
                ->send($mail);
        }
    }
}
