<?php

namespace App\Services;

use App\Models\Pruefkatalog;
use Illuminate\Support\Facades\Log;

class PruefkatalogMigrationService
{
    /**
     * Migrate all existing Pruefkatalog entries to the new structured format
     *
     * @return array Statistics about the migration
     */
    public function migrateExistingData(): array
    {
        $total = 0;
        $success = 0;
        $failed = 0;
        $stats = [
            'types' => [],
        ];

        $pruefkatalogs = Pruefkatalog::all();
        
        foreach ($pruefkatalogs as $pruefkatalog) {
            $total++;
            
            try {
                // Using the model's parser to populate structured fields
                $absolutwerte = $pruefkatalog->absolutwerte;
                $parsed = $pruefkatalog->parseAbsolutwerte($absolutwerte);
                
                if ($parsed) {
                    $pruefkatalog->grenzwert_typ = $parsed['typ'];
                    $pruefkatalog->grenzwert_richtung = $parsed['richtung'];
                    $pruefkatalog->grenzwert_operator = $parsed['operator'];
                    $pruefkatalog->grenzwert_wert = $parsed['wert'];
                    $pruefkatalog->grenzwert_einheit = $parsed['einheit'];
                    $pruefkatalog->grenzwerte_json = $parsed;
                    
                    $pruefkatalog->save();
                    
                    // Track statistics
                    if (!isset($stats['types'][$parsed['typ']])) {
                        $stats['types'][$parsed['typ']] = 0;
                    }
                    $stats['types'][$parsed['typ']]++;
                    
                    $success++;
                } else {
                    Log::warning('Failed to parse absolutwerte', [
                        'fehlercode' => $pruefkatalog->fehlercode,
                        'absolutwerte' => $absolutwerte
                    ]);
                    $failed++;
                }
            } catch (\Exception $e) {
                Log::error('Error migrating pruefkatalog', [
                    'fehlercode' => $pruefkatalog->fehlercode,
                    'error' => $e->getMessage()
                ]);
                $failed++;
            }
        }
        
        return [
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
            'stats' => $stats
        ];
    }
} 