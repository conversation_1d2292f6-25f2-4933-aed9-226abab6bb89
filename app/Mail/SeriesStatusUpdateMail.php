<?php

namespace App\Mail;

use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SeriesStatusUpdateMail extends Mailable
{
    use Queueable, SerializesModels;

    public $rotekarte;

    /**
     * Create a new message instance.
     */
    public function __construct(Rotekarte $rotekarte)
    {
        $this->rotekarte = $rotekarte;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Serien-Status Änderung - Rotekarte Nr. ' . $this->rotekarte->id)
                    ->view('emails.series-status-update');
    }
}
