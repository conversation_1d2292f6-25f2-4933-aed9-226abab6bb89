<?php

namespace App\Mail;

use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;
use Barryvdh\DomPDF\Facade\Pdf;

class RotekartePdfMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Rotekarte $rotekarte
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Analysenabweichung - Rotekarte Nr. ' . $this->rotekarte->id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.rotekarte',
            with: [
                'rotekarte' => $this->rotekarte,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $pdf = PDF::loadView('pdf.rotekarte-analyse', ['rotekarte' => $this->rotekarte]);

        return [
            Attachment::fromData(
                fn () => $pdf->output(),
                'analysenabweichung.pdf'
            )
            ->withMime('application/pdf'),
        ];
    }
}
