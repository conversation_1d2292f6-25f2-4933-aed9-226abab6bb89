<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SpektrometerDatenMail extends Mailable
{
    use Queueable, SerializesModels;

    public $spektrometerDaten;
    public $abteilung;
    public $rotekarteId;

    public function __construct($spektrometerDaten, string $abteilung, int $rotekarteId)
    {
        $this->spektrometerDaten = $spektrometerDaten;
        $this->abteilung = $abteilung;
        $this->rotekarteId = $rotekarteId;
    }

    public function envelope(): Envelope
    {
        $abteilungName = match($this->abteilung) {
            'NG' => 'Kleinguss',
            'HF' => 'Handformerei',
            'GG' => 'Grossguss',
            default => $this->abteilung,
        };

        return new Envelope(
            subject: "Neue Spektrometer Daten für {$abteilungName}",
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.spektrometer-daten',
        );
    }
}
