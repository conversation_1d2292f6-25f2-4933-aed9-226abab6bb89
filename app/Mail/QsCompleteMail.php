<?php

namespace App\Mail;

use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class QsCompleteMail extends Mailable
{
    use Queueable, SerializesModels;

    public $rotekarte;

    public function __construct(Rotekarte $rotekarte)
    {
        $this->rotekarte = $rotekarte;
    }

    public function build()
    {
        return $this->subject('QS Prüfung abgeschlossen - Entscheidung erforderlich')
                    ->view('emails.qs-complete');
    }
}
