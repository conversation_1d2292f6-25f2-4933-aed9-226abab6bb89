<?php

namespace App\Mail;

use App\Models\Spektrometer;
use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SpektrometerNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $spektrometer;
    public $rotekarte;

    /**
     * Create a new message instance.
     */
    public function __construct(Spektrometer $spektrometer, Rotekarte $rotekarte)
    {
        $this->spektrometer = $spektrometer;
        $this->rotekarte = $rotekarte;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        $data = json_decode($this->spektrometer->data, true);

        return $this->subject('Neue Spektrometer-Analyse - ' . ($data['chargennummer'] ?? 'Unbekannt'))
            ->markdown('emails.spektrometer-notification', [
                'spektrometer' => $this->spektrometer,
                'rotekarte' => $this->rotekarte,
                'data' => $data
            ]);
    }
}
