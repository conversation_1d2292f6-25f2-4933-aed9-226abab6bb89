<?php

namespace App\Mail;

use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SpektrometerCompleteMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Rotekarte $rotekarte
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Neue Analysenabweichung - Formanlage Dokumentation erforderlich',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.spektrometer-complete',
            with: [
                'rotekarte' => $this->rotekarte,
                'formanlageUrl' => route('formanlage.index', ['rotekarte' => $this->rotekarte->id]),
                'gussnachbehandlungUrl' => route('gussnachbehandlung.index', ['rotekarte' => $this->rotekarte->id]),
                'qsUrl' => route('qs.index', ['rotekarte' => $this->rotekarte->id])
            ],
        );
    }
}
