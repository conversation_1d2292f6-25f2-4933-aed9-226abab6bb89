<?php

namespace App\Mail;

use App\Models\Rotekarte;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class FormanlagCompleteMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Rotekarte $rotekarte
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Neue Rotekarte Nr. ' . $this->rotekarte->id . ' - Formanlage abgeschlossen'
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.formanlage-complete',
            with: [
                'rotekarte' => $this->rotekarte,
                'gussnachbehandlungUrl' => route('gussnachbehandlung.index', ['rotekarte' => $this->rotekarte->id]),
                'qsUrl' => route('qs.index', ['rotekarte' => $this->rotekarte->id])
            ]
        );
    }

    public function attachments(): array
    {
        return [
            public_path('storage/rotekarten/rotekarte-' . $this->rotekarte->id . '.pdf')
        ];
    }
}
