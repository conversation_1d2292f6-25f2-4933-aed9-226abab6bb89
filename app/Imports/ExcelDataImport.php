<?php

namespace App\Imports;

use App\Models\ExcelData;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ExcelDataImport implements ToArray, WithHeadingRow
{
    protected $filename;

    public function __construct(string $filename)
    {
        $this->filename = $filename;
    }

    public function array(array $rows)
    {
        // Speichere oder aktualisiere die Daten
        ExcelData::updateOrCreate(
            ['filename' => $this->filename],
            [
                'data' => json_encode($rows),
                'last_sync' => now()
            ]
        );
    }
}
