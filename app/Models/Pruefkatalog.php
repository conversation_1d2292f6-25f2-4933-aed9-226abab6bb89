<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Pruefkatalog extends Model
{
    protected $table = 'pruefkatalogs';

    protected $fillable = [
        'fehlercode',
        'eisenmarke',
        'element',
        'absolutwerte',
        'teilesperren',
        'massnahmen',
        'bemerkungen',
        'grenzwert_typ',
        'grenzwert_richtung',
        'grenzwert_operator',
        'grenzwert_wert',
        'grenzwert_einheit',
        'grenzwerte_json',
    ];

    protected $casts = [
        'teilesperren' => 'boolean',
        'grenzwert_wert' => 'float',
        'grenzwerte_json' => 'array',
    ];
    
    /**
     * Parse the text-based absolutwerte to structured grenzwerte format
     * 
     * @param string $value The absolutwerte string value
     * @return array|null The parsed structured data
     */
    public function parseAbsolutwerte(string $value): ?array
    {
        // Handle "Nicht analysierbar" special case
        if ($value === 'Probe nicht analysierbar') {
            return [
                'typ' => 'SPECIAL',
                'richtung' => null,
                'operator' => null,
                'wert' => null,
                'einheit' => null,
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Komplexe Bedingungen mit "oder"-Verknüpfungen - exakte Muster für die verbleibenden Fälle
        if (in_array($value, [
            'OGW + ≥ 0,002% oder OGW + 0,001% und Mg ≤ 0,040%',
            'OGW + ≥ 0,002%, oder OGW + 0,001% und Mg ≤ 0,040%',
            'OGW + ≥ 0,005%, oder OGW + 0,001% und Mg ≤ 0,040%'
        ])) {
            // Extrahiere alle Zahlen
            preg_match_all('/[\d,\.]+/', $value, $matches);
            $numbers = $matches[0];
            
            // Die erste Zahl ist der Hauptwert, die zweite der alternative Wert, die dritte der Mg-Wert
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => '>=',
                'wert' => (float)str_replace(',', '.', $numbers[0]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null,
                'alternative_bedingung' => [
                    'typ' => 'OGW',
                    'richtung' => '+',
                    'operator' => null,
                    'wert' => (float)str_replace(',', '.', $numbers[1]),
                    'einheit' => '%',
                    'und_bedingung' => [
                        'element' => 'Mg',
                        'operator' => '<=',
                        'wert' => (float)str_replace(',', '.', $numbers[2]),
                        'einheit' => '%'
                    ]
                ]
            ];
        }

        // Spezialfall: Zwei "Unter"-Bedingungen für verschiedene Eisenmarken
        if (preg_match('/^Unter\s+([\d,\.]+)(?:\s*%)?\s+EM\s+(\d+)\s+Unter\s+([\d,\.]+)(?:\s*%)?$/i', $value, $matches)) {
            return [
                'typ' => 'ABSOLUTE',
                'richtung' => '-',
                'operator' => '<',
                'wert' => (float)str_replace(',', '.', $matches[1]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null,
                'eisenmarke_bedingung' => [
                    'eisenmarke' => $matches[2],
                    'operator' => '<',
                    'wert' => (float)str_replace(',', '.', $matches[3]),
                    'einheit' => '%'
                ]
            ];
        }

        // Spezialfall "Steuerung mit thermischer Analyse"
        if (preg_match('/^Steuerung\s+mit\s+thermischer\s+Analyse$/i', $value)) {
            return [
                'typ' => 'SPECIAL',
                'richtung' => null,
                'operator' => null,
                'wert' => null,
                'einheit' => null,
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Ungewöhnliche Operatorschreibweisen: "Unter X%" 
        if (preg_match('/^Unter\s+([\d,\.]+)(?:\s*%)?$/i', $value, $matches)) {
            return [
                'typ' => 'ABSOLUTE',
                'richtung' => '-',
                'operator' => '<',
                'wert' => (float)str_replace(',', '.', $matches[1]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Komplexe Muster mit Freigabebedingung (z.B. "OGW + ≤ 0,001% Giessfreigabe wenn Mg > 0,040%")
        if (preg_match('/^(OGW\s*[\+\＋])\s*([<>]=?|≤|≥)?\s*([\d,\.]+)(?:\s*%)?\s*Giessfreigabe\s*wenn\s*(\w+)\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%)?.*$/i', $value, $matches)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => $matches[2] ? $this->normalizeOperator($matches[2]) : null,
                'wert' => (float)str_replace(',', '.', $matches[3]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => [
                    'element' => $matches[4],
                    'operator' => $this->normalizeOperator($matches[5]),
                    'wert' => (float)str_replace(',', '.', $matches[6]),
                    'einheit' => '%'
                ]
            ];
        }

        // Einfaches Format für direkte Vergleiche (z.B. "> 0,05%")
        if (preg_match('/^([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%)?$/', $value, $matches)) {
            return [
                'typ' => 'ABSOLUTE',
                'richtung' => null,
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // UGW mit Operator (z.B. "UGW - ≥ 0,11%")
        if (preg_match('/^UGW\s*[-–—−﹣－\-]\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%)?$/', $value, $matches)) {
            return [
                'typ' => 'UGW',
                'richtung' => '-',
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // OGW mit Operator (z.B. "OGW + ≥ 0,04%")
        if (preg_match('/^OGW\s*[\+\＋]\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%)?$/', $value, $matches)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Einfaches UGW-Muster (z.B. "UGW - 0,11%")
        if (preg_match('/^UGW\s*[-–—−﹣－\-]\s*([\d,\.]+)(?:\s*%)?$/', $value, $matches)) {
            return [
                'typ' => 'UGW',
                'richtung' => '-',
                'operator' => null,
                'wert' => (float)str_replace(',', '.', $matches[1]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Einfaches OGW-Muster (z.B. "OGW + 0,06%")
        if (preg_match('/^OGW\s*[\+\＋]\s*([\d,\.]+)(?:\s*%)?$/', $value, $matches)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => null,
                'wert' => (float)str_replace(',', '.', $matches[1]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // "Unterschreitung UGW" pattern
        if (preg_match('/^Unterschreitung\s+UGW$/i', $value)) {
            return [
                'typ' => 'UGW',
                'richtung' => '-',
                'operator' => null,
                'wert' => null,
                'einheit' => null,
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // "Überschreitung OGW" pattern
        if (preg_match('/^Überschreitung\s+OGW$/i', $value)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => null,
                'wert' => null,
                'einheit' => null,
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Weitere "oder"-Bedingungen mit anderen Werten
        if (preg_match('/^OGW\s*[\+\＋]\s*(?:[≥>=])?\s*([\d,\.]+)(?:\s*%)?,?\s+(?:oder|OR)\s+OGW\s*[\+\＋]\s*([\d,\.]+)(?:\s*%)?\s+und\s+Mg\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%)?$/i', $value, $matches)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => '>=',
                'wert' => (float)str_replace(',', '.', $matches[1]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null,
                'alternative_bedingung' => [
                    'typ' => 'OGW',
                    'richtung' => '+',
                    'operator' => null,
                    'wert' => (float)str_replace(',', '.', $matches[2]),
                    'einheit' => '%',
                    'und_bedingung' => [
                        'element' => 'Mg',
                        'operator' => $this->normalizeOperator($matches[3]),
                        'wert' => (float)str_replace(',', '.', $matches[4]),
                        'einheit' => '%'
                    ]
                ]
            ];
        }

        // Doppelung des %-Zeichens
        if (preg_match('/^OGW\s*[\+\＋]\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*%+)$/i', $value, $matches)) {
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Temperaturbezogene Werte
        if (preg_match('/^Liquidustemperatur\s+([<>]=?|≤|≥)?\s*([\d,\.]+)(?:\s*°C)$/i', $value, $matches)) {
            return [
                'typ' => 'ABSOLUTE',
                'richtung' => null,
                'operator' => $this->normalizeOperator($matches[1] ?? '>'),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '°C',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Temperaturbezogene UGW-Werte
        if (preg_match('/^Liquidustemperatur\s+UGW\s*[-–—−﹣－\-]\s*([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*°C)$/i', $value, $matches)) {
            return [
                'typ' => 'UGW',
                'richtung' => '-',
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '°C',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Unterkühlung
        if (preg_match('/^Unter\s*kühlung\s+([<>]=?|≤|≥)\s*([\d,\.]+)(?:\s*°C)$/i', $value, $matches)) {
            return [
                'typ' => 'ABSOLUTE',
                'richtung' => null,
                'operator' => $this->normalizeOperator($matches[1]),
                'wert' => (float)str_replace(',', '.', $matches[2]),
                'einheit' => '°C',
                'text' => $value,
                'freigabe_bedingung' => null
            ];
        }

        // Spezialfälle für die letzten drei verbleibenden Einträge
        if (in_array($value, [
            'OGW + ≥ 0,003%, oder OGW + 0,001% und Mg ≤ 0,040%',
            'OGW + ≥ 0,006%, oder OGW + 0,001% und Mg ≤ 0,040%'
        ])) {
            // Extrahiere alle Zahlen
            preg_match_all('/[\d,\.]+/', $value, $matches);
            $numbers = $matches[0];
            
            // Die erste Zahl ist der Hauptwert, die zweite der alternative Wert, die dritte der Mg-Wert
            return [
                'typ' => 'OGW',
                'richtung' => '+',
                'operator' => '>=',
                'wert' => (float)str_replace(',', '.', $numbers[0]),
                'einheit' => '%',
                'text' => $value,
                'freigabe_bedingung' => null,
                'alternative_bedingung' => [
                    'typ' => 'OGW',
                    'richtung' => '+',
                    'operator' => null,
                    'wert' => (float)str_replace(',', '.', $numbers[1]),
                    'einheit' => '%',
                    'und_bedingung' => [
                        'element' => 'Mg',
                        'operator' => '<=',
                        'wert' => (float)str_replace(',', '.', $numbers[2]),
                        'einheit' => '%'
                    ]
                ]
            ];
        }

        // Default return für nicht erkannte Muster
        return [
            'typ' => 'TEXT',
            'richtung' => null,
            'operator' => null,
            'wert' => null,
            'einheit' => null,
            'text' => $value,
            'freigabe_bedingung' => null
        ];
    }

    /**
     * Normalisiert verschiedene Operator-Schreibweisen
     */
    private function normalizeOperator(string $operator): string
    {
        $operator = trim($operator);
        return match($operator) {
            '≥' => '>=',
            '≤' => '<=',
            'OGW +' => '<=',  // Spezialfall für "OGW + ≤"
            default => $operator
        };
    }
    
    /**
     * Set the absolutwerte attribute and populate structured fields
     *
     * @param string $value
     * @return void
     */
    public function setAbsolutwerteAttribute($value)
    {
        $this->attributes['absolutwerte'] = $value;
        
        // Parse the value into structured fields
        $parsed = $this->parseAbsolutwerte($value);
        
        if ($parsed) {
            $this->attributes['grenzwert_typ'] = $parsed['typ'];
            $this->attributes['grenzwert_richtung'] = $parsed['richtung'];
            $this->attributes['grenzwert_operator'] = $parsed['operator'];
            $this->attributes['grenzwert_wert'] = $parsed['wert'];
            $this->attributes['grenzwert_einheit'] = $parsed['einheit'];
            $this->attributes['grenzwerte_json'] = json_encode($parsed);
            
            // Log zur Fehlersuche
            \Illuminate\Support\Facades\Log::debug('Absolutwerte wurden geparst und gesetzt', [
                'absolutwerte' => $value,
                'parsed' => $parsed,
                'json' => json_encode($parsed)
            ]);
        }
    }
    
    /**
     * Ensure the grenzwerte_json field is synchronized with the individual fields
     * 
     * @param array $attributes
     * @return bool
     */
    public function update(array $attributes = [], array $options = [])
    {
        // Check if any of the structured fields are being updated but not the json field
        $structuredFieldsUpdated = array_key_exists('grenzwert_typ', $attributes) ||
                                 array_key_exists('grenzwert_richtung', $attributes) ||
                                 array_key_exists('grenzwert_operator', $attributes) ||
                                 array_key_exists('grenzwert_wert', $attributes) ||
                                 array_key_exists('grenzwert_einheit', $attributes);
        
        $jsonUpdated = array_key_exists('grenzwerte_json', $attributes);
        
        // If structured fields are updated but not the JSON, update the JSON to match
        if ($structuredFieldsUpdated && !$jsonUpdated) {
            $attributes['grenzwerte_json'] = json_encode([
                'typ' => $attributes['grenzwert_typ'] ?? $this->grenzwert_typ ?? 'TEXT',
                'richtung' => $attributes['grenzwert_richtung'] ?? $this->grenzwert_richtung,
                'operator' => $attributes['grenzwert_operator'] ?? $this->grenzwert_operator,
                'wert' => $attributes['grenzwert_wert'] ?? $this->grenzwert_wert,
                'einheit' => $attributes['grenzwert_einheit'] ?? $this->grenzwert_einheit,
                'text' => $attributes['absolutwerte'] ?? $this->absolutwerte
            ]);
        }
        
        // Wenn hingegen das JSON-Feld aktualisiert wird ohne die strukturierten Felder,
        // sollten wir die strukturierten Felder aus dem JSON aktualisieren
        if ($jsonUpdated && !$structuredFieldsUpdated && is_string($attributes['grenzwerte_json'])) {
            $json = json_decode($attributes['grenzwerte_json'], true);
            if (is_array($json)) {
                if (isset($json['typ'])) $attributes['grenzwert_typ'] = $json['typ'];
                if (isset($json['richtung'])) $attributes['grenzwert_richtung'] = $json['richtung'];
                if (isset($json['operator'])) $attributes['grenzwert_operator'] = $json['operator'];
                if (isset($json['wert'])) $attributes['grenzwert_wert'] = $json['wert'];
                if (isset($json['einheit'])) $attributes['grenzwert_einheit'] = $json['einheit'];
            }
        }
        
        return parent::update($attributes, $options);
    }
}
