<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Rotekarte extends Model
{
    use HasFactory;

    protected $table = 'rotekarten';

    protected $fillable = [
        'status',
        'type',
        'spektrometer_daten',
        'formanlage_daten',
        'gussnachbehandlung_daten',
        'guss_daten',
        'qs_daten'
    ];

    protected $casts = [
        'spektrometer_daten' => 'array',
        'formanlage_daten' => 'array',
        'gussnachbehandlung_daten' => 'array',
        'guss_daten' => 'array',
        'qs_daten' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the spektrometer data as an accessor
     */
    public function getSpektrometerDatenAttribute($value)
    {
        return json_decode($value, true);
    }

    /**
     * Set the spektrometer data as a mutator
     */
    public function setSpektrometerDatenAttribute($value)
    {
        $this->attributes['spektrometer_daten'] = json_encode($value);
    }
}
