<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Sollvorgaben extends Model
{
    protected $table = 'sollvorgaben';

    protected $fillable = ['data'];

    protected $casts = [
        'data' => 'array',
    ];

    public static function findByEisenmarke(string $eisenmarke, string $abteilung)
    {
        $jsonPath = $abteilung === 'HF' ? '$.EM_HF' : '$.EM';
        return static::whereRaw('UPPER(JSON_UNQUOTE(JSON_EXTRACT(data, ?))) = ?', [$jsonPath, strtoupper($eisenmarke)])->first();
    }
}
