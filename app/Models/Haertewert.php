<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Haertewert extends Model
{
    use HasFactory;

    protected $table = 'haertewerts';

    protected $fillable = [
        'auftrag_id',
        'teilenummer',
        'auftragsnummer',
        'chargenummer',
        'nestnummer',
        'eisenmarke',
        'soll_von',
        'soll_bis',
        'ist_wert',
        'pruefposition',
        'pruefverfahren',
        'geprueft_von',
        'bemerkung',
        'wanddicke',
    ];

    protected $casts = [
        'soll_von' => 'float',
        'soll_bis' => 'float',
        'ist_wert' => 'float',
        'nestnummer' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public const PRUEFPOSITIONEN = [
        'UT' => 'UT',
        'OT' => 'OT',
        'Stehend' => 'Stehend',
        '<PERSON>ugstrebe' => 'Zugstrebe',
        'Zahnkranz' => 'Zahnkranz',
        'Andere' => 'Andere',
    ];

    public const PRUEFVERFAHREN = [
        'Brinellhärte' => 'Brinellhärte',
        'Scherkraft-Härteprüfer' => 'Scherkraft-Härteprüfer',
        'Optisches Auslesen' => 'Optisches Auslesen',
    ];

    /**
     * Get the order that this hardness value belongs to
     */
    public function auftrag(): BelongsTo
    {
        return $this->belongsTo(HaertewertAuftrag::class, 'auftrag_id');
    }
}
