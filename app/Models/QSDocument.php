<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QSDocument extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'qs_documents';

    protected $fillable = [
        'rotekarte_id',
        'filename',
        'original_filename',
        'path',
        'mime_type',
        'file_type',
        'file_size',
        'category',
        'description',
        'user_id',
    ];

    protected $casts = [
        'file_size' => 'integer',
    ];

    public function rotekarte()
    {
        return $this->belongsTo(Rotekarte::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getFullPathAttribute()
    {
        return storage_path('app/' . $this->path);
    }

    public function isImage()
    {
        return $this->file_type === 'image';
    }

    public function isDocument()
    {
        return $this->file_type === 'document';
    }
}
