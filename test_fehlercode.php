<?php
require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Test der FehlercodeSuggestService-Klasse
$service = app()->make(App\Services\FehlercodeSuggestService::class);

// Test mit Eisenmarke 405 und Mg-Wert 0,065
$eisenmarke = '405';
$analysewerte = [
    [
        'element' => 'Mg',
        'istWert' => '0,065',
        'sollWert' => '0,038 - 0,055'
    ]
];

// Ergebnis der Fehlercode-Zuordnung anzeigen
$result = $service->suggestFehlercode($eisenmarke, $analysewerte);

echo "\n=== Ergebnis der Fehlercode-Zuordnung ===\n";
echo "Erfolg: " . ($result['success'] ? 'Ja' : 'Nein') . "\n";
echo "Nachricht: " . ($result['message'] ?? '<PERSON><PERSON>') . "\n";
echo "Fehlercode: " . ($result['fehlercode'] ?? 'Kein <PERSON>hlercode') . "\n";
if (isset($result['details'])) {
    echo "Fehlercode-Details:\n";
    echo "  Element: " . $result['details']['element'] . "\n";
    echo "  Absolutwerte: " . $result['details']['absolutwerte'] . "\n";
    echo "  Teilesperren: " . ($result['details']['teilesperren'] ? 'Ja' : 'Nein') . "\n";
    echo "  Maßnahmen: " . $result['details']['massnahmen'] . "\n";
}
echo "=== Ende des Tests ===\n";
