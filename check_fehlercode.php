<?php
require 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Pruefkatalog;
use Illuminate\Support\Facades\DB;

// Fehlercode prüfen
$code = Pruefkatalog::where('fehlercode', 'CA-81116')->first();

if ($code) {
    echo "Fehlercode gefunden:\n";
    echo "Fehlercode: {$code->fehlercode}\n";
    echo "Element: {$code->element}\n";
    echo "Eisenmarke: {$code->eisenmarke}\n";
    echo "Absolutwerte: {$code->absolutwerte}\n";
    echo "Typ: {$code->grenzwert_typ}\n";
    echo "Richtung: {$code->grenzwert_richtung}\n";
    echo "Operator: {$code->grenzwert_operator}\n";
    echo "Wert: {$code->grenzwert_wert}\n";
    echo "Einheit: {$code->grenzwert_einheit}\n";
    
    // JSON-Struktur anzeigen
    echo "JSON:\n";
    print_r($code->grenzwerte_json);
    echo "\n\n";
} else {
    echo "Fehlercode CA-81116 nicht gefunden!\n\n";
}

// Alle Fehlercodes für Cu und Eisenmarke 811 prüfen
echo "Alle Fehlercodes für Cu und Eisenmarke 811:\n";
$codes = Pruefkatalog::where('element', 'Cu')
    ->where('eisenmarke', '811')
    ->get();

foreach ($codes as $code) {
    echo "{$code->fehlercode} | {$code->absolutwerte} | Typ: {$code->grenzwert_typ} | ";
    echo "Richtung: {$code->grenzwert_richtung} | Operator: {$code->grenzwert_operator} | ";
    echo "Wert: {$code->grenzwert_wert}\n";
}

echo "\n";

// Simulation der Fehlercodezuordnung für Cu=0.794 und Eisenmarke 811
echo "Simulation für Cu=0.794 und Eisenmarke 811:\n";
$analysewert = 0.794;
$eisenmarke = '811';
$element = 'Cu';

// Logik zur Fehlercodezuordnung basierend auf den Absolutwerten und Operatoren
$matchingCodes = Pruefkatalog::where('element', $element)
    ->where('eisenmarke', $eisenmarke)
    ->get();

$foundMatch = false;
foreach ($matchingCodes as $code) {
    $match = false;
    
    // Prüfen ob der Wert mit den Grenzwerten übereinstimmt
    if ($code->grenzwert_operator && $code->grenzwert_wert !== null) {
        switch ($code->grenzwert_operator) {
            case '<':
                $match = $analysewert < $code->grenzwert_wert;
                break;
            case '<=':
                $match = $analysewert <= $code->grenzwert_wert;
                break;
            case '>':
                $match = $analysewert > $code->grenzwert_wert;
                break;
            case '>=':
                $match = $analysewert >= $code->grenzwert_wert;
                break;
        }
    } else if ($code->grenzwert_typ === 'UGW' && $code->grenzwert_richtung === '-') {
        // Für UGW ohne Operator: Wert unter Grenzwert prüfen
        if ($code->grenzwert_wert !== null) {
            $match = $analysewert < $code->grenzwert_wert;
        }
    } else if ($code->grenzwert_typ === 'OGW' && $code->grenzwert_richtung === '+') {
        // Für OGW ohne Operator: Wert über Grenzwert prüfen
        if ($code->grenzwert_wert !== null) {
            $match = $analysewert > $code->grenzwert_wert;
        }
    }
    
    if ($match) {
        echo "MATCH: {$code->fehlercode} | {$code->absolutwerte}\n";
        echo "Grund: Analysewert $analysewert {$code->grenzwert_operator} {$code->grenzwert_wert}\n";
        $foundMatch = true;
    }
}

if (!$foundMatch) {
    echo "Kein passender Fehlercode gefunden! Mögliche Gründe:\n";
    
    // Sollwertbereich prüfen
    $sollwertMin = 1.0;
    $sollwertMax = 1.1;
    if ($analysewert < $sollwertMin) {
        $abweichung = $analysewert - $sollwertMin;
        echo "Analysewert $analysewert ist unter dem Mindestsollwert $sollwertMin (Abweichung: $abweichung)\n";
    } else if ($analysewert > $sollwertMax) {
        $abweichung = $analysewert - $sollwertMax;
        echo "Analysewert $analysewert ist über dem Maximalsollwert $sollwertMax (Abweichung: $abweichung)\n";
    } else {
        echo "Analysewert $analysewert liegt innerhalb des Sollwertbereichs $sollwertMin - $sollwertMax\n";
    }
} 