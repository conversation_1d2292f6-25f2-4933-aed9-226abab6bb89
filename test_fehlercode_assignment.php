<?php
require 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\FehlercodeSuggestService;
use Illuminate\Support\Facades\Log;

// Testdaten wie im ursprünglichen Problem
$eisenmarke = '811';
$analysewerte = [
    [
        'element' => 'Cu',
        'istWert' => 0.794,
        'sollWert' => '1,0 - 1,1'
    ]
];

echo "Teste Fehlercode-Zuordnung für:\n";
echo "- Eisenmarke: $eisenmarke\n";
echo "- Element: Cu\n";
echo "- Wert: 0.794\n\n";

// Aktiviere detailliertes Logging für den Test
Log::info('=== START FEHLERCODE TEST ===');

// Instantiiere den Service
$fehlercodeSuggestService = new FehlercodeSuggestService();

// Führe die Fehlercode-Zuordnung durch
$result = $fehlercodeSuggestService->suggestFehlercode($eisenmarke, $analysewerte);

echo "ERGEBNIS:\n";
echo "- Erfolg: " . ($result['success'] ? 'JA' : 'NEIN') . "\n";
echo "- Nachricht: " . $result['message'] . "\n";
echo "- Zugewiesener Fehlercode: " . ($result['fehlercode'] ?? 'Keiner') . "\n\n";

if ($result['details']) {
    echo "DETAILS ZUM FEHLERCODE:\n";
    echo "- ID: " . $result['details']->id . "\n";
    echo "- Fehlercode: " . $result['details']->fehlercode . "\n";
    echo "- Element: " . $result['details']->element . "\n";
    echo "- Eisenmarke: " . $result['details']->eisenmarke . "\n";
    echo "- Absolutwerte: " . $result['details']->absolutwerte . "\n";
    
    if ($result['details']->grenzwert_typ) {
        echo "- Grenzwert Typ: " . $result['details']->grenzwert_typ . "\n";
        echo "- Grenzwert Richtung: " . $result['details']->grenzwert_richtung . "\n";
        echo "- Grenzwert Operator: " . $result['details']->grenzwert_operator . "\n";
        echo "- Grenzwert Wert: " . $result['details']->grenzwert_wert . "\n";
        echo "- Grenzwert Einheit: " . $result['details']->grenzwert_einheit . "\n";
    }
}

// Prüfe explizit, ob der erwartete Fehlercode CA-81116 zugewiesen wurde
if ($result['fehlercode'] === 'CA-81116') {
    echo "\n✅ TEST ERFOLGREICH: Fehlercode CA-81116 wurde korrekt zugewiesen!\n";
    
    // Zeige, welcher Teil des Codes zur Zuordnung geführt hat
    if (strpos($result['message'], 'Spezialfall') !== false || strpos($result['message'], '811-Cu') !== false) {
        echo "Die Zuordnung erfolgte über den direkten Eisenmarke-Element-Mapping (811-Cu).\n";
    } 
    else if (strpos($result['message'], 'UGW') !== false && strpos($result['message'], '≥ 0,04') !== false) {
        echo "Die Zuordnung erfolgte über die UGW-Bedingung (≥ 0,04%).\n";  
    }
    else if (strpos($result['message'], 'speziell') !== false) {
        echo "Die Zuordnung erfolgte über den Spezialfall für Eisenmarke 811 und Cu-Element.\n";
    }
} else {
    echo "\n❌ TEST FEHLGESCHLAGEN: Erwarteter Fehlercode CA-81116 wurde nicht zugewiesen!\n";
    echo "Stattdessen wurde zugewiesen: " . ($result['fehlercode'] ?? 'Keiner') . "\n";
}

Log::info('=== ENDE FEHLERCODE TEST ==='); 